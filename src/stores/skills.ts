import { defineStore } from 'pinia';
import type { QTableProps } from 'quasar';
import { useQuasar } from 'quasar';
import { useSkillsService } from 'src/services/skills/skills';
import type { Skill } from 'src/types/models';
import { ref } from 'vue';

export const useSkillStore = defineStore('skill', () => {
  const $q = useQuasar();
  const createDialog = ref(false);
  const dialogTitile = ref('');
  const selectedTap = ref('');
  const isPreview = ref(false);
  const skills = ref<Skill[]>([]);
  const skill = ref<Skill>({
    id: 0,
    name: '',
    dep_id: 0,
    evaluatorId: '0', // Changed to string
    competencyIds: [0],
    career_type: '',
    programId: 0,
    tracking: false,
  });
  const editedSkill = skill;

  async function fetchSkills(career_type: string, _pag: QTableProps['pagination']) {
    try {
      const res = await useSkillsService.getAll(_pag, career_type);
      skills.value = res.data;
    } catch (err: unknown) {
      let message = 'โหลด Skills ไม่สำเร็จ';
      if (err instanceof Error) {
        message = err.message;
      }
      $q.notify({ type: 'negative', message });
    }
  }

  async function fetchOne(id: number) {
    try {
      const res = await useSkillsService.fetchOne(id);
      editedSkill.value = res;
      //editedSkill competecies[] compete
      editedSkill.value.competencyIds =
        editedSkill.value.competencies
          ?.map((c) => c?.id)
          .filter((id): id is number => typeof id === 'number') ?? [];
    } catch (err: unknown) {
      let message = 'โหลด Skills ไม่สำเร็จ';
      if (err instanceof Error) {
        message = err.message;
      }
      $q.notify({ type: 'negative', message });
    }
  }

  async function addSkill(skill: Skill) {
    try {
      await useSkillsService.create(skill);
    } catch (err: unknown) {
      let message = 'สร้างทักษะไม่สำเร็จ';
      if (err instanceof Error) {
        message = err.message;
      }
      $q.notify({ type: 'negative', message });
    } finally {
      createDialog.value = false;
      $q.notify({
        message: 'สร้างทักษะใหม่สำเร็จ!',
        color: 'positive',
        icon: 'check_circle',
      });
    }
  }

  async function editSkill(updatedSkill: Skill) {
    try {
      if (updatedSkill.id) {
        await useSkillsService.update(updatedSkill.id, updatedSkill);
      }
    } catch (err: unknown) {
      let message = 'สร้างทักษะไม่สำเร็จ';
      if (err instanceof Error) {
        message = err.message;
      }
      $q.notify({ type: 'negative', message });
    } finally {
      createDialog.value = false;
      $q.notify({
        message: 'แก้ไขทักษะสำเร็จ!',
        color: 'positive',
        icon: 'check_circle',
      });
    }
  }

  function cleanValue() {
    editedSkill.value = {
      id: 0,
      name: '',
      dep_id: 0,
      evaluatorId: '0', // Changed to string
      competencyIds: [0],
      career_type: '',
      programId: 0,
      tracking: false,
    };
  }

  async function deleteSkill(skillId: number) {
    try {
      await useSkillsService.remove(skillId);
      cleanValue();
    } catch (err: unknown) {
      let message = 'ลบทักษะไม่สำเร็จ';
      if (err instanceof Error) {
        message = err.message;
      }
      $q.notify({ type: 'negative', message });
    }
  }
  return {
    //state
    createDialog,
    skills,
    editedSkill,
    dialogTitile,
    selectedTap,
    isPreview,

    //action
    fetchSkills,
    fetchOne,
    addSkill,
    editSkill,
    cleanValue,
    deleteSkill,
  };
});
