import { defineStore } from 'pinia';
import type { ItemBlock, Assessment } from 'src/types/models';
import type { DataResponse } from 'src/types/data';
import { ref, computed, nextTick } from 'vue';
import { AssessmentService } from 'src/services/asm/assessmentService';
import { useGlobalStore } from './global';

// Import utilities and composables
import { useUIRefresh } from 'src/composables/useUIRefresh';
import { useBlockCreator } from 'src/composables/useBlockCreator';
import { createDOMReferenceManager } from 'src/utils/domReferenceManager';
import { createTimeoutManager } from 'src/utils/fabHelper';
import {
  generateNextQuestionId,
  generateNextOptionId,
  resetIdCounters,
} from 'src/utils/idGenerator';
import {
  insertBlockWithSequenceManagement,
  addBlocksToEnd,
  updateOptionTextInBlocks,
  reorderBlocksAndUpdateSequences,
  removeBlockFromList,
  replaceBlockAtIndex,
  createDuplicateBlock,
  updateBlockSection,
} from 'src/utils/blockOperations';
import {
  calculateTotalSections,
  isSectionBlock,
  isSectionBlockById,
  getSectionNumber,
  getSectionNumberBySequence,
  getSectionNumberById,
} from 'src/utils/sectionManager';
import {
  isAnswerItemBlockType,
  validateAssessmentIds,
  validateBlockDeletion,
  validatePostDeletion,
} from 'src/utils/blockValidation';

// Constants
const FAB_POSITION_DEBOUNCE_DELAY = 50;
const FAB_LOCK_RELEASE_DELAY = 200;
const FAB_PROTECTION_DURATION = 200;

export const useBlockCreatorStore = defineStore('blockCreator', () => {
  // ===== Core State Management =====
  const blockList = ref<ItemBlock[]>([]);
  const currentSelectedBlock = ref<ItemBlock | null>(null);
  const currentSelectedBlockId = ref<string | undefined>();

  // ===== Assessment State =====
  const assessmentMetadata = ref<DataResponse<Assessment> | null>(null);
  const assessmentCollection = ref<Assessment[]>([]);
  const activeAssessment = ref<Assessment | null>(null);
  const isLoadingAssessment = ref(false);
  const assessmentError = ref<string | null>(null);
  const paginationPage = ref(1);
  const paginationLimit = ref(5);
  const searchQuery = ref('');

  // ===== UI State =====
  const showDuplicateDialog = ref(false);
  const isDragOperationActive = ref(false);
  const itemBlockPage = ref(1);
  const hasMoreBlocks = ref(true);

  // ===== FAB State =====
  const fabPositioningState = ref({
    isPositionLocked: false,
    pendingTargetPosition: null as number | null,
    isCreationInProgress: false,
    targetBlockId: null as number | null,
  });

  // ===== Utility Managers =====
  const domReferenceManager = createDOMReferenceManager();
  const fabTimeoutManager = createTimeoutManager();
  const { uiRefreshTrigger, executeBasicUIRefresh, synchronizeBlocksWithAssessment } = useUIRefresh();
  const { isBlockCreationInProgress, createNewBlockAfterIndex, createNewHeaderAfterIndex, createNewSection } = useBlockCreator();

  // ===== Computed Properties =====
  const isFabPositionLocked = computed({
    get: () => fabPositioningState.value.isPositionLocked,
    set: (value: boolean) => {
      fabPositioningState.value.isPositionLocked = value;
    },
  });

  const pendingFabTargetPosition = computed({
    get: () => fabPositioningState.value.pendingTargetPosition,
    set: (value: number | null) => {
      fabPositioningState.value.pendingTargetPosition = value;
    },
  });

  const isFabCreationInProgress = computed({
    get: () => fabPositioningState.value.isCreationInProgress,
    set: (value: boolean) => {
      fabPositioningState.value.isCreationInProgress = value;
    },
  });

  const fabTargetBlockId = computed({
    get: () => fabPositioningState.value.targetBlockId,
    set: (value: number | null) => {
      fabPositioningState.value.targetBlockId = value;
    },
  });

  const totalSections = computed(() => calculateTotalSections(blockList.value));

  // ===== Block Operations =====
  function addBlocks(newBlocks: ItemBlock[]) {
    blockList.value = addBlocksToEnd(blockList.value, newBlocks);
  }

  function insertBlockAfterIndex(newBlock: ItemBlock, targetIndex: number) {
    blockList.value = insertBlockWithSequenceManagement(blockList.value, newBlock, targetIndex);
  }

  function addBlockToEnd(newBlock: ItemBlock) {
    blockList.value = [...blockList.value, newBlock];
  }

  function updateBlocksOrder(reorderedBlocks: ItemBlock[]) {
    blockList.value = reorderBlocksAndUpdateSequences(reorderedBlocks);
    synchronizeAssessmentWithBlocks();
  }

  function setBlockSection(sectionNumber: number, blockIndex: number) {
    blockList.value = updateBlockSection(blockList.value, sectionNumber, blockIndex);
  }

  function updateBlock(updatedBlock: ItemBlock, targetIndex: number) {
    blockList.value = replaceBlockAtIndex(blockList.value, updatedBlock, targetIndex);
  }

  function deleteBlock(targetIndex: number) {
    const result = removeBlockFromList(blockList.value, targetIndex);
    blockList.value = result.updatedBlocks;
    return result.removedBlock;
  }

  function duplicateBlock(sourceBlock: ItemBlock, insertionIndex: number) {
    blockList.value = createDuplicateBlock(blockList.value, sourceBlock, insertionIndex);
    return blockList.value[insertionIndex + 1];
  }

  function updateOptionTextInStore(optionId: number, newText: string) {
    blockList.value = updateOptionTextInBlocks(blockList.value, optionId, newText);

    // Sync with activeAssessment
    if (activeAssessment.value?.itemBlocks) {
      activeAssessment.value.itemBlocks = updateOptionTextInBlocks(
        activeAssessment.value.itemBlocks,
        optionId,
        newText
      );
    }
  }

  // ===== Block Pagination =====
  async function loadMoreBlocks(assessmentId: number, type: 'quiz' | 'evaluate') {
    if (!hasMoreBlocks.value) return;

    const service = new AssessmentService(type);
    const nextPage = itemBlockPage.value + 1;
    const limit = 10;

    const { pagedItemBlocks } = await service.fetchOne(assessmentId, {
      page: nextPage,
      limit,
    });

    if (pagedItemBlocks.length < limit) {
      hasMoreBlocks.value = false;
    }

    itemBlockPage.value = nextPage;
    blockList.value = addBlocksToEnd(blockList.value, pagedItemBlocks);
  }

  // ===== FAB Position Management =====
  function updateFabPosition(blockId: number, shouldPositionImmediately = false) {
    if (isBlockCreationInProgress.value && !shouldPositionImmediately) {
      pendingFabTargetPosition.value = blockId;
      return;
    }

    fabTimeoutManager.clearFabTimeout();

    if (shouldPositionImmediately) {
      positionFabImmediately(blockId);
    } else {
      positionFabWithDebounce(blockId);
    }
  }

  function positionFabImmediately(blockId: number) {
    isFabPositionLocked.value = true;
    currentSelectedBlockId.value = `block-${blockId}`;
    fabTimeoutManager.clearLockTimeout();

    fabTimeoutManager.lockReleaseTimeout = setTimeout(() => {
      isFabPositionLocked.value = false;
      const pendingPosition = pendingFabTargetPosition.value;
      if (pendingPosition && pendingPosition !== blockId) {
        updateFabPosition(pendingPosition, false);
        pendingFabTargetPosition.value = null;
      }
    }, FAB_LOCK_RELEASE_DELAY);
  }

  function positionFabWithDebounce(blockId: number) {
    fabTimeoutManager.fabPositionTimeout = setTimeout(() => {
      if (!isFabPositionLocked.value) {
        currentSelectedBlockId.value = `block-${blockId}`;
      } else {
        pendingFabTargetPosition.value = blockId;
      }
    }, FAB_POSITION_DEBOUNCE_DELAY);
  }

  function createFabProtectionForBlock(blockId: number, duration = FAB_PROTECTION_DURATION) {
    isFabCreationInProgress.value = true;
    fabTargetBlockId.value = blockId;
    isFabPositionLocked.value = true;
    currentSelectedBlockId.value = `block-${blockId}`;

    fabTimeoutManager.clearLockTimeout();

    fabTimeoutManager.lockReleaseTimeout = setTimeout(() => {
      isFabPositionLocked.value = false;
      isFabCreationInProgress.value = false;
      fabTargetBlockId.value = null;
      currentSelectedBlockId.value = `block-${blockId}`;
    }, duration);
  }

  // ===== Scroll Utilities =====
  function scrollToSelectedBlock() {
    if (!currentSelectedBlockId.value) return;
    const blockId = Number(currentSelectedBlockId.value.split('-')[1]);
    const blockElement = domReferenceManager.getBlockDOMReference(blockId);
    if (blockElement && 'scrollIntoView' in blockElement) {
      (blockElement as HTMLElement).scrollIntoView({
        behavior: 'smooth',
        block: 'center',
      });
    }
  }

  async function positionFabAndScrollToBlock(blockId: number) {
    updateFabPosition(blockId, true);
    await nextTick();
    await nextTick();
    scrollToSelectedBlock();
  }

  // ===== Assessment Management =====
  async function fetchAssessmentById(id: number) {
    isLoadingAssessment.value = true;
    assessmentError.value = null;
    try {
      const { assessment, pagedItemBlocks } = await new AssessmentService('evaluate').fetchOne(id);
      if (assessment) {
        activeAssessment.value = assessment;
        if (pagedItemBlocks?.length) {
          blockList.value = pagedItemBlocks;
        }
      }
    } catch (err: unknown) {
      assessmentError.value = err instanceof Error ? err.message : 'Unable to load assessment';
    } finally {
      isLoadingAssessment.value = false;
    }
  }

  async function addAssessment(assessmentData: Partial<Assessment>): Promise<Assessment> {
    const response = await new AssessmentService('evaluate').createOne(assessmentData);
    assessmentCollection.value.push(response);
    activeAssessment.value = response;
    return response;
  }

  async function updateAssessment(id: number, assessmentData: Assessment): Promise<Assessment> {
    const response = await new AssessmentService('evaluate').updateOne(id, assessmentData);
    const index = assessmentCollection.value.findIndex((assessment) => assessment.id === id);
    if (index !== -1) assessmentCollection.value[index] = response;
    if (activeAssessment.value?.id === id) activeAssessment.value = response;
    return response;
  }

  async function removeAssessment(id: number): Promise<void> {
    await new AssessmentService('evaluate').deleteOne(id);
    assessmentCollection.value = assessmentCollection.value.filter(
      (assessment) => assessment.id !== id
    );
    if (activeAssessment.value?.id === id) {
      activeAssessment.value = null;
    }
  }

  // ===== Helper Functions =====
  function synchronizeAssessmentWithBlocks() {
    if (activeAssessment.value?.itemBlocks) {
      activeAssessment.value.itemBlocks = [...blockList.value];
    }
  }

  function getAssessmentId(): number | null {
    return activeAssessment.value?.id || null;
  }

  function getItemBlockById(id: number) {
    return activeAssessment.value?.itemBlocks?.find((block) => block.id === id) || null;
  }

  function getHeaderBlockId(): number | null {
    const headerBlock = activeAssessment.value?.itemBlocks?.find(
      (block) => block.type === 'HEADER'
    );
    return headerBlock?.id || null;
  }

  function getRadioBlockId(): number | null {
    const radioBlock = activeAssessment.value?.itemBlocks?.find((block) => block.type === 'RADIO');
    return radioBlock?.id || null;
  }

  function getAllItemBlockIds(): number[] {
    return activeAssessment.value?.itemBlocks?.map((block) => block.id) || [];
  }

  function initializeBlocks(initialBlocks: ItemBlock[]) {
    blockList.value = [...initialBlocks];
  }

  function resetBlocks(initialBlocks: ItemBlock[]) {
    blockList.value = initialBlocks;
  }

  function getAssessmentData() {
    return {
      blocks: blockList.value,
      totalBlocks: blockList.value.length,
      totalSections: totalSections.value,
    };
  }

  // ===== Cleanup =====
  function resetStore() {
    blockList.value = [];
    currentSelectedBlock.value = null;
    currentSelectedBlockId.value = undefined;
    assessmentMetadata.value = null;
    assessmentCollection.value = [];
    activeAssessment.value = null;
    isLoadingAssessment.value = false;
    assessmentError.value = null;
    paginationPage.value = 1;
    paginationLimit.value = 5;
    searchQuery.value = '';
    showDuplicateDialog.value = false;
    isDragOperationActive.value = false;
    itemBlockPage.value = 1;
    hasMoreBlocks.value = true;
    fabPositioningState.value = {
      isPositionLocked: false,
      pendingTargetPosition: null,
      isCreationInProgress: false,
      targetBlockId: null,
    };
    resetIdCounters();
    domReferenceManager.clearAllReferences();
    fabTimeoutManager.clearAll();
  }

  // ===== Sequence Backend Sync =====
  async function syncSequencesToBackend(assessmentType: 'quiz' | 'evaluate') {
    try {
      const validBlocks = blockList.value.filter((block) => {
        return block.id && !isNaN(Number(block.id)) && Number(block.id) > 0;
      });

      if (validBlocks.length === 0) {
        console.log('🔄 No blocks to sync sequences');
        return;
      }

      console.log('🔄 Syncing sequences to backend:', {
        blocksCount: validBlocks.length,
        sequences: validBlocks.map((b) => ({ id: b.id, sequence: b.sequence })),
      });

      const assessmentService = new AssessmentService(assessmentType);
      const result = await assessmentService.updateBlockSequences(validBlocks);

      if (result?.success) {
        console.log('✅ Sequence sync successful');
      } else {
        console.warn('⚠️ Unexpected sequence sync result:', result);
      }
    } catch (error) {
      console.error('❌ Failed to sync sequences to backend:', error);
    }
  }

  // ===== Block Duplication =====
  async function handleDuplicateHeaderBlock(
    sourceBlockId: number,
    assessmentType: 'quiz' | 'evaluate',
    assessmentId?: number | null
  ) {
    if (isBlockCreationInProgress.value) return;

    const sourceBlockIndex = blockList.value.findIndex((block) => block.id === sourceBlockId);
    if (sourceBlockIndex === -1) {
      console.error('❌ Source header block not found for duplication:', sourceBlockId);
      return;
    }

    const sourceBlock = blockList.value[sourceBlockIndex];
    if (!sourceBlock || sourceBlock.type !== 'HEADER') {
      console.error('❌ Source block is not a header block:', sourceBlock);
      return;
    }

    try {
      isBlockCreationInProgress.value = true;
      const globalStore = useGlobalStore();
      globalStore.startSaveOperation('Duplicating header...');

      const finalAssessmentId = assessmentId || getAssessmentId();
      if (!finalAssessmentId) {
        globalStore.completeSaveOperation(false, 'Assessment ID not found');
        return;
      }

      const assessmentService = new AssessmentService(assessmentType);
      const duplicatedBlock = await assessmentService.duplicateBlock(sourceBlockId, {
        assessmentId: finalAssessmentId,
        sequence: sourceBlock.sequence + 1,
        section: sourceBlock.section,
      });

      if (duplicatedBlock) {
        blockList.value = insertBlockWithSequenceManagement(blockList.value, duplicatedBlock, sourceBlockIndex);
        await syncSequencesToBackend(assessmentType);
        synchronizeAssessmentWithBlocks();
        
        globalStore.completeSaveOperation(true, 'Header duplicated successfully');
        await positionFabAndScrollToBlock(duplicatedBlock.id);
      } else {
        globalStore.completeSaveOperation(false, 'Failed to duplicate header');
      }
    } catch (error) {
      console.error('❌ Error during header block duplication:', error);
      const globalStore = useGlobalStore();
      globalStore.completeSaveOperation(false, 'Error duplicating header');
    } finally {
      isBlockCreationInProgress.value = false;
    }
  }
  async function handleAddBlockAfter(
    targetIndex: number,
    assessmentType: 'quiz' | 'evaluate',
    assessmentId?: number | null
  ) {
    const finalAssessmentId = assessmentId || getAssessmentId();
    if (!finalAssessmentId) return;

    const result = await createNewBlockAfterIndex(
      blockList.value,
      targetIndex,
      assessmentType,
      finalAssessmentId,
      activeAssessment.value
    );

    if (result.success && result.updatedBlocks) {
      blockList.value = result.updatedBlocks;
      synchronizeAssessmentWithBlocks();
      if (result.newBlock) {
        await positionFabAndScrollToBlock(result.newBlock.id);
      }
    }
  }

  async function handleAddHeaderAfter(
    targetIndex: number,
    assessmentType: 'quiz' | 'evaluate',
    assessmentId?: number | null,
    assignSection = true
  ) {
    const finalAssessmentId = assessmentId || getAssessmentId();
    if (!finalAssessmentId) return;

    const result = await createNewHeaderAfterIndex(
      blockList.value,
      targetIndex,
      assessmentType,
      finalAssessmentId,
      assignSection
    );

    if (result.success && result.updatedBlocks) {
      blockList.value = result.updatedBlocks;
      synchronizeAssessmentWithBlocks();
      if (result.newBlock) {
        await positionFabAndScrollToBlock(result.newBlock.id);
      }
    }
  }

  async function handleAddSection(
    assessmentType: 'quiz' | 'evaluate',
    assessmentId?: number | null
  ) {
    const finalAssessmentId = assessmentId || getAssessmentId();
    if (!finalAssessmentId) return;

    const result = await createNewSection(blockList.value, assessmentType, finalAssessmentId);

    if (result.success && result.updatedBlocks) {
      blockList.value = result.updatedBlocks;
      synchronizeAssessmentWithBlocks();
      if (result.newBlocks?.[0]) {
        await positionFabAndScrollToBlock(result.newBlocks[0].id);
      }
    }
  }

  // ===== Store API Export =====
  return {
    // ===== State (with backward compatibility) =====
    blocks: blockList,
    selectedBlock: currentSelectedBlock,
    selectedBlockId: currentSelectedBlockId,
    assessments: assessmentCollection,
    currentAssessment: activeAssessment,
    loading: isLoadingAssessment,
    error: assessmentError,
    meta: assessmentMetadata,
    page: paginationPage,
    limit: paginationLimit,
    search: searchQuery,
    duplicateDialog: showDuplicateDialog,
    isCreatingBlock: isBlockCreationInProgress,
    isDragging: isDragOperationActive,
    forceUpdateTrigger: uiRefreshTrigger,
    fabState: fabPositioningState,
    fabPositionLock: isFabPositionLocked,
    pendingFabPosition: pendingFabTargetPosition,
    blockCreationInProgress: isFabCreationInProgress,
    targetBlockId: fabTargetBlockId,
    totalSections,
    itemBlockPage,
    hasMoreBlocks,

    // ===== Managers =====
    timeoutManager: fabTimeoutManager,

    // ===== Block Operations =====
    addBlock: insertBlockAfterIndex,
    appendBlock: addBlockToEnd,
    updateBlocksOrder,
    setSection: setBlockSection,
    updateBlock,
    deleteBlock,
    duplicateBlock,
    insertBlockWithSequenceManagement: (newBlock: ItemBlock, targetIndex: number) => {
      blockList.value = insertBlockWithSequenceManagement(blockList.value, newBlock, targetIndex);
    },
    addBlocks,
    updateOptionTextInStore,
    loadMoreBlocks,

    // ===== Block Creation Methods =====
    handleAddBlockAfter,
    handleAddHeaderAfter,
    handleAddSection,
    handleDuplicateHeaderBlock,
    syncSequencesToBackend,

    // ===== FAB and Scroll Management =====
    setFabPosition: updateFabPosition,
    createFabProtection: createFabProtectionForBlock,
    scrollToTarget: scrollToSelectedBlock,
    setFabAndScroll: positionFabAndScrollToBlock,

    // ===== UI State Management =====
    forceRefreshBlocks: executeBasicUIRefresh,
    syncBlocksWithAssessment: () => synchronizeBlocksWithAssessment(activeAssessment.value, blockList.value),

    // ===== DOM Reference Management =====
    setBlockRef: domReferenceManager.setBlockDOMReference,
    getBlockRef: domReferenceManager.getBlockDOMReference,

    // ===== Utilities =====
    isAnswerItemBlockType,
    isSectionBlock: (index: number) => isSectionBlock(blockList.value, index),
    isSectionBlockById: (blockId: number) => isSectionBlockById(blockList.value, blockId),
    getSectionNumber: (index: number) => getSectionNumber(blockList.value, index),
    getSectionNumberBySequence: (blockSequence: number) => getSectionNumberBySequence(blockList.value, blockSequence),
    getSectionNumberById: (blockId: number) => getSectionNumberById(blockList.value, blockId),
    resetBlocks,
    initializeBlocks,
    getAssessmentData,

    // ===== ID Generation =====
    generateQuestionId: generateNextQuestionId,
    generateOptionId: generateNextOptionId,

    // ===== Assessment Management =====
    fetchAssessmentById,
    addAssessment,
    updateAssessment,
    removeAssessment,

    // ===== Helper Functions =====
    getAssessmentId,
    getItemBlockById,
    getHeaderBlockId,
    getRadioBlockId,
    getAllItemBlockIds,

    // ===== Validation =====
    validateIds: () => validateAssessmentIds(activeAssessment.value),
    validateBlockDeletion: (blockId: number) => validateBlockDeletion(blockId, activeAssessment.value),
    validatePostDeletion: (deletedBlockId: number) => validatePostDeletion(deletedBlockId, activeAssessment.value),

    // ===== Cleanup =====
    resetStore,
  };
});
