import { ref, computed } from 'vue';
import { defineStore } from 'pinia';
import authService from '../services/authService';
import { jwtDecode, type JwtPayload } from 'jwt-decode';
import { type User, type Permission } from 'src/types/models';
import { useUtilsStore } from './utils';
import type { AxiosError } from 'axios';

export const useAuthStore = defineStore('auth', () => {
  // State
  const loginUsername = ref('');
  const loginPassword = ref('');
  const incorrectUsernamePasswordStatus = ref(false);
  const notifyDialog = ref(false);
  const notifyMessage = ref('');
  const utilsStore = useUtilsStore();
  const secretKey = 'E13qDGn!Z|"38y/BUvFjl$cA-vs4)P';
  const currentRoleName = ref();
  const currentFacultyId = ref<string | null>(null); // Changed to string to match new structure

  // Reset all reactive state to initial values
  const resetState = () => {
    loginUsername.value = '';
    loginPassword.value = '';
    incorrectUsernamePasswordStatus.value = false;
    notifyDialog.value = false;
    notifyMessage.value = '';
    currentRoleName.value = 'Guest';
  };

  // Set the current role name based on the departments or facDepUserRoles
  const setCurrentRole = (roleName: string, facultyId?: string) => {
    try {
      const user = getCurrentUser();

      // Try new departments structure first
      if (user?.departments) {
        const foundDepartment = user.departments.find(
          (department) => department.role?.name === roleName,
        );

        if (foundDepartment) {
          currentRoleName.value = roleName;
          localStorage.setItem('currentRole', roleName);

          // Find target faculty if facultyId is provided
          let targetDepartment;
          if (facultyId) {
            targetDepartment = user.departments.find(
              (department) =>
                department.faculty?.id === facultyId && department.role?.name === roleName,
            );
          }

          if (!targetDepartment) {
            targetDepartment = foundDepartment;
          }

          // Store faculty ID for consistent access later
          if (targetDepartment?.faculty?.id) {
            localStorage.setItem('currentFacultyId', targetDepartment.faculty.id);
            currentFacultyId.value = targetDepartment.faculty.id;
          }
        } else {
          console.warn('Role not found in user departments:', roleName);
          currentRoleName.value = 'Guest';
          localStorage.setItem('currentRole', 'Guest');
        }
      } else if (user?.facDepUserRoles) {
        // Try facDepUserRoles structure
        const foundFacDepRole = user.facDepUserRoles.find(
          (facDepRole) => facDepRole.role?.name === roleName,
        );

        if (foundFacDepRole) {
          currentRoleName.value = roleName;
          localStorage.setItem('currentRole', roleName);

          // Find target faculty if facultyId is provided
          let targetFacDepRole;
          if (facultyId) {
            targetFacDepRole = user.facDepUserRoles.find(
              (facDepRole) =>
                facDepRole.department?.faculty?.id === facultyId &&
                facDepRole.role?.name === roleName,
            );
          }

          if (!targetFacDepRole) {
            targetFacDepRole = foundFacDepRole;
          }

          // Store faculty ID for consistent access later
          if (targetFacDepRole?.department?.faculty?.id) {
            localStorage.setItem('currentFacultyId', targetFacDepRole.department.faculty.id);
            currentFacultyId.value = targetFacDepRole.department.faculty.id;
          }
        } else {
          console.warn('Role not found in user facDepUserRoles:', roleName);
          currentRoleName.value = 'Guest';
          localStorage.setItem('currentRole', 'Guest');
        }
      } else if (user?.faculties) {
        // Fallback to legacy structure for backward compatibility
        const foundRole = user.faculties
          .flatMap((faculty) => faculty.roles || [])
          .find((role) => role.name === roleName);

        if (foundRole) {
          currentRoleName.value = roleName;
          localStorage.setItem('currentRole', roleName);

          // Find target faculty if facultyId is provided
          let targetFaculty;
          if (facultyId) {
            targetFaculty = user.faculties.find(
              (faculty) =>
                faculty.id === facultyId && faculty.roles?.some((role) => role.name === roleName),
            );
          }

          if (!targetFaculty) {
            targetFaculty = user.faculties.find((faculty) =>
              faculty.roles?.some((role) => role.name === roleName),
            );
          }
          // Store faculty ID for consistent access later
          if (targetFaculty) {
            localStorage.setItem('currentFacultyId', targetFaculty.id.toString());
            currentFacultyId.value = targetFaculty.id;
          }
        } else {
          console.warn('Role not found in user faculties:', roleName);
          currentRoleName.value = 'Guest';
          localStorage.setItem('currentRole', 'Guest');
        }
      } else {
        console.warn(
          'No departments, faculties, or facDepUserRoles found for user, setting role to Guest',
        );
        currentRoleName.value = 'Guest';
        localStorage.setItem('currentRole', 'Guest');
      }
    } catch (error) {
      console.error('Error setting current role:', error);
      currentRoleName.value = 'Guest';
      localStorage.setItem('currentRole', 'Guest');
    }
  };

  // Initialize currentRoleName when the store is created
  const storedRole = localStorage.getItem('currentRole') as string | null;
  const storedFacultyId = localStorage.getItem('currentFacultyId');
  const user = getCurrentUser();

  // Initialize reactive facultyId from localStorage
  currentFacultyId.value = storedFacultyId || null;

  // Get all available roles from new structure or fallback to legacy
  const availableRoles =
    user?.departments?.map((department) => department.role?.name).filter(Boolean) ||
    user?.facDepUserRoles?.map((facDepRole) => facDepRole.role?.name).filter(Boolean) ||
    user?.faculties?.flatMap((faculty) => faculty.roles?.map((role) => role.name) || []) ||
    [];

  // Use stored role if it's valid for this user, otherwise use first available role
  let userRole: string | null = null;
  if (availableRoles.length > 0) {
    if (storedRole && availableRoles.includes(storedRole)) {
      userRole = storedRole;
    } else {
      userRole = availableRoles[0] || null; // Use first available role
    }
  }

  currentRoleName.value = userRole || 'Guest';
  console.log('Available roles:', availableRoles);
  console.log('Current role set to:', currentRoleName.value);

  // Getters
  const isLoggedIn = computed(() => {
    return Boolean(localStorage.getItem('access_token'));
  });

  // Flatten all permissions from current role in departments, facDepUserRoles or faculties
  const userPermissions = computed(() => {
    const user = getCurrentUser();
    if (!user || !currentRoleName.value) return [];

    // Try new departments structure first
    if (user.departments) {
      const currentDepartment = user.departments.find(
        (department) => department.role?.name === currentRoleName.value,
      );

      if (currentDepartment?.role?.permissions) {
        return [
          ...new Set(
            currentDepartment.role.permissions
              .map((p: Permission) => p.id ?? p.perId ?? 0)
              .filter((id) => id > 0),
          ),
        ];
      }
    }

    // Try facDepUserRoles structure next
    if (user.facDepUserRoles) {
      const currentFacDepRole = user.facDepUserRoles.find(
        (facDepRole) => facDepRole.role?.name === currentRoleName.value,
      );

      if (currentFacDepRole?.role?.permissions) {
        return [
          ...new Set(
            currentFacDepRole.role.permissions
              .map((p: Permission) => p.id ?? p.perId ?? 0)
              .filter((id) => id > 0),
          ),
        ];
      }
    }

    // Fallback to legacy structure
    if (user.faculties) {
      const currentRole = user.faculties
        .flatMap((faculty) => faculty.roles || [])
        .find((role) => role?.name === currentRoleName.value);

      if (currentRole?.permissions) {
        return [
          ...new Set(
            currentRole.permissions
              .map((p: Permission) => p.id ?? p.perId ?? 0)
              .filter((id) => id > 0),
          ),
        ];
      }
    }

    return [];
  });

  const userRoles = computed(() => {
    const user = getCurrentUser();

    // Try new departments structure first
    if (user?.departments) {
      return user.departments
        .map((department) => department.role?.name)
        .filter(Boolean) as string[];
    }

    // Try facDepUserRoles structure next
    if (user?.facDepUserRoles) {
      return user.facDepUserRoles
        .map((facDepRole) => facDepRole.role?.name)
        .filter(Boolean) as string[];
    }

    // Fallback to legacy structure
    if (user?.faculties) {
      return user.faculties.flatMap((faculty) => faculty.roles?.map((role) => role.name) || []);
    }

    return [];
  });

  // Get all available roles for role switching
  const availableUserRoles = computed(() => {
    return userRoles.value;
  });

  // Get current faculty based on current role
  const currentFaculty = computed(() => {
    const user = getCurrentUser();
    if (!user || !currentRoleName.value) return null;

    // Try new departments structure first
    if (user.departments) {
      // Use reactive facultyId first, then fallback to localStorage
      const facultyId = currentFacultyId.value || localStorage.getItem('currentFacultyId');

      // First try to find faculty by facultyId if available
      if (facultyId) {
        const departmentById = user.departments.find(
          (department) =>
            department.faculty?.id === facultyId && department.role?.name === currentRoleName.value,
        );
        if (departmentById?.faculty) return departmentById.faculty;
      }

      // Fallback to first faculty with current role
      const firstDepartment = user.departments.find(
        (department) => department.role?.name === currentRoleName.value,
      );
      return firstDepartment?.faculty || null;
    }

    // Try facDepUserRoles structure next
    if (user.facDepUserRoles) {
      // Use reactive facultyId first, then fallback to localStorage
      const facultyId = currentFacultyId.value || localStorage.getItem('currentFacultyId');

      // First try to find faculty by facultyId if available
      if (facultyId) {
        const facDepRoleById = user.facDepUserRoles.find(
          (facDepRole) =>
            facDepRole.department?.faculty?.id === facultyId &&
            facDepRole.role?.name === currentRoleName.value,
        );
        if (facDepRoleById?.department?.faculty) return facDepRoleById.department.faculty;
      }

      // Fallback to first faculty with current role
      const firstFacDepRole = user.facDepUserRoles.find(
        (facDepRole) => facDepRole.role?.name === currentRoleName.value,
      );
      return firstFacDepRole?.department?.faculty || null;
    }

    // Fallback to legacy structure
    if (user.faculties) {
      // Use reactive facultyId first, then fallback to localStorage
      const facultyId = currentFacultyId.value || localStorage.getItem('currentFacultyId');

      // First try to find faculty by facultyId if available
      if (facultyId) {
        const facultyById = user.faculties.find(
          (faculty) =>
            faculty.id === facultyId &&
            faculty.roles?.some((role) => role.name === currentRoleName.value),
        );
        if (facultyById) return facultyById;
      }

      // Fallback to first faculty with current role
      return (
        user.faculties.find((faculty) =>
          faculty.roles?.some((role) => role.name === currentRoleName.value),
        ) || null
      );
    }

    return null;
  });

  // Actions
  function getCurrentUser(): User | undefined {
    const token = localStorage.getItem('access_token');
    if (!token) return undefined;
    const user = getUserFromToken(token);
    console.log('AuthStore - getCurrentUser:', user);
    console.log('AuthStore - User departments:', user?.departments);
    console.log('AuthStore - User facDepUserRoles:', user?.facDepUserRoles);
    console.log('AuthStore - User faculties:', user?.faculties);
    return user;
  }

  function getUserFromToken(token: string): User {
    try {
      const decodedToken = jwtDecode<JwtPayload>(token);
      const decrypted = utilsStore.decryptObject(secretKey, decodedToken.sub!);
      if (!decrypted) throw new Error('Failed to decrypt user object');

      const user = decrypted as unknown as User;

      // 🔒 เก็บ permission (จาก role.permissions) เป็น string เข้ารหัสใน localStorage
      if (user.roles?.[0] && 'permissions' in user.roles[0] && user.roles[0].permissions) {
        const perms =
          utilsStore.encryptString(secretKey, JSON.stringify(user.roles[0].permissions), false) ||
          '';
        localStorage.setItem('perms', perms);
      }

      return user;
    } catch (e) {
      console.error('Token decode or decrypt failed:', e);
      return {
        id: '0', // Changed to string
        email: '',
        firstName: '',
        lastName: '',
        username: '',
        departments: [],
        roles: [
          {
            id: 0,
            userId: 0,
            name: 'Guest',
            description: '',
            department: '', // Added required field
          },
        ],
      };
    }
  }

  function logout() {
    // Reset all authentication state using the reset function
    resetState();

    // Clear localStorage completely
    localStorage.removeItem('access_token');
    localStorage.removeItem('perms');
    localStorage.removeItem('hasVisited');
    localStorage.removeItem('currentRole');
    localStorage.removeItem('currentFacultyId');
    localStorage.removeItem('currentFacultyName');
    localStorage.removeItem('redirectAfterLogin');
    localStorage.removeItem('acs');

    // Navigate to login and force reload to ensure clean state
    window.location.href = '/login';
    setTimeout(() => {
      window.location.reload();
    }, 100);
  }

  function showNotifyDialog(message: string) {
    notifyMessage.value = message;
    notifyDialog.value = true;
  }

  async function loginBuu(): Promise<boolean> {
    // Reset error and notification states before attempting login
    incorrectUsernamePasswordStatus.value = false;
    notifyDialog.value = false;
    notifyMessage.value = '';

    try {
      const encryptedUsername = utilsStore.encryptString(
        'loginBuu_username',
        loginUsername.value,
        false,
      );
      const encryptedPassword = utilsStore.encryptString(
        'loginBuu_password',
        loginPassword.value,
        false,
      );
      localStorage.removeItem('access_token');
      localStorage.removeItem('perms');
      const response = await authService.loginBuu(encryptedUsername!, encryptedPassword!);
      localStorage.setItem('access_token', response.data.access_token);
      setCurrentRole(userRoles.value[0] || 'Guest');

      return true;
    } catch (error) {
      const axiosError = error as AxiosError<{ message: string }>;
      if (axiosError.response?.data.message === 'ชื่อผู้ใช้หรือรหัสผ่านไม่ถูกต้อง') {
        incorrectUsernamePasswordStatus.value = true;
      } else {
        console.error('Login error:', axiosError);
        showNotifyDialog('เกิดข้อผิดพลาดในการเข้าสู่ระบบ');
      }
      return false;
    }
  }

  async function login(): Promise<boolean> {
    // Reset error and notification states before attempting login
    incorrectUsernamePasswordStatus.value = false;
    notifyDialog.value = false;
    notifyMessage.value = '';

    try {
      localStorage.removeItem('access_token');
      localStorage.removeItem('perms');

      const response = await authService.login(loginUsername.value, loginPassword.value);
      localStorage.setItem('access_token', response.data.access_token);

      // Wait for token to be processed and user data to be available
      await new Promise((resolve) => setTimeout(resolve, 50));

      // Set current role after token is processed
      const user = getCurrentUser();
      if (user && userRoles.value.length > 0) {
        setCurrentRole(userRoles.value[0] || 'Guest');
      } else {
        setCurrentRole('Guest');
      }

      return true;
    } catch (error) {
      const axiosError = error as AxiosError<{ message: string }>;
      if (axiosError.response?.data.message === 'ชื่อผู้ใช้หรือรหัสผ่านไม่ถูกต้อง') {
        incorrectUsernamePasswordStatus.value = true;
      } else {
        console.error('Login error:', axiosError);
        showNotifyDialog('เกิดข้อผิดพลาดในการเข้าสู่ระบบ');
      }
      return false;
    }
  }

  // Return all permission ids from both roles[].permissions and psnPermissions
  function getUserListPermId(user?: User): number[] {
    if (!user) return [];
    // From roles[].permissions
    const rolePerms =
      user.roles?.flatMap((role) =>
        'permissions' in role && Array.isArray(role.permissions)
          ? role.permissions.map((p) => p.id)
          : [],
      ) ?? [];
    // From psnPermissions
    const psnPerms = Array.isArray(user.permissions) ? user.permissions.map((p) => p.perId) : [];
    // Merge and dedupe
    return Array.from(new Set([...rolePerms, ...psnPerms]));
  }

  // Helper functions
  function hasPermission(permId: number): boolean {
    // Super Admin has all permissions
    if (currentRoleName.value === 'Super Admin') {
      return true;
    }
    return userPermissions.value.includes(permId);
  }
  function hasAnyPermission(permIds: number[]): boolean {
    // Super Admin has all permissions
    if (currentRoleName.value === 'Super Admin') {
      return true;
    }
    return permIds.some((id) => userPermissions.value.includes(id));
  }

  function hasAnyPermissionByName(permNames: string[]): boolean {
    // Super Admin has all permissions
    if (currentRoleName.value === 'Super Admin') {
      return true;
    }

    const user = getCurrentUser();
    if (!user || !currentRoleName.value) return false;

    // Try new departments structure first
    if (user.departments) {
      const currentDepartment = user.departments.find(
        (department) => department.role?.name === currentRoleName.value,
      );

      if (currentDepartment?.role?.permissions) {
        const userPermissionNames = currentDepartment.role.permissions
          .map((p: Permission) => p.name || p.perName || '')
          .filter((name) => name !== '');

        return permNames.some((name) => userPermissionNames.includes(name));
      }
    }

    // Try facDepUserRoles structure next
    if (user.facDepUserRoles) {
      const currentFacDepRole = user.facDepUserRoles.find(
        (facDepRole) => facDepRole.role?.name === currentRoleName.value,
      );

      if (currentFacDepRole?.role?.permissions) {
        const userPermissionNames = currentFacDepRole.role.permissions
          .map((p: Permission) => p.name || p.perName || '')
          .filter((name) => name !== '');

        return permNames.some((name) => userPermissionNames.includes(name));
      }
    }

    // Fallback to legacy structure
    if (user.faculties) {
      const currentRole = user.faculties
        .flatMap((faculty) => faculty.roles || [])
        .find((role) => role?.name === currentRoleName.value);

      if (currentRole?.permissions) {
        const userPermissionNames = currentRole.permissions
          .map((p: Permission) => p.name || p.perName || '')
          .filter((name) => name !== '');

        return permNames.some((name) => userPermissionNames.includes(name));
      }
    }

    return false;
  }
  function isSuperAdmin(): boolean {
    return currentRoleName.value === 'Super Admin';
  }
  function hasRole(roleName: string): boolean {
    return userRoles.value.includes(roleName);
  }

  return {
    // State
    secretKey,
    loginUsername,
    loginPassword,
    incorrectUsernamePasswordStatus,
    notifyDialog,
    notifyMessage,
    currentFacultyId,
    // Getters
    isLoggedIn,
    userPermissions,
    currentRoleName,
    userRoles,
    availableUserRoles,
    currentFaculty,
    // Actions
    login,
    loginBuu,
    getCurrentUser,
    getUserPermIds: getUserListPermId,
    logout,
    showNotifyDialog,
    setCurrentRole,
    resetState,
    // Helper functions
    hasPermission,
    hasAnyPermission,
    hasAnyPermissionByName,
    isSuperAdmin,
    hasRole,
  };
});
