import type { IndividualDevelopmentPlanStatus } from 'src/types/models';

// Status options for individual development plans
export const INDIVIDUAL_DEVELOPMENT_PLAN_STATUS_OPTIONS = [
  { label: 'ใช้งาน', value: 'active' as IndividualDevelopmentPlanStatus },
  { label: 'ไม่ใช้งาน', value: 'inactive' as IndividualDevelopmentPlanStatus },
  { label: 'เสร็จสิ้น', value: 'completed' as IndividualDevelopmentPlanStatus },
  { label: 'ยกเลิก', value: 'cancelled' as IndividualDevelopmentPlanStatus },
];

// Department options
export const DEPARTMENT_OPTIONS = [
  'เทคโนโลยีสารสนเทศ',
  'การตลาด',
  'ทรัพยากรมนุษย์',
  'การเงิน',
  'วิจัยและพัฒนา',
  'ฝ่ายขาย',
  'ฝ่ายผลิต',
  'ฝ่ายบริหาร',
  'ฝ่ายกฎหมาย',
  'ฝ่ายบัญชี',
];

// Year options generator
export const generateYearOptions = (yearsAhead: number = 5): number[] => {
  const currentYear = new Date().getFullYear();
  const years = [];
  for (let i = currentYear; i <= currentYear + yearsAhead; i++) {
    years.push(i);
  }
  return years;
};

// Status display helper
export const getStatusDisplay = (status: IndividualDevelopmentPlanStatus): string => {
  const statusOption = INDIVIDUAL_DEVELOPMENT_PLAN_STATUS_OPTIONS.find(
    option => option.value === status
  );
  return statusOption?.label || status;
};

// Progress calculation helper
export const calculateProgress = (status: IndividualDevelopmentPlanStatus): number => {
  switch (status) {
    case 'completed':
      return 100;
    case 'active':
      return 65; // Default progress for active plans
    case 'inactive':
      return 25;
    case 'cancelled':
      return 0;
    default:
      return 0;
  }
};
