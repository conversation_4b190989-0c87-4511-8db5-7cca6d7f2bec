import { getQuizSettingsApi, updateQuizSettingsApi, type UpdateQuizSettingsPayload } from 'src/apis/quizSettings';
import { showSuccess, showError } from 'src/utils/notifications';

export class QuizSettingsService {
  /**
   * Get quiz settings for an assessment
   * @param assessmentId - The ID of the assessment
   * @returns Promise containing quiz settings
   */
  async getSettings(assessmentId: number) {
    try {
      const response = await getQuizSettingsApi(assessmentId);
      return response.data;
    } catch (error) {
      showError('ไม่สามารถโหลดการตั้งค่าได้');
      throw error;
    }
  }

  /**
   * Update quiz settings for an assessment
   * @param assessmentId - The ID of the assessment
   * @param settings - The settings to update
   * @returns Promise containing updated assessment
   */
  async updateSettings(assessmentId: number, settings: UpdateQuizSettingsPayload) {
    try {
      const response = await updateQuizSettingsApi(assessmentId, settings);
      showSuccess('บันทึกการตั้งค่าสำเร็จ');
      return response.data;
    } catch (error) {
      showError('ไม่สามารถบันทึกการตั้งค่าได้');
      throw error;
    }
  }
}

// Export singleton instance
export const quizSettingsService = new QuizSettingsService();
