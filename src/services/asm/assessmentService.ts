/**
 * Assessment Service - Refactored to functional exports
 *import type { QTableProps } from 'quasar';
import type { AssessmentType } from 'src/types/data';
import type { Assessment, ItemBlock, HeaderBody, Question, Option } from 'src/types/models';
import type { AssessmentFetchResponse, UpdateBlockPayload, UpdateSequenceResponse } from 'src/types/api';
import * as assessmentApi from 'src/apis/assessment';
import * as blockApi from 'src/apis/blockApi';
import * as headerBodyApi from 'src/apis/headerBodyApi';is file provides functional exports for assessment operations.
 * The original class-based approach has been replaced with individual functions
 * organized by functionality and extracted to separate utility files.
 */

export type { AssessmentType } from 'src/types/data';
export type { 
  Assessment, 
  ItemBlock, 
  HeaderBody, 
  Question, 
  Option 
} from 'src/types/models';
export type { 
  UpdateBlockPayload, 
  UpdateSequenceResponse, 
  AssessmentFetchResponse 
} from 'src/types/api';

// Re-export assessment operations
export {
  fetchAssessments,
  fetchAssessmentsForView,
  fetchAssessment,
  getAssessmentByUUID,
  createAssessment,
  updateAssessment,
  duplicateAssessment,
  deleteAssessment,
} from 'src/apis/assessment';

// Re-export block operations
export {
  createBlock,
  updateBlock,
  updateBlockSequences,
  deleteBlock,
  duplicateBlock,
} from 'src/apis/blockApi';
export type { CreateBlockPayload, DuplicateBlockData } from 'src/apis/blockApi';

// Re-export header body and related operations
export {
  fetchHeaderBody,
  createHeaderBody,
  updateHeaderBody,
  deleteHeaderBody,
  updateQuestion,
  updateQuestionLegacy,
  updateOption,
  getQuizHeader,
  getQuizByLinkUrl,
} from 'src/apis/headerBodyApi';

/**
 * Legacy compatibility layer
 * Provides the same interface as the original AssessmentService class
 * for backward compatibility during migration.
 */

/**
 * Legacy compatibility layer
 * Provides the same interface as the original AssessmentService class
 * for backward compatibility during migration.
 */

import type { QTableProps } from 'quasar';
import type { AssessmentType } from 'src/types/data';
import type { Assessment, ItemBlock, HeaderBody, Question, Option } from 'src/types/models';
import type { AssessmentFetchResponse, UpdateBlockPayload } from 'src/types/api';
import * as assessmentApi from 'src/apis/assessment';
import * as blockApi from 'src/apis/blockApi';
import * as headerBodyApi from 'src/apis/headerBodyApi';

/**
 * Legacy AssessmentService class for backward compatibility
 * @deprecated Use individual functional exports instead
 */
export class AssessmentService {
  readonly path = '/assessments';
  readonly type: AssessmentType;
  readonly headerBodyPath = '/header-bodies';

  constructor(type: AssessmentType) {
    this.type = type;
  }

  async fetchAll(pag: QTableProps['pagination'], search?: string) {
    return assessmentApi.fetchAssessments(this.type, pag, search);
  }

  async fetchAllStUser(pag: QTableProps['pagination'], search?: string) {
    return assessmentApi.fetchAssessmentsForView(this.type, pag, search);
  }

  async fetchOne(
    id: number,
    query?: { page?: number; limit?: number }
  ): Promise<AssessmentFetchResponse> {
    return assessmentApi.fetchAssessment(id, query);
  }

  getAssessmentByUUID = async (id: string): Promise<{ data: Assessment }> => {
    return assessmentApi.getAssessmentByUUID(id);
  };

  async fetchAndAppendBlocks(
    id: number,
    query: { page?: number; limit?: number },
    existingBlocks: ItemBlock[] = []
  ): Promise<AssessmentFetchResponse> {
    const response = await assessmentApi.fetchAssessment(id, query);
    const combinedBlocks = [...existingBlocks, ...response.pagedItemBlocks];

    return {
      assessment: {
        ...response.assessment,
        itemBlocks: combinedBlocks,
      },
      pagedItemBlocks: combinedBlocks,
    };
  }

  async createOne(assessment: Partial<Assessment>) {
    return assessmentApi.createAssessment(assessment);
  }

  async updateOne(id: number, assessment: Partial<Assessment>) {
    return assessmentApi.updateAssessment(id, assessment);
  }

  async duplicate(sourceId: number, targetId: number) {
    return assessmentApi.duplicateAssessment(sourceId, targetId);
  }

  async deleteOne(id: number) {
    return assessmentApi.deleteAssessment(id);
  }

  async createBlock(block: Omit<ItemBlock, 'id'>): Promise<ItemBlock | undefined> {
    const createBlockPayload: blockApi.CreateBlockPayload = {
      assessmentId: block.assessmentId,
      type: block.type,
      sequence: block.sequence,
      section: block.section,
      isRequired: block.isRequired,
    };
    return blockApi.createBlock(createBlockPayload, this.type);
  }

  async addBlock(block: ItemBlock): Promise<ItemBlock | undefined> {
    console.log('⚠️ addBlock method is deprecated, using updateBlock instead');
    return blockApi.updateBlock({
      id: block.id,
      type: block.type,
      isRequired: block.isRequired,
      assessmentId: block.assessmentId,
    });
  }

  async updateBlock(block: UpdateBlockPayload): Promise<ItemBlock | undefined> {
    return blockApi.updateBlock(block);
  }

  async updateBlockSequences(blocks: ItemBlock[]) {
    return blockApi.updateBlockSequences(blocks);
  }

  async deleteBlock(block: ItemBlock): Promise<ItemBlock | undefined> {
    return blockApi.deleteBlock(block, this.type);
  }

  async duplicateBlock(
    sourceBlockId: number,
    duplicateData: blockApi.DuplicateBlockData
  ): Promise<ItemBlock | undefined> {
    return blockApi.duplicateBlock(sourceBlockId, duplicateData);
  }

  // HeaderBody methods
  async fetchHeaderBody(id: number) {
    return headerBodyApi.fetchHeaderBody(id);
  }

  async createHeaderBody(headerBody: Partial<HeaderBody>) {
    return headerBodyApi.createHeaderBody(headerBody);
  }

  async updateHeaderBody(id: number, headerBody: Partial<HeaderBody>) {
    return headerBodyApi.updateHeaderBody(id, headerBody);
  }

  async deleteHeaderBody(id: number) {
    return headerBodyApi.deleteHeaderBody(id);
  }

  // Question methods
  async updateQuestion(id: number, question: Partial<Question>) {
    return headerBodyApi.updateQuestion(id, question);
  }

  async updateQuestionLegacy(itemBlockId: number, itemBlockType: string, question: Partial<Question>) {
    return headerBodyApi.updateQuestionLegacy(itemBlockId, itemBlockType, question);
  }

  // Option methods
  async updateOption(itemBlockId: number, itemBlockType: string, option: Partial<Option>) {
    return headerBodyApi.updateOption(itemBlockId, itemBlockType, option);
  }

  // Quiz methods
  async getQuizHeader(linkUrl: string) {
    return headerBodyApi.getQuizHeader(linkUrl);
  }

  async getQuizByLinkUrl(linkUrl: string) {
    return headerBodyApi.getQuizByLinkUrl(linkUrl);
  }
}
