import { getEvaluateSettingsApi, updateEvaluateSettingsApi, type UpdateEvaluateSettingsPayload } from 'src/apis/evaluateSettings';
import { showSuccess, showError } from 'src/utils/notifications';

export class EvaluateSettingsService {
  /**
   * Get evaluate settings for an assessment
   * @param assessmentId - The ID of the assessment
   * @returns Promise containing evaluate settings
   */
  async getSettings(assessmentId: number) {
    try {
      const response = await getEvaluateSettingsApi(assessmentId);
      return response.data;
    } catch (error) {
      showError('ไม่สามารถโหลดการตั้งค่าได้');
      throw error;
    }
  }

  /**
   * Update evaluate settings for an assessment
   * @param assessmentId - The ID of the assessment
   * @param settings - The settings to update
   * @returns Promise containing updated assessment
   */
  async updateSettings(assessmentId: number, settings: UpdateEvaluateSettingsPayload) {
    try {
      const response = await updateEvaluateSettingsApi(assessmentId, settings);
      showSuccess('บันทึกการตั้งค่าสำเร็จ');
      return response.data;
    } catch (error) {
      showError('ไม่สามารถบันทึกการตั้งค่าได้');
      throw error;
    }
  }
}

// Export singleton instance
export const evaluateSettingsService = new EvaluateSettingsService();
