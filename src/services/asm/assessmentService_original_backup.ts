import type { QTableProps } from 'quasar';
import type { AssessmentType } from 'src/types/data';
import type {
  Assessment,
  ItemBlock,
  HeaderBody,
  Question,
  Option,
  ItemBlockType,
} from 'src/types/models';
import type {
  UpdateBlockPayload,
  UpdateSequenceResponse,
  AssessmentFetchResponse,
} from 'src/types/api';
import { api as axios } from 'src/boot/axios';
import {
  fetchAssessmentsApi,
  fetchAssessmentsForViewApi,
  fetchAssessmentApi,
  getAssessmentByUUIDApi,
  createAssessmentA<PERSON>,
  updateAssessmentApi,
  duplicateAssessmentApi,
  deleteAssessmentApi,
} from 'src/apis/assessment';
import { getAssessmentErrorMessage } from 'src/utils/assessmentErrors';
import { useGlobalStore } from 'src/stores/global';
import { showSuccess, showError } from 'src/utils/notifications';
import type { AxiosError } from 'axios';

const globalStore = useGlobalStore();

export class AssessmentService {
  readonly path = '/assessments';
  readonly type: AssessmentType;
  readonly headerBodyPath = '/header-bodies';

  constructor(type: AssessmentType) {
    this.type = type;
  }

  async fetchAll(pag: QTableProps['pagination'], search?: string) {
    try {
      const response = await fetchAssessmentsApi(this.path, this.type, pag, search);
      return response.data;
    } catch (error) {
      const axiosError = error as AxiosError;
      const errorMessage = getAssessmentErrorMessage(axiosError, 'โหลด');
      showError(errorMessage);
      throw new Error(errorMessage);
    }
  }

  async fetchAllStUser(pag: QTableProps['pagination'], search?: string) {
    try {
      const response = await fetchAssessmentsForViewApi(this.path, this.type, pag, search);
      return response.data;
    } catch (error) {
      const axiosError = error as AxiosError;
      const errorMessage = getAssessmentErrorMessage(axiosError, 'โหลด');
      showError(errorMessage);
      throw new Error(errorMessage);
    }
  }

  async fetchOne(
    id: number,
    query?: { page?: number; limit?: number },
  ): Promise<AssessmentFetchResponse> {
    try {
      const response = await fetchAssessmentApi(this.path, id, query);
      console.log('fetchOne response:', response.data);
      return response.data;
    } catch (error) {
      const axiosError = error as AxiosError;
      const errorMessage = getAssessmentErrorMessage(axiosError, 'โหลด');
      showError(errorMessage);
      throw new Error(errorMessage);
    }
  }

  getAssessmentByUUID = async (id: string): Promise<{ data: Assessment }> => {
    try {
      const response = await getAssessmentByUUIDApi(this.path, id);
      return response;
    } catch (error) {
      const axiosError = error as AxiosError;
      const errorMessage = getAssessmentErrorMessage(axiosError, 'โหลด');
      showError(errorMessage);
      throw error;
    }
  };

  async fetchAndAppendBlocks(
    id: number,
    query: { page?: number; limit?: number },
    existingBlocks: ItemBlock[] = [],
  ): Promise<AssessmentFetchResponse> {
    try {
      const response = await fetchAssessmentApi(this.path, id, query);
      const combinedBlocks = [...existingBlocks, ...response.data.pagedItemBlocks];

      return {
        assessment: {
          ...response.data.assessment,
          itemBlocks: combinedBlocks,
        },
        pagedItemBlocks: combinedBlocks,
      };
    } catch (error) {
      const axiosError = error as AxiosError;
      const errorMessage = getAssessmentErrorMessage(axiosError, 'โหลด');
      showError(errorMessage);
      throw new Error(errorMessage);
    }
  }

  async createOne(assessment: Partial<Assessment>) {
    try {
      const response = await createAssessmentApi(this.path, assessment);
      globalStore.Loading();
      showSuccess('สร้างแบบประเมินสำเร็จ');
      return response.data;
    } catch (error) {
      const axiosError = error as AxiosError;
      const errorMessage = getAssessmentErrorMessage(axiosError, 'สร้าง');
      showError(errorMessage);
      throw new Error(errorMessage);
    }
  }

  async updateOne(id: number, assessment: Partial<Assessment>) {
    try {
      const response = await updateAssessmentApi(this.path, id, assessment);
      showSuccess('อัปเดตแบบประเมินสำเร็จ');
      return response.data;
    } catch (error) {
      const axiosError = error as AxiosError;
      const errorMessage = getAssessmentErrorMessage(axiosError, 'อัปเดต');
      showError(errorMessage);
      throw new Error(errorMessage);
    }
  }

  async duplicate(sourceId: number, targetId: number) {
    try {
      const response = await duplicateAssessmentApi(this.path, sourceId, targetId);
      showSuccess('คัดลอกแบบประเมินสำเร็จ');
      return response.data;
    } catch (error) {
      const axiosError = error as AxiosError;
      const errorMessage = getAssessmentErrorMessage(axiosError, 'คัดลอก');
      showError(errorMessage);
      throw new Error(errorMessage);
    }
  }

  async deleteOne(id: number) {
    try {
      const response = await deleteAssessmentApi(this.path, id);
      showSuccess('ลบแบบประเมินสำเร็จ');
      return response.data;
    } catch (error) {
      const axiosError = error as AxiosError;
      const errorMessage = getAssessmentErrorMessage(axiosError, 'ลบ');
      showError(errorMessage);
      throw new Error(errorMessage);
    }
  }

  async createBlock(block: Omit<ItemBlock, 'id'>): Promise<ItemBlock | undefined> {
    console.log('🚀 AssessmentService.createBlock called with:', {
      type: this.type,
      path: this.path,
      block: block,
    });

    // Use the correct endpoint directly based on the type
    if (this.type === 'evaluate') {
      // For evaluate type, use the correct endpoint: /item-blocks/block

      return await this.createBlockViaCorrectEndpoint(block);
    } else {
      // For quiz type, try the original endpoint first
      try {
        console.log('📍 Attempting to create block with quiz endpoint:', `/item-blocks`);

        const res = await axios.post<ItemBlock>(`/item-blocks/block`, block);
        globalStore.Loading();
        return res.data;
      } catch (error: unknown) {
        console.error('❌ Block creation failed:', error);

        // Type guard for axios error
        if (error && typeof error === 'object' && 'response' in error) {
          const axiosError = error as { response?: { data?: unknown; status?: number } };
          console.error('Error response:', axiosError.response?.data);
          console.error('Error status:', axiosError.response?.status);

          // Check if it's a 404 error and try alternative endpoints
          if (axiosError.response?.status === 404) {
            return await this.createBlockFallback(block);
          } else {
            showError('ไม่สามารถสร้าง Block ได้');
          }
        } else {
          console.error('Full error:', error);
          showError('ไม่สามารถสร้าง Block ได้');
        }
        return;
      }
    }
  }

  // Fallback method to try alternative approaches
  private async createBlockFallback(block: Omit<ItemBlock, 'id'>): Promise<ItemBlock | undefined> {
    // First try the simple /evaluate/item-blocks endpoint
    try {
      const payload = {
        assessmentId: block.assessmentId,
        type: block.type || 'RADIO',
        sequence: block.sequence,
        section: block.section,
        isRequired: block.isRequired,
      };

      const res = await axios.post<ItemBlock>('/evaluate/item-blocks', payload);

      globalStore.Loading();
      return res.data;
    } catch (error) {
      console.error('Simple evaluate endpoint failed:', error);
    }

    try {
      // Try the correct evaluate endpoint: /evaluate/item-blocks/block
      return await this.createBlockViaCorrectEndpoint(block);
    } catch (error) {
      console.error('Correct evaluate endpoint failed:', error);
    }

    try {
      // Try using the assessment update approach as backup
      return await this.createBlockViaAssessmentUpdate(block);
    } catch (error) {
      console.error('Assessment update approach failed:', error);
    }

    // Try other alternative endpoints as last resort
    const alternatives = [
      `/evaluate/item-blocks`,
      `/item-blocks`,
      `/evaluate/item-blocks/create`,
      `/evaluate/blocks`,
      `/evaluate/blocks/create`,
      `${this.path.replace('/assessments', '')}/item-blocks`,
      `${this.path.replace('/assessments', '')}/blocks`,
    ];

    for (const endpoint of alternatives) {
      try {
        const res = await axios.post<ItemBlock>(endpoint, block);

        globalStore.Loading();
        return res.data;
      } catch {
        continue;
      }
    }

    // If all alternatives fail, show error
    showError(`ไม่พบ endpoint ที่ใช้งานได้สำหรับการสร้าง Block - กรุณาตรวจสอบ backend API`);
    return;
  }

  // Try creating block using the correct evaluate endpoint
  private async createBlockViaCorrectEndpoint(
    block: Omit<ItemBlock, 'id'>,
  ): Promise<ItemBlock | undefined> {
    try {
      // Prepare the payload with required fields (based on backend logs)
      const payload = {
        assessmentId: block.assessmentId,
        type: block.type || 'RADIO', // Default to 'RADIO' as specified
        sequence: block.sequence,
        section: block.section,
        isRequired: block.isRequired,
      };

      // Use the exact endpoint that works from backend logs
      const res = await axios.post<ItemBlock>('/item-blocks/block', payload);

      globalStore.Loading();
      return res.data;
    } catch (error: unknown) {
      console.error('❌ Correct endpoint failed:', error);

      // Type guard for axios error
      if (error && typeof error === 'object' && 'response' in error) {
        const axiosError = error as { response?: { data?: unknown; status?: number } };
        console.error('Error response from correct endpoint:', axiosError.response?.data);
        console.error('Error status from correct endpoint:', axiosError.response?.status);

        // If it's the header creation bug, don't try fallbacks for HEADER type
        if (block.type === 'HEADER' && axiosError.response?.status === 500) {
          console.error(
            '🚫 Header creation failed due to known backend bug - not trying fallbacks',
          );
          throw error; // Re-throw to let the caller handle it
        }
      }

      // Try fallback approaches for non-header blocks
      if (block.type !== 'HEADER') {
        return await this.createBlockFallback(block);
      } else {
        throw error; // Re-throw for header blocks
      }
    }
  }

  // Try creating block by updating the assessment with new blocks
  private async createBlockViaAssessmentUpdate(
    block: Omit<ItemBlock, 'id'>,
  ): Promise<ItemBlock | undefined> {
    if (!block.assessmentId) {
      throw new Error('Assessment ID is required for block creation');
    }

    // First, get the current assessment
    // const currentAssessment = await this.fetchOne(block.assessmentId);
    const { assessment: currentAssessment } = await this.fetchOne(block.assessmentId);

    // Create a new block with a temporary ID (backend should assign real ID)
    const tempId = Date.now(); // Temporary ID
    const newBlock: ItemBlock = {
      id: tempId,
      ...block,
    };

    // Add the new block to the assessment
    const updatedItemBlocks = [...(currentAssessment.itemBlocks || []), newBlock];

    const assessmentUpdate = {
      ...currentAssessment,
      itemBlocks: updatedItemBlocks,
    };

    // Update the assessment with the new block
    const updatedAssessment = await axios.patch<Assessment>(
      `${this.path}/${block.assessmentId}`,
      assessmentUpdate,
    );

    // Find the newly created block in the response
    const createdBlock = updatedAssessment.data.itemBlocks?.find(
      (b) => b.sequence === block.sequence && b.section === block.section && b.type === block.type,
    );

    if (createdBlock) {
      globalStore.Loading();
      return createdBlock;
    } else {
      throw new Error('Block was not found in updated assessment');
    }
  }

  async addBlock(block: ItemBlock): Promise<ItemBlock | undefined> {
    try {
      console.log('➕ AssessmentService.addBlock called with:', {
        type: this.type,
        blockId: block.id,
        blockType: block.type,
      });

      let addEndpoint: string;

      if (this.type === 'evaluate') {
        // For evaluate type, use the correct endpoint: /item-blocks/{id}
        addEndpoint = `/item-blocks/${block.id}`;
      } else {
        // For quiz type, use the original endpoint
        addEndpoint = `${this.path}/item-blocks/${block.id}`;
      }

      const res = await axios.put<ItemBlock>(addEndpoint, block);

      globalStore.Loading();
      return res.data;
    } catch (error: unknown) {
      console.error('❌ Block add failed:', error);

      // Type guard for axios error
      if (error && typeof error === 'object' && 'response' in error) {
        const axiosError = error as { response?: { data?: unknown; status?: number } };
        console.error('Add error response:', axiosError.response?.data);
        console.error('Add error status:', axiosError.response?.status);
      }

      showError('ไม่สามารถเพิ่ม Block ได้');
      return;
    }
  }

  // async updateBlock(block: ItemBlock): Promise<ItemBlock | undefined> {
  //   try {
  //     console.log('🔄 AssessmentService.updateBlock called with:', {
  //       type: this.type,
  //       blockId: block.id,
  //       blockType: block.type,
  //     });

  //     // let updateEndpoint: string;

  //     // if (this.type === 'evaluate') {
  //     //   // For evaluate type, use the correct endpoint: /item-blocks/{id}
  //     //   updateEndpoint = `/item-blocks/${block.id}`;
  //     // } else {
  //     //   // For quiz type, use the original endpoint
  //     //   updateEndpoint = `${this.path}/item-blocks/${block.id}`;
  //     // }

  //     const updateEndpoint = `/item-blocks/${block.id}`;

  //     const sanitizedBlock = {
  //       id: block.id,
  //       type: block.type,
  //       // ส่ง options เฉพาะที่แน่ใจว่า valid (ถ้าจำเป็นต้องส่ง)
  //       options: block.options?.map((opt) => ({
  //         ...opt,
  //         itemBlockId: block.id, // ✅ บังคับใส่ให้ครบ
  //       })),
  //     };

  //     // const res = await axios.patch<ItemBlock>(updateEndpoint, block);
  //     const res = await axios.patch<ItemBlock>(updateEndpoint, sanitizedBlock);

  //     console.log('res.data: ', res.data);

  //     globalStore.Loading();
  //     return res.data;
  //   } catch (error: unknown) {
  //     console.error('❌ Block update failed:', error);

  //     // Type guard for axios error
  //     if (error && typeof error === 'object' && 'response' in error) {
  //       const axiosError = error as { response?: { data?: unknown; status?: number } };
  //       console.error('Update error response:', axiosError.response?.data);
  //       console.error('Update error status:', axiosError.response?.status);
  //     }

  //     showError('ไม่สามารถอัปเดต Block ได้');
  //     return;
  //   }
  // }

  async updateBlock(block: UpdateBlockPayload): Promise<ItemBlock | undefined> {
    try {
      const updateEndpoint = `/item-blocks/${block.id}`;

      const sanitizedBlock: Partial<ItemBlock> = {
        id: block.id,
        ...(block.type && { type: block.type as ItemBlockType }),
        ...(block.isRequired !== undefined && { isRequired: block.isRequired }),
        ...(block.assessmentId !== undefined && { assessmentId: block.assessmentId }),
      };

      if (block.options?.length) {
        sanitizedBlock.options = block.options.map((opt, idx) => ({
          id: opt.id ?? 0,
          itemBlockId: opt.itemBlockId ?? block.id,
          optionText: opt.optionText ?? '',
          value: opt.value ?? 0,
          sequence: idx,
        }));
      }

      console.log('before send: ', block.type);

      const res = await axios.patch<ItemBlock>(updateEndpoint, sanitizedBlock);

      console.log('✅ Updated block from server:', res.data);

      globalStore.Loading();
      return res.data;
    } catch (error: unknown) {
      console.error('❌ Block update failed:', error);

      if (error && typeof error === 'object' && 'response' in error) {
        const axiosError = error as { response?: { data?: unknown; status?: number } };
        console.error('Update error response:', axiosError.response?.data);
        console.error('Update error status:', axiosError.response?.status);
      }

      showError('ไม่สามารถอัปเดต Block ได้');
      return;
    }
  }

  async updateBlockSequences(blocks: ItemBlock[]): Promise<UpdateSequenceResponse | undefined> {
    try {
      console.log('🔄 AssessmentService.updateBlockSequences called with:', {
        type: this.type,
        blocksCount: blocks.length,
        blockIds: blocks.map((b) => b.id),
        sequences: blocks.map((b) => b.sequence),
      });

      // Validate blocks before preparing payload
      const validatedBlocks = blocks.filter((block) => {
        const isValid = block.id && !isNaN(Number(block.id)) && Number(block.id) > 0;
        if (!isValid) {
          console.error('❌ Invalid block detected in updateBlockSequences:', {
            blockId: block.id,
            blockType: block.type,
            sequence: block.sequence,
            idType: typeof block.id,
            isNaN: isNaN(Number(block.id)),
          });
        }
        return isValid;
      });

      if (validatedBlocks.length === 0) {
        console.error('❌ No valid blocks to update sequences');
        showError('ไม่มีรายการที่ถูกต้องสำหรับการอัปเดตลำดับ');
        return;
      }

      // Prepare the payload for the API
      const payload = {
        itemBlocks: validatedBlocks.map((block) => ({
          id: Number(block.id), // Ensure ID is a number
          sequence: Number(block.sequence), // Ensure sequence is a number
        })),
      };

      console.log('📤 Sending payload to backend:', {
        payload,
        originalBlocksCount: blocks.length,
        validatedBlocksCount: validatedBlocks.length,
        itemBlockIds: payload.itemBlocks.map((b) => b.id),
        itemBlockSequences: payload.itemBlocks.map((b) => b.sequence),
      });

      const res = await axios.patch<UpdateSequenceResponse>(
        '/item-blocks/update/sequences',
        payload,
      );

      console.log('✅ Block sequences updated successfully:', res.data);
      return res.data;
    } catch (error: unknown) {
      console.error('❌ Block sequence update failed:', error);

      // Type guard for axios error
      if (error && typeof error === 'object' && 'response' in error) {
        const axiosError = error as { response?: { data?: unknown; status?: number } };
        console.error('Sequence update error response:', axiosError.response?.data);
        console.error('Sequence update error status:', axiosError.response?.status);
      }

      showError('ไม่สามารถอัปเดตลำดับได้');
      return;
    }
  }

  async deleteBlock(block: ItemBlock): Promise<ItemBlock | undefined> {
    try {
      console.log('🗑️ AssessmentService.deleteBlock called with:', {
        type: this.type,
        blockId: block.id,
        blockType: block.type,
        assessmentId: block.assessmentId,
        hasHeaderBody: !!block.headerBody,
        hasQuestions: !!block.questions?.length,
        hasOptions: !!block.options?.length,
        questionsCount: block.questions?.length || 0,
        optionsCount: block.options?.length || 0,
      });

      // Enhanced validation before deletion
      if (!block.id) {
        console.error('❌ Cannot delete block: Missing block ID');
        showError('ไม่สามารถลบได้ - ไม่พบ ID ของรายการ');
        return;
      }

      if (!block.assessmentId) {
        console.error('❌ Cannot delete block: Missing assessmentId');
        showError('ไม่สามารถลบได้ - ไม่พบ Assessment ID');
        return;
      }

      let deleteEndpoint: string;

      if (this.type === 'evaluate') {
        // For evaluate type, use the correct endpoint: /item-blocks/{id}
        deleteEndpoint = `/item-blocks/${block.id}`;
      } else {
        // For quiz type, use the original endpoint
        deleteEndpoint = `/item-blocks/${block.id}`;
      }

      console.log('🌐 Sending DELETE request to backend...', {
        endpoint: deleteEndpoint,
        blockId: block.id,
        blockType: block.type,
        timestamp: new Date().toISOString(),
      });

      const res = await axios.delete<ItemBlock>(deleteEndpoint);

      console.log('✅ Block deleted successfully from backend:', {
        deletedBlock: res.data,
        responseStatus: res.status,
        responseHeaders: res.headers,
        timestamp: new Date().toISOString(),
      });

      // Validate that the response contains the expected data
      if (!res.data || !res.data.id) {
        console.warn('⚠️ Backend response missing expected data:', res.data);
      } else if (res.data.id !== block.id) {
        console.warn('⚠️ Backend response ID mismatch:', {
          expectedId: block.id,
          receivedId: res.data.id,
        });
      }

      return res.data;
    } catch (error: unknown) {
      console.error('❌ Block deletion failed:', error);

      // Enhanced error logging
      if (error && typeof error === 'object' && 'response' in error) {
        const axiosError = error as {
          response?: { data?: unknown; status?: number; statusText?: string };
        };
        console.error('Delete error details:', {
          status: axiosError.response?.status,
          statusText: axiosError.response?.statusText,
          data: axiosError.response?.data,
          blockId: block.id,
          blockType: block.type,
          endpoint:
            this.type === 'evaluate'
              ? `/item-blocks/${block.id}`
              : `${this.path}/item-blocks/${block.id}`,
        });

        // Provide more specific error messages based on status code
        if (axiosError.response?.status === 404) {
          showError('ไม่พบรายการที่ต้องการลบ - อาจถูกลบไปแล้ว');
        } else if (axiosError.response?.status === 403) {
          showError('ไม่มีสิทธิ์ในการลบรายการนี้');
        } else if (axiosError.response?.status === 500) {
          showError('เกิดข้อผิดพลาดในระบบ - กรุณาลองใหม่อีกครั้ง');
        } else {
          showError('ไม่สามารถลบ Block ได้');
        }
      } else {
        console.error('Delete error (non-axios):', error);
        showError('ไม่สามารถลบ Block ได้');
      }

      return;
    }
  }

  /**
   * ATOMIC DUPLICATION: Creates a complete copy of an ItemBlock with all its content
   * This solves the race condition by performing all operations in a single backend transaction
   */
  async duplicateBlock(
    sourceBlockId: number,
    duplicateData: { assessmentId: number; sequence?: number; section?: number },
  ): Promise<ItemBlock | undefined> {
    try {
      console.log('🔄 AssessmentService.duplicateBlock called with:', {
        type: this.type,
        sourceBlockId,
        duplicateData,
      });

      const endpoint = `/item-blocks/${sourceBlockId}/duplicate`;
      const res = await axios.post<ItemBlock>(endpoint, duplicateData);

      console.log('✅ Block duplicated successfully:', {
        sourceBlockId,
        duplicatedBlock: res.data,
        responseStatus: res.status,
        timestamp: new Date().toISOString(),
      });

      globalStore.Loading();
      return res.data;
    } catch (error: unknown) {
      console.error('❌ Block duplication failed:', error);

      // Type guard for axios error
      if (error && typeof error === 'object' && 'response' in error) {
        const axiosError = error as { response?: { data?: unknown; status?: number } };
        console.error('Duplication error response:', axiosError.response?.data);
        console.error('Duplication error status:', axiosError.response?.status);
      }

      showError('ไม่สามารถคัดลอก Block ได้');
      return;
    }
  }

  // HeaderBody methods moved from HeaderBodyService
  async fetchHeaderBody(id: number): Promise<HeaderBody | undefined> {
    try {
      const res = await axios.get<HeaderBody>(`${this.headerBodyPath}/${id}`);
      return res.data;
    } catch {
      showError('ไม่สามารถโหลดข้อมูล Header Body ได้');
      return;
    }
  }

  async createHeaderBody(headerBody: Partial<HeaderBody>): Promise<HeaderBody | undefined> {
    try {
      const res = await axios.post<HeaderBody>(this.headerBodyPath, headerBody);
      globalStore.Loading();

      return res.data;
    } catch {
      showError('ไม่สามารถสร้าง Header Body ได้');
      return;
    }
  }

  async updateHeaderBody(
    id: number,
    headerBody: Partial<HeaderBody>,
  ): Promise<HeaderBody | undefined> {
    try {
      const res = await axios.patch<HeaderBody>(`${this.headerBodyPath}/${id}`, headerBody);
      globalStore.Loading();
      return res.data;
    } catch {
      showError('ไม่สามารถอัปเดต Header Body ได้');
      return;
    }
  }

  async deleteHeaderBody(id: number): Promise<HeaderBody | undefined> {
    try {
      const res = await axios.delete<HeaderBody>(`${this.headerBodyPath}/${id}`);

      return res.data;
    } catch {
      showError('ไม่สามารถลบ Header Body ได้');
      return;
    }
  }

  // Question methods - using /questions/{id} endpoint following headerBody pattern
  async updateQuestion(id: number, question: Partial<Question>): Promise<Question | undefined> {
    try {
      const res = await axios.patch<Question>(`/questions/${id}`, question);
      globalStore.Loading();
      return res.data;
    } catch {
      showError('ไม่สามารถอัปเดตคำถามได้');
      return;
    }
  }

  // Legacy method for backward compatibility - using /item-blocks/{id} endpoint with id and type
  async updateQuestionLegacy(
    itemBlockId: number,
    itemBlockType: string,
    question: Partial<Question>,
  ): Promise<Question | undefined> {
    try {
      const res = await axios.patch<Question>(`/item-blocks/${itemBlockId}`, {
        id: itemBlockId,
        type: itemBlockType,
        questionText: question.questionText,
      });
      globalStore.Loading();
      return res.data;
    } catch {
      showError('ไม่สามารถอัปเดตคำถามได้');
      return;
    }
  }

  // Option methods - using /item-blocks/{id} endpoint with id and type
  async updateOption(
    itemBlockId: number,
    itemBlockType: string,
    option: Partial<Option>,
  ): Promise<Option | undefined> {
    try {
      const res = await axios.patch<Option>(`/item-blocks/${itemBlockId}`, {
        id: itemBlockId,
        type: itemBlockType,
        optionText: option.optionText,
        score: option.value,
      });
      globalStore.Loading();
      return res.data;
    } catch {
      showError('ไม่สามารถอัปเデตตัวเลือกได้');
      return;
    }
  }

  async getQuizHeader(linkUrl: string) {
    try {
      const res = await axios.get<Assessment>(`/assessments/header/url/${linkUrl}`);
      return res.data;
    } catch (error) {
      console.error(`Error fetching quiz header for assessmentId ${linkUrl}:`, error);
      throw error;
    }
  }

  async getQuizByLinkUrl(linkUrl: string) {
    try {
      const res = await axios.get<Assessment>(`/assessments/by-link`, {
        params: { linkUrl },
      });
      return res.data;
    } catch (error) {
      console.error(`Error fetching quiz by linkUrl ${linkUrl}:`, error);
      throw error;
    }
  }
}
