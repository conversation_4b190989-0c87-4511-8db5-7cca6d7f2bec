import type { QTableProps } from 'quasar';
import { toRaw } from 'vue';
import {
  getAllSkillsApi,
  getOneSkill<PERSON>pi,
  createSkill<PERSON><PERSON>,
  updateSkill<PERSON>pi,
  deleteSkillApi,
} from 'src/apis/skills';
import type { AxiosError } from 'axios';
import type { Skill } from 'src/types/models';
import type { DataResponse } from 'src/types/data';
import type { UpdateSkillRequest } from 'src/types/api';
import { formatParams } from 'src/utils/formatter';
import { showSuccess, showError } from 'src/utils/notifications';

// Error message mapping for skill operations
const getSkillErrorMessage = (error: AxiosError, operation: string): string => {
  const status = error.response?.status;

  if (status === 403) {
    return 'ไม่มีสิทธิ์ในการจัดการทักษะ';
  }
  if (status === 404) {
    return 'ไม่พบทักษะที่ต้องการ';
  }
  if (status === 409) {
    return 'ชื่อทักษะนี้มีอยู่แล้ว';
  }
  if (status && status >= 500) {
    return 'เกิดข้อผิดพลาดจากเซิร์ฟเวอร์ กรุณาลองใหม่ภายหลัง';
  }

  // Default error messages based on operation
  const defaultMessages: Record<string, string> = {
    getAll: 'ดึงข้อมูลทักษะทั้งหมดล้มเหลว',
    getOne: 'ดึงข้อมูลทักษะล้มเหลว',
    create: 'สร้างทักษะไม่สำเร็จ',
    update: 'อัปเดตทักษะล้มเหลว',
    delete: 'ลบทักษะล้มเหลว',
  };

  return defaultMessages[operation] || 'เกิดข้อผิดพลาดในการจัดการทักษะ';
};

/**
 * Skills Service - Functional approach
 */
export const useSkillsService = {
  /**
   * Get all skills with pagination and search
   * @param pag - Pagination parameters
   * @param search - Search keyword
   * @returns Promise<DataResponse<Skill>>
   */
  async getAll(pag: QTableProps['pagination'], search?: string): Promise<DataResponse<Skill>> {
    try {
      formatParams(pag, search); // Apply formatting side effects
      const response = await getAllSkillsApi(pag, search);
      return response.data;
    } catch (error) {
      const axiosError = error as AxiosError;
      const errorMessage = getSkillErrorMessage(axiosError, 'getAll');
      showError(errorMessage);
      throw new Error(errorMessage);
    }
  },

  /**
   * Get a single skill by ID
   * @param id - Skill ID
   * @returns Promise<Skill>
   */
  async fetchOne(id: number): Promise<Skill> {
    try {
      const response = await getOneSkillApi(id);
      return response.data;
    } catch (error) {
      const axiosError = error as AxiosError;
      const errorMessage = getSkillErrorMessage(axiosError, 'getOne');
      showError(errorMessage);
      throw new Error(errorMessage);
    }
  },

  /**
   * Create a new skill
   * @param data - Skill data
   * @returns Promise<Skill>
   */
  async create(data: Skill): Promise<Skill> {
    try {
      let payload = { ...data };

      if (data.competencies) {
        const rawCompetencyIds = toRaw(data.competencies);

        const competencyIdsOnly = (rawCompetencyIds as (number | { id: number })[]).map((item) =>
          typeof item === 'object' && item !== null ? item.id : item,
        );

        delete payload.competencies;

        payload = {
          ...payload,
          competencyIds: competencyIdsOnly,
        };
      }

      const response = await createSkillApi(payload);
      showSuccess('สร้างทักษะสำเร็จ');
      return response.data;
    } catch (error) {
      const axiosError = error as AxiosError;
      const errorMessage = getSkillErrorMessage(axiosError, 'create');
      showError(errorMessage);
      throw new Error(errorMessage);
    }
  },

  /**
   * Update an existing skill
   * @param id - Skill ID
   * @param params - Updated skill data
   * @returns Promise<Skill>
   */
  async update(id: number, params: Partial<Skill>): Promise<Skill> {
    try {
      // Create clean update payload by filtering out unwanted fields
      const updateData: UpdateSkillRequest = {};

      // Copy only allowed fields
      if (params.name !== undefined) updateData.name = params.name;
      if (params.description !== undefined) updateData.description = params.description;
      if (params.career_type !== undefined) updateData.career_type = params.career_type;
      if (params.competencyIds !== undefined) updateData.competencyIds = params.competencyIds;

      const response = await updateSkillApi(id, updateData);
      showSuccess('อัปเดตทักษะสำเร็จ');
      return response.data;
    } catch (error) {
      const axiosError = error as AxiosError;
      const errorMessage = getSkillErrorMessage(axiosError, 'update');
      showError(errorMessage);
      throw new Error(errorMessage);
    }
  },

  /**
   * Delete a skill
   * @param id - Skill ID
   * @returns Promise<void>
   */
  async remove(id: number): Promise<void> {
    try {
      await deleteSkillApi(id);
      showSuccess('ลบทักษะเรียบร้อยแล้ว');
    } catch (error) {
      const axiosError = error as AxiosError;
      const errorMessage = getSkillErrorMessage(axiosError, 'delete');
      showError(errorMessage);
      throw new Error(errorMessage);
    }
  },
};
