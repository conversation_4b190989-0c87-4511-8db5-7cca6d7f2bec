import type { QTableProps } from 'quasar';
import type { AxiosError } from 'axios';
import type { DataResponse, FacultyUser, FacultyWithUserStatus } from 'src/types/data';
import type { User } from 'src/types/models';
import {
  createUser<PERSON><PERSON>,
  getUserByIdApi,
  update<PERSON>serApi,
  deleteUserApi,
  getUsersApi,
  getFacultiesWithUserStatusApi,
  updateUserFacultiesApi,
  updateUserFacultiesRoleApi,
  getFalcultieUserApi,
} from 'src/apis/user';
import { showSuccess, showError } from 'src/utils/notifications';

/**
 * Get error message for user operations
 */
function getUserErrorMessage(error: AxiosError, operation: string = 'ดำเนินการ'): string {
  const status = error.response?.status;
  const serverMessage = (error.response?.data as { message?: string })?.message;

  if (serverMessage) {
    return `${operation}ผู้ใช้ไม่สำเร็จ: ${serverMessage}`;
  }

  switch (status) {
    case 400:
      return `${operation}ผู้ใช้ไม่สำเร็จ: ข้อมูลที่ส่งมาไม่ถูกต้อง`;
    case 401:
      return `${operation}ผู้ใช้ไม่สำเร็จ: ไม่ได้รับอนุญาต กรุณาเข้าสู่ระบบใหม่`;
    case 403:
      return `${operation}ผู้ใช้ไม่สำเร็จ: ไม่มีสิทธิ์เข้าถึง`;
    case 404:
      return `${operation}ผู้ใช้ไม่สำเร็จ: ไม่พบผู้ใช้ที่ต้องการ`;
    case 409:
      return `${operation}ผู้ใช้ไม่สำเร็จ: มีผู้ใช้นี้อยู่ในระบบแล้ว`;
    case 422:
      return `${operation}ผู้ใช้ไม่สำเร็จ: ข้อมูลไม่ถูกต้องตามที่ระบบกำหนด`;
    case 500:
      return `${operation}ผู้ใช้ไม่สำเร็จ: เกิดข้อผิดพลาดภายในระบบ`;
    default:
      return `${operation}ผู้ใช้ไม่สำเร็จ: เกิดข้อผิดพลาดที่ไม่ทราบสาเหตุ`;
  }
}

// Service Functions with Business Logic and Error Handling
export async function createUser(dto: User): Promise<User> {
  try {
    const response = await createUserApi(dto);
    showSuccess('สร้างผู้ใช้สำเร็จ');
    return response.data;
  } catch (error) {
    const errorMessage = getUserErrorMessage(error as AxiosError, 'สร้าง');
    console.error('Error creating user:', error);
    showError(errorMessage);
    throw new Error(errorMessage);
  }
}

export async function getUserById(id: number): Promise<User> {
  try {
    const response = await getUserByIdApi(id);
    return response.data;
  } catch (error) {
    const errorMessage = getUserErrorMessage(error as AxiosError, 'ดึงข้อมูล');
    console.error('Error fetching user:', error);
    showError(errorMessage);
    throw new Error(errorMessage);
  }
}

export async function updateUser(id: number, data: Partial<User>): Promise<User> {
  try {
    const response = await updateUserApi(id, data);
    showSuccess('อัปเดตผู้ใช้สำเร็จ');
    return response.data;
  } catch (error) {
    const errorMessage = getUserErrorMessage(error as AxiosError, 'อัปเดต');
    console.error('Error updating user:', error);
    showError(errorMessage);
    throw new Error(errorMessage);
  }
}

export async function deleteUser(id: number): Promise<void> {
  try {
    await deleteUserApi(id);
    showSuccess('ลบผู้ใช้สำเร็จ');
  } catch (error) {
    const errorMessage = getUserErrorMessage(error as AxiosError, 'ลบ');
    console.error('Error deleting user:', error);
    showError(errorMessage);
    throw new Error(errorMessage);
  }
}

export async function getUsers(
  pagination?: QTableProps['pagination'],
  search?: string,
  facultyFilter?: number,
  roleFilter?: number,
): Promise<DataResponse<User>> {
  try {
    const response = await getUsersApi(pagination, search, facultyFilter, roleFilter);
    return response.data;
  } catch (error) {
    const errorMessage = getUserErrorMessage(error as AxiosError, 'ดึงรายการ');
    console.error('Error fetching users:', error);
    showError(errorMessage);
    throw new Error(errorMessage);
  }
}

export async function getFacultiesWithUserStatus(
  userId: number,
  pagination: QTableProps['pagination'],
  search?: string,
): Promise<DataResponse<FacultyWithUserStatus>> {
  try {
    const response = await getFacultiesWithUserStatusApi(userId, pagination, search);
    return response.data;
  } catch (error) {
    const errorMessage = getUserErrorMessage(error as AxiosError, 'ดึงข้อมูลคณะ');
    console.error('Error fetching faculties with user status:', error);
    showError(errorMessage);
    throw new Error(errorMessage);
  }
}

export async function updateUserFaculties(userId: number, facultyIds: number[]): Promise<unknown> {
  try {
    const response = await updateUserFacultiesApi(userId, facultyIds);
    showSuccess('อัปเดตคณะของผู้ใช้สำเร็จ');
    return response.data;
  } catch (error) {
    const errorMessage = getUserErrorMessage(error as AxiosError, 'อัปเดตคณะ');
    console.error('Error updating user faculties:', error);
    showError(errorMessage);
    throw new Error(errorMessage);
  }
}

export async function updateUserFacultiesRole(
  userId: number,
  roleIds: number[],
  facultyUserId: number,
): Promise<unknown> {
  try {
    const response = await updateUserFacultiesRoleApi(userId, roleIds, facultyUserId);
    showSuccess('อัปเดตบทบาทของผู้ใช้สำเร็จ');
    return response.data;
  } catch (error) {
    const errorMessage = getUserErrorMessage(error as AxiosError, 'อัปเดตบทบาท');
    console.error('Error updating user roles:', error);
    showError(errorMessage);
    throw new Error(errorMessage);
  }
}

export async function getFalcultieUser(
  userId: number,
  pagination: QTableProps['pagination'],
  search?: string,
): Promise<DataResponse<FacultyUser>> {
  try {
    const response = await getFalcultieUserApi(userId, pagination, search);
    console.log('Response Service', response.data);
    return response.data;
  } catch (error) {
    const errorMessage = getUserErrorMessage(error as AxiosError, 'ดึงข้อมูลผู้ใช้ในคณะ');
    console.error('Error fetching faculty users:', error);
    showError(errorMessage);
    throw new Error(errorMessage);
  }
}

// Composable Functions
export function useUserService() {
  return {
    createUser,
    getUserById,
    updateUser,
    deleteUser,
    getUsers,
    getFacultiesWithUserStatus,
    updateUserFaculties,
    getFalcultieUser,
  };
}
