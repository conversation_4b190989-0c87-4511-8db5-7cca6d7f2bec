import type { QTableProps } from 'quasar';
import type { AxiosError } from 'axios';
import type { DataResponse, FacultyUser } from 'src/types/data';

import { getFacultiesApi, getFalcultieRolesApi, getUserByFacultyApi } from 'src/apis/faculty';
import { showError } from 'src/utils/notifications';
import type { Faculty } from 'src/types';

/**
 * Get error message for faculty operations
 */
function getFacultyErrorMessage(error: AxiosError, operation: string = 'ดำเนินการ'): string {
  const status = error.response?.status;
  const serverMessage = (error.response?.data as { message?: string })?.message;

  if (serverMessage) {
    return `${operation}คณะไม่สำเร็จ: ${serverMessage}`;
  }

  switch (status) {
    case 400:
      return `${operation}คณะไม่สำเร็จ: ข้อมูลที่ส่งมาไม่ถูกต้อง`;
    case 401:
      return `${operation}คณะไม่สำเร็จ: ไม่ได้รับอนุญาต กรุณาเข้าสู่ระบบใหม่`;
    case 403:
      return `${operation}คณะไม่สำเร็จ: ไม่มีสิทธิ์เข้าถึง`;
    case 404:
      return `${operation}คณะไม่สำเร็จ: ไม่พบคณะที่ต้องการ`;
    case 409:
      return `${operation}คณะไม่สำเร็จ: มีคณะนี้อยู่ในระบบแล้ว`;
    case 422:
      return `${operation}คณะไม่สำเร็จ: ข้อมูลไม่ถูกต้องตามที่ระบบกำหนด`;
    case 500:
      return `${operation}คณะไม่สำเร็จ: เกิดข้อผิดพลาดภายในระบบ`;
    default:
      return `${operation}คณะไม่สำเร็จ: เกิดข้อผิดพลาดที่ไม่ทราบสาเหตุ`;
  }
}

// Service Functions with Business Logic and Error Handling
export async function getFacultiesList(
  pagination: QTableProps['pagination'],
): Promise<DataResponse<Faculty>> {
  try {
    const response = await getFacultiesApi(pagination);
    return response.data;
  } catch (error) {
    const errorMessage = getFacultyErrorMessage(error as AxiosError, 'ดึงรายการ');
    console.error('Error fetching faculties:', error);
    showError(errorMessage);
    throw new Error(errorMessage);
  }
}

export async function getFacultyRoles(
  pagination: QTableProps['pagination'],
  search?: string,
  facultyId?: number,
  roleId?: number,
): Promise<DataResponse<FacultyUser>> {
  try {
    const response = await getFalcultieRolesApi(pagination, search, facultyId, roleId);
    return response.data;
  } catch (error) {
    const errorMessage = getFacultyErrorMessage(error as AxiosError, 'ดึงบทบาท');
    console.error('Error fetching faculty roles:', error);
    showError(errorMessage);
    throw new Error(errorMessage);
  }
}

export async function getUserByFaculty(
  pagination: QTableProps['pagination'],
  search?: string,
  facultyId?: number,
): Promise<DataResponse<FacultyUser>> {
  try {
    const response = await getUserByFacultyApi(pagination, search, facultyId);
    return response.data;
  } catch (error) {
    const errorMessage = getFacultyErrorMessage(error as AxiosError, 'ดึงผู้ใช้งาน');
    console.error('Error fetching user by faculty:', error);
    showError(errorMessage);
    throw new Error(errorMessage);
  }
}

// TODO: Add CRUD operations when API endpoints are available
// These functions are placeholders for future implementation

// export async function createFaculty(dto: Partial<Faculty>): Promise<Faculty> {
//   try {
//     // TODO: Implement when createFacultyApi is available
//     // const response = await createFacultyApi(dto);
//     // showSuccess('สร้างคณะสำเร็จ');
//     // return response.data;

//     console.log('Creating faculty with data:', dto);
//     throw new Error('สร้างคณะ: ฟังก์ชันนี้ยังไม่พร้อมใช้งาน');
//   } catch (error) {
//     const errorMessage = getFacultyErrorMessage(error as AxiosError, 'สร้าง');
//     console.error('Error creating faculty:', error);
//     showError(errorMessage);
//     throw new Error(errorMessage);
//   }
// }

// export async function getFacultyById(id: number): Promise<Faculty> {
//   try {
//     // TODO: Implement when getFacultyByIdApi is available
//     // const response = await getFacultyByIdApi(id);
//     // return response.data;

//     console.log('Fetching faculty with ID:', id);
//     throw new Error('ดึงข้อมูลคณะ: ฟังก์ชันนี้ยังไม่พร้อมใช้งาน');
//   } catch (error) {
//     const errorMessage = getFacultyErrorMessage(error as AxiosError, 'ดึงข้อมูล');
//     console.error('Error fetching faculty:', error);
//     showError(errorMessage);
//     throw new Error(errorMessage);
//   }
// }

// export async function updateFaculty(id: number, data: Partial<Faculty>): Promise<Faculty> {
//   try {
//     // TODO: Implement when updateFacultyApi is available
//     // const response = await updateFacultyApi(id, data);
//     // showSuccess('อัปเดตคณะสำเร็จ');
//     // return response.data;

//     console.log('Updating faculty with ID:', id, 'and data:', data);
//     throw new Error('อัปเดตคณะ: ฟังก์ชันนี้ยังไม่พร้อมใช้งาน');
//   } catch (error) {
//     const errorMessage = getFacultyErrorMessage(error as AxiosError, 'อัปเดต');
//     console.error('Error updating faculty:', error);
//     showError(errorMessage);
//     throw new Error(errorMessage);
//   }
// }

// export async function deleteFaculty(id: number): Promise<void> {
//   try {
//     // TODO: Implement when deleteFacultyApi is available
//     // await deleteFacultyApi(id);
//     // showSuccess('ลบคณะสำเร็จ');

//     console.log('Deleting faculty with ID:', id);
//     throw new Error('ลบคณะ: ฟังก์ชันนี้ยังไม่พร้อมใช้งาน');
//   } catch (error) {
//     const errorMessage = getFacultyErrorMessage(error as AxiosError, 'ลบ');
//     console.error('Error deleting faculty:', error);
//     showError(errorMessage);
//     throw new Error(errorMessage);
//   }
// }

// Composable Functions
export function useFacultyService() {
  return {
    getFacultiesList,
    getFacultyRoles,
    getUserByFaculty,
  };
}
