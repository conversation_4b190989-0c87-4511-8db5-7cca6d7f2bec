import type { IndividualDevelopmentPlan, IndividualDevelopmentPlanForm } from 'src/types/models';

// Mock data for development
const mockData: IndividualDevelopmentPlan[] = [
  {
    id: 1,
    employee_name: 'สมชาย ใจดี',
    employee_id: 'EMP001',
    department: 'เทคโนโลยีสารสนเทศ',
    position: 'นักพัฒนาระบบ',
    development_goals: 'พัฒนาทักษะ Full Stack Development',
    target_year: 2025,
    status: 'active',
    created_at: '2024-01-15T10:30:00Z',
  },
  {
    id: 2,
    employee_name: 'สุดารัตน์ มีความสุข',
    employee_id: 'EMP002',
    department: 'การตลาด',
    position: 'นักการตลาด',
    development_goals: 'พัฒนาทักษะ Digital Marketing และ Data Analytics',
    target_year: 2025,
    status: 'active',
    created_at: '2024-01-20T14:15:00Z',
  },
  {
    id: 3,
    employee_name: 'วิทยา ขยันเรียน',
    employee_id: 'EMP003',
    department: 'ทรัพยากรมนุษย์',
    position: 'ผู้จัดการทรัพยากรมนุษย์',
    development_goals: 'พัฒนาทักษะการบริหารจัดการ และ Leadership',
    target_year: 2024,
    status: 'completed',
    created_at: '2024-01-10T09:00:00Z',
  },
  {
    id: 4,
    employee_name: 'อนุชา ทำดี',
    employee_id: 'EMP004',
    department: 'การเงิน',
    position: 'นักวิเคราะห์การเงิน',
    development_goals: 'พัฒนาทักษะ Financial Modeling และ Risk Management',
    target_year: 2025,
    status: 'active',
    created_at: '2024-02-01T11:45:00Z',
  },
  {
    id: 5,
    employee_name: 'สมพร รักการเรียนรู้',
    employee_id: 'EMP005',
    department: 'วิจัยและพัฒนา',
    position: 'นักวิจัย',
    development_goals: 'พัฒนาทักษะ Machine Learning และ AI',
    target_year: 2026,
    status: 'inactive',
    created_at: '2024-01-25T16:20:00Z',
  },
];

// Pagination interface
export interface PaginationParams {
  page: number;
  rowsPerPage: number;
  sortBy?: string;
  descending?: boolean;
}

// API Response interface
export interface PaginatedResponse<T> {
  data: T[];
  total: number;
  page: number;
  rowsPerPage: number;
}

// Individual Development Plan Service
export class IndividualDevelopmentPlanService {
  // Simulate API delay
  private async delay(ms: number = 500): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  // Get all plans with pagination and search
  async getAll(
    pagination: PaginationParams, 
    searchKeyword: string = ''
  ): Promise<PaginatedResponse<IndividualDevelopmentPlan>> {
    await this.delay();

    // Filter by search keyword
    let filteredData = mockData;
    if (searchKeyword) {
      const keyword = searchKeyword.toLowerCase();
      filteredData = mockData.filter(item => 
        item.employee_name.toLowerCase().includes(keyword) ||
        item.employee_id.toLowerCase().includes(keyword) ||
        item.department.toLowerCase().includes(keyword) ||
        item.position.toLowerCase().includes(keyword) ||
        item.development_goals.toLowerCase().includes(keyword)
      );
    }

    // Sort data
    if (pagination.sortBy) {
      filteredData.sort((a, b) => {
        const sortBy = pagination.sortBy as keyof IndividualDevelopmentPlan;
        const aVal = a[sortBy];
        const bVal = b[sortBy];
        
        // Handle null/undefined values
        if (aVal == null && bVal == null) return 0;
        if (aVal == null) return pagination.descending ? 1 : -1;
        if (bVal == null) return pagination.descending ? -1 : 1;
        
        if (aVal < bVal) return pagination.descending ? 1 : -1;
        if (aVal > bVal) return pagination.descending ? -1 : 1;
        return 0;
      });
    }

    // Pagination
    const start = (pagination.page - 1) * pagination.rowsPerPage;
    const end = start + pagination.rowsPerPage;
    const paginatedData = filteredData.slice(start, end);

    return {
      data: paginatedData,
      total: filteredData.length,
      page: pagination.page,
      rowsPerPage: pagination.rowsPerPage,
    };
  }

  // Get plan by ID
  async getById(id: number): Promise<IndividualDevelopmentPlan | null> {
    await this.delay();
    
    const plan = mockData.find(item => item.id === id);
    if (!plan) return null;

    // Add mock additional data for view
    return {
      ...plan,
      development_activities: 'เข้าร่วมการอบรม React.js, Node.js, Express.js และ Database Design รวมถึงการศึกษาเพิ่มเติมเกี่ยวกับ Cloud Computing และ DevOps',
      success_criteria: 'สามารถพัฒนาแอพพลิเคชันเว็บได้ครบ End-to-End ภายใน 6 เดือน และผ่านการประเมินโปรเจค Capstone',
      updated_at: '2024-02-20T14:15:00Z',
    };
  }

  // Create new plan
  async create(planForm: IndividualDevelopmentPlanForm): Promise<IndividualDevelopmentPlan> {
    await this.delay(1000);
    
    const newPlan: IndividualDevelopmentPlan = {
      ...planForm,
      id: Math.max(...mockData.map(p => p.id)) + 1,
      created_at: new Date().toISOString(),
    };
    
    mockData.push(newPlan);
    return newPlan;
  }

  // Update existing plan
  async update(id: number, planForm: IndividualDevelopmentPlanForm): Promise<IndividualDevelopmentPlan> {
    await this.delay(1000);
    
    const index = mockData.findIndex(item => item.id === id);
    if (index === -1) {
      throw new Error('Plan not found');
    }

    const existingPlan = mockData[index];
    if (!existingPlan) {
      throw new Error('Plan not found');
    }

    const updatedPlan: IndividualDevelopmentPlan = {
      ...planForm,
      id,
      created_at: existingPlan.created_at,
      updated_at: new Date().toISOString(),
    };
    
    mockData[index] = updatedPlan;
    return updatedPlan;
  }

  // Delete plan
  async delete(id: number): Promise<void> {
    await this.delay(300);
    
    const index = mockData.findIndex(item => item.id === id);
    if (index === -1) {
      throw new Error('Plan not found');
    }
    
    mockData.splice(index, 1);
  }
}

// Export singleton instance
export const individualDevelopmentPlanService = new IndividualDevelopmentPlanService();
