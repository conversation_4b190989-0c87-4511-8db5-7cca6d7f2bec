import {
  <PERSON>,
  <PERSON>,
  Param,
  Body,
  ParseIntPipe,
  Get,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiParam,
  ApiBearerAuth,
  ApiResponse,
} from '@nestjs/swagger';
import { UpdateQuizSettingsDto } from '../dto/updates/update-quiz-settings.dto';
import { Assessment } from '../entities/assessment.entity';
import { AssessmentsService } from '../services/assessments.service';
import { RequirePermissions } from 'src/common/decorators/permissions.decorator';

@ApiTags('Quiz Settings')
@ApiBearerAuth()
@Controller('assessments/:id/settings')
export class QuizSettingsController {
  constructor(private readonly assessmentsService: AssessmentsService) {}

  @Get()
  @ApiOperation({ 
    summary: 'Get quiz settings',
    description: 'ดึงการตั้งค่าของแบบทดสอบ'
  })
  @ApiParam({ 
    name: 'id', 
    description: 'รหัสของแบบทดสอบ', 
    type: Number 
  })
  @ApiResponse({
    status: 200,
    description: 'ข้อมูลการตั้งค่าแบบทดสอบ',
    type: Assessment,
  })
  @RequirePermissions('manage_own_surveys')
  async getQuizSettings(
    @Param('id', ParseIntPipe) id: number,
  ): Promise<Partial<Assessment>> {
    const { assessment } = await this.assessmentsService.findOne(id, { page: 1, limit: 1 });
    
    // Return only settings-related fields
    return {
      id: assessment.id,
      name: assessment.name,
      startAt: assessment.startAt,
      endAt: assessment.endAt,
      timeout: assessment.timeout,
      submitLimit: assessment.submitLimit,
      passRatio: assessment.passRatio,
      isPrototype: assessment.isPrototype,
      type: assessment.type,
    };
  }

  @Patch()
  @ApiOperation({ 
    summary: 'Update quiz settings',
    description: 'อัปเดตการตั้งค่าของแบบทดสอบ (เฉพาะฟิลด์ที่เกี่ยวข้องกับการตั้งค่า)'
  })
  @ApiParam({ 
    name: 'id', 
    description: 'รหัสของแบบทดสอบ', 
    type: Number 
  })
  @ApiResponse({
    status: 200,
    description: 'การตั้งค่าแบบทดสอบถูกอัปเดตเรียบร้อยแล้ว',
    type: Assessment,
  })
  @RequirePermissions('manage_own_surveys')
  async updateQuizSettings(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateQuizSettingsDto: UpdateQuizSettingsDto,
  ): Promise<Assessment> {
    // Cast to UpdateAssessmentDto for compatibility with existing service
    const settingsUpdate = updateQuizSettingsDto as any;

    return this.assessmentsService.update(id, settingsUpdate);
  }
}
