import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ItemBlocksService } from './item-blocks.service';
import { ItemBlock } from '../entities/item-block.entity';
import { Assessment } from '../entities/assessment.entity';
import { Question } from '../entities/question.entity';
import { Option } from '../entities/option.entity';
import { HeaderBody } from '../entities/header-body.entity';
import { ImageBody } from '../entities/image-body.entity';
import { QuestionsModule } from '../questions/questions.module';
import { ItemBlocksController } from './item-blocks.controller';
import { HeaderBodiesModule } from '../header-bodies/header-bodies.module';
import { ImageBodiesModule } from '../image-bodies/image-bodies.module';
import { FileUploadService } from '../utils/file-upload.service';
import { OptionsModule } from '../options/options.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      ItemBlock,
      Assessment,
      Question,
      Option,
      HeaderBody,
      ImageBody,
    ]),

    QuestionsModule,
    HeaderBodiesModule,
    ImageBodiesModule,
    OptionsModule,
  ],
  controllers: [ItemBlocksController],
  providers: [
    ItemBlocksService,
    FileUploadService,
  ],
  exports: [
    ItemBlocksService,
    FileUploadService,
  ],
})
export class ItemBlocksModule {}
