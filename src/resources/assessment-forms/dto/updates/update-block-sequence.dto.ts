import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>Int, Min } from 'class-validator';
import { Type } from 'class-transformer';
import { ApiProperty } from '@nestjs/swagger';

class ItemBlockSequenceDto {
  @ApiProperty({ example: 1 })
  @IsInt()
  @Min(1)
  id: number;

  @ApiProperty({ example: 1 })
  @IsInt()
  @Min(1)
  sequence: number;
}

export class BulkUpdateItemBlockSequencesDto {
  @ApiProperty({
    type: [ItemBlockSequenceDto],
    description: 'Array of item blocks with their new sequence numbers',
    example: [
      { id: 1, sequence: 1 },
      { id: 2, sequence: 2 },
      { id: 3, sequence: 3 },
    ],
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ItemBlockSequenceDto)
  itemBlocks: ItemBlockSequenceDto[];
}
