// file management controller file

import { <PERSON>, Body, Post } from '@nestjs/common';
import { FileUploadService } from './file-upload.service';
import { ApiBody, ApiTags } from '@nestjs/swagger';
import { IsString, IsNotEmpty } from 'class-validator';

export class PublicFilePathDto {
  @IsString()
  @IsNotEmpty()
  imagePath: string;
}

@ApiTags('File Upload')
@Controller('file-upload')
export class FileUploadController {
  constructor(private readonly fileUploadService: FileUploadService) {}

  // get public file by post path body
  @Post('public-file-path')
  @ApiBody({ type: PublicFilePathDto })
  async getPublicFile(@Body() imagePath: PublicFilePathDto) {
    return this.fileUploadService.processImagePath(imagePath.imagePath);
  }

}
