import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseInterceptors,
  UseGuards,
  BadRequestException,
} from '@nestjs/common';
import { HeaderBodiesService } from './header-bodies.service';
import {
  ApiTags,
  ApiOperation,
  ApiConsumes,
  ApiBody,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { AnyFilesInterceptor } from '@nestjs/platform-express';
import type { CreateHeaderBodyDto } from '../dto/creates/create-header-body.dto';
import { UpdateHeaderBodyDto } from '../dto/updates/update-header-body.dto';
import { AuthGuard } from 'src/auth/auth.guard';
import { plainToInstance } from 'class-transformer';
import { validate } from 'class-validator';
import { FileInterceptor } from '@nestjs/platform-express';
import { diskStorage } from 'multer';
import { extname } from 'path';
import { v4 as uuidv4 } from 'uuid';
import { UploadedFile } from '@nestjs/common';

@ApiTags('Header-Bodies')
@ApiBearerAuth()
@UseGuards(AuthGuard)
@Controller('header-bodies')
export class HeaderBodiesController {
  constructor(private readonly headerBodiesService: HeaderBodiesService) {}

  @Post()
  @ApiOperation({
    summary: 'สร้าง Header Bodyใหม่',
    description: 'สร้าง Header Body ใหม่สำหรับ (Evaluate)',
  })
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    description: 'ข้อมูลสำหรับสร้าง Header Body ใหม่',
    schema: {
      type: 'object',
      properties: {
        itemBlockId: { type: 'integer', example: 1 },
        title: { type: 'string', example: 'หัวข้อที่ 1' },
        description: { type: 'string', example: 'คำอธิบายหัวข้อที่ 1' },
      },
      required: ['itemBlockId'],
    },
  })
  @UseInterceptors(AnyFilesInterceptor())
  create(@Body() createHeaderBodyDto: CreateHeaderBodyDto) {
    return this.headerBodiesService.createHeaderBody(
      createHeaderBodyDto.itemBlockId,
    );
  }

  @Get()
  findAll() {
    return this.headerBodiesService.findAll();
  }

  @Get(':id')
  findOne(@Param('id') id: string) {
    return this.headerBodiesService.findOne(+id);
  }

  @Patch(':id')
  @ApiOperation({
    summary: 'อัปเดตHeader Body',
    description: 'อัปเดตHeader Body (Evaluate) ตาม template ที่กำหนด',
  })
  @ApiConsumes('multipart/form-data')
  @ApiBody({ type: UpdateHeaderBodyDto })
  @UseInterceptors(
    FileInterceptor('imagePath', {
      storage: diskStorage({
        destination: './uploaded_file',
        filename: (req, file, cb) => {
          const ext = extname(file.originalname);
          const name = uuidv4();
          cb(null, name + ext);
        },
      }),
    }),
  )
  update(
    @Param('id') id: string,
    @Body() updateHeaderBodyDto: UpdateHeaderBodyDto,
    @UploadedFile() file?: Express.Multer.File,
  ) {
    return this.headerBodiesService.update(+id, updateHeaderBodyDto);
  }

  @Delete(':id')
  remove(@Param('id') id: string) {
    return this.headerBodiesService.remove(+id);
  }
}
