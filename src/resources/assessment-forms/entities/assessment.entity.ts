import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  Column,
  ManyToOne,
  JoinColumn,
  OneToMany,
  CreateDateColumn,
} from 'typeorm';
import { AssessmentType } from '../enums/assessment-type.enum';
import { Program } from '../../programs/entities/program.entity';
import { ItemBlock } from './item-block.entity';
import { Submission } from './submission.entity';
import { User } from 'src/resources/users/entities/user.entity';

@Entity('ASM_T_ASSESSMENTS', {
  comment: 'ตารางแบบประเมินผล (Quiz / Evaluate)',
})
export class Assessment {
  @PrimaryGeneratedColumn({ name: 'ASM_ID', comment: 'รหัสแบบประเมิน' })
  id: number;

  @Column({ name: 'ASM_NAME', comment: 'ชื่อแบบประเมิน' })
  name: string;

  @Column({
    name: 'ASM_TYPE',
    type: 'enum',
    enum: AssessmentType,
    default: AssessmentType.EVALUATE,
    comment: 'ประเภทของแบบประเมิน (Quiz / Evaluate)',
  })
  type: AssessmentType;

  @CreateDateColumn({ name: 'ASM_CREATED_AT', type: 'datetime' , comment: 'วันที่สร้างแบบประเมิน'})
  createdAt: Date;

  @Column({ name: 'ASM_START_AT', type: 'datetime', nullable: true , comment: 'วันที่เริ่มต้นการประเมิน' })
  startAt: Date | null;

  @Column({ name: 'ASM_END_AT', type: 'datetime', nullable: true , comment: 'วันที่สิ้นสุดการประเมิน' })
  endAt: Date | null;

  @Column({
    name: 'ASM_SUBMIT_LIMIT',
    default: -1,
    comment: 'จำนวนครั้งที่สามารถส่งแบบประเมินได้ (-1 = ไม่จำกัด)',
  })
  submitLimit: number;

  @Column({ name: 'ASM_LINK_URL', nullable: true , comment: 'ลิงก์ URL สำหรับแบบประเมิน' })
  linkURL: string;

  @Column({ name: 'ASM_RESPONSE_EDIT', default: false , comment: 'อนุญาตให้แก้ไขการตอบแบบประเมินได้หรือไม่' })
  responseEdit: boolean;

  @Column({ name: 'ASM_STATUS', default: false , comment: 'สถานะของแบบประเมิน' })
  status: boolean;

  @Column({ name: 'ASM_TOTAL_SCORE', default: 0 , comment: 'คะแนนรวมของแบบประเมิน' })
  totalScore: number;

  @Column({
    name: 'ASM_TIMEOUT',
    default: 0,
    comment: 'ระยะเวลาที่ต้องการส่งแบบ หน่วยเป็น second',
  })
  timeout: number;

  @Column({
    name: 'ASM_PASS_RATIO',
    type: 'decimal',
    precision: 3,
    scale: 1,
    default: 0.5,
    comment: 'อัตราส่วนการผ่านแบบประเมิน (0.0 - 1.0)',
  })
  passRatio: number;

  @Column({ name: 'ASM_IS_PROTOTYPE', default: false , comment: 'เป็นแบบประเมินต้นแบบหรือไม่' })
  isPrototype: boolean;

  @ManyToOne(() => User, (user) => user.assessments)
  @JoinColumn({ name: 'USR_ID' })
  creator: User;

  @Column({ name: 'PRG_ID' , comment: 'รหัสโปรแกรมที่แบบประเมินนี้เกี่ยวข้อง' })
  programId: number;

  @ManyToOne(() => Program, (program) => program.assessments)
  @JoinColumn({ name: 'PRG_ID' })
  program: Program;

  @OneToMany(() => ItemBlock, (itemBlock) => itemBlock.assessment, {
    cascade: true,
  })
  itemBlocks: ItemBlock[];

  @OneToMany(() => Submission, (submission) => submission.assessment, {
    cascade: true,
  })
  submissions: Submission[];
}
