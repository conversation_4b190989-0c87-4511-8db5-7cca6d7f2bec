import { Type } from 'class-transformer';
import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  ManyToOne,
  JoinColumn,
  OneToMany,
} from 'typeorm';
import { ItemBlock } from './item-block.entity';
import { Response } from './response.entity';

@Entity('ASM_T_QUESTIONS', {
  comment: 'ตารางคำถามในแบบประเมินผล (Quiz / Evaluate Form)',
})
export class Question {
  @PrimaryGeneratedColumn({ name: 'QST_ID', comment: 'รหัสคำถาม' })
  id: number;

  @Column({ name: 'IBL_ID', comment: 'รหัสบล็อกของคำถาม' })
  itemBlockId: number;

  @Column({ name: 'QST_QUESTION_TEXT', type: 'text', comment: 'ข้อความคำถาม' })
  questionText: string;

  @Column({ name: 'QST_IMAGE_PATH', nullable: true, type: 'text', comment: 'ที่อยู่รูปภาพประกอบคำถาม' })
  imagePath: string;

  @Column({ name: 'QST_IMAGE_WIDTH', nullable: true, comment: 'ความกว้างรูปภาพ' })
  imageWidth: number | null;

  @Column({ name: 'QST_IMAGE_HEIGHT', nullable: true, comment: 'ความสูงรูปภาพ' })
  imageHeight: number | null;

  @Column({ name: 'QST_IS_HEADER', default: false, comment: 'เป็นหัวข้อหรือไม่' })
  isHeader: boolean;

  @Column({ name: 'QST_SEQUENCE', comment: 'ลำดับคำถาม' })
  sequence: number;

  @Column({ name: 'QST_SIZE_LIMIT', nullable: true, default: null , comment: 'ขนาดไฟล์สูงสุดที่อนุญาต (ใน KB)' })
  sizeLimit: number;

  @Column({ name: 'QST_ACCEPT_FILE', nullable: true, default: null , comment: 'ประเภทไฟล์ที่อนุญาต' })
  acceptFile: string;

  @Column({ name: 'QST_UPLOAD_LIMIT', nullable: true, default: null , comment: 'จำนวนไฟล์ที่อนุญาตให้ส่ง' })
  uploadLimit: number;

  @ManyToOne(() => ItemBlock, (itemBlock) => itemBlock.questions, {
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'IBL_ID' })
  itemBlock: ItemBlock;

  @OneToMany(() => Response, (response) => response.question, {
    cascade: true,
  })
  responses: Response[];

  @Column({ nullable: true , comment: 'คะแนนของคำถาม' })
  score: number | null;
}
