import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedC<PERSON>umn,
  Column,
  ManyToOne,
  JoinColumn,
  OneToMany,
  OneToOne,
} from 'typeorm';
import { ItemBlockType } from '../enums/item-block-type.enum';
import { Assessment } from './assessment.entity';
import { Option } from './option.entity';
import { Question } from './question.entity';
import { HeaderBody } from './header-body.entity';
import { ImageBody } from './image-body.entity';

@Entity('ASM_T_ITEM_BLOCKS', {
  comment: 'ตารางบล็อกไอเท็มในแบบประเมินผล (Quiz / Evaluate Form)',
})
export class ItemBlock {
  @PrimaryGeneratedColumn({ name: 'IBL_ID', comment: 'รหัสบล็อก' })
  id: number;

  @Column({ name: 'IBL_SEQUENCE', comment: 'ลำดับบล็อก' })
  sequence: number;

  @Column({ name: 'IBL_SECTION', default: 1, comment: 'section ของบล็อก' })
  section: number;

  @Column({
    name: 'IBL_TYPE',
    type: 'enum',
    enum: ItemBlockType,
    comment: 'ประเภทบล็อก'
  })
  type: ItemBlockType;

  @Column({ name: 'IBL_IS_REQUIRED', default: false, comment: 'ต้องกรอกหรือไม่' })
  isRequired: boolean;

  @Column({ name: 'ASM_ID', nullable: true, comment: 'รหัสแบบประเมิน' })
  assessmentId: number;

  @ManyToOne(() => Assessment, (assessment) => assessment.itemBlocks, {
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'ASM_ID' })
  assessment: Assessment;

  @OneToOne(() => HeaderBody, (headerBody) => headerBody.itemBlock, {
    cascade: true,
  })
  headerBody: HeaderBody;

  @OneToOne(() => ImageBody, (imageBody) => imageBody.itemBlock, {
    cascade: true,
    nullable: true,
  })
  imageBody: ImageBody | null;

  @OneToMany(() => Question, (question) => question.itemBlock, {
    cascade: true,
  })
  questions: Question[];

  @OneToMany(() => Option, (option) => option.itemBlock, {
    cascade: true,
  })
  options: Option[];
}
