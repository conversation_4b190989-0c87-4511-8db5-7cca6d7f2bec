import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  Column,
  ManyToOne,
  JoinColumn,
  OneToMany,
} from 'typeorm';
import { Assessment } from './assessment.entity';
import { Response } from './response.entity';
import { User } from 'src/resources/users/entities/user.entity';

@Entity('ASM_T_SUBMISSIONS', {comment: 'ตารางการส่งแบบประเมินผล (Quiz / Evaluate Form)'})
export class Submission {
  @PrimaryGeneratedColumn({ name: 'SBM_ID', comment: 'รหัสการส่งแบบประเมิน' })
  id: number;

  @Column({
    name: 'SBM_START_AT',
    type: 'datetime',
    comment: 'วันที่เริ่มทำแบบประเมิน',
  })
  startAt: Date;

  @Column({
    name: 'SBM_END_AT',
    type: 'datetime',
    comment: 'วันที่หมดเวลาทำแบบประเมิน',
    nullable: true,
  })
  endAt: Date;

  @Column({
    name: 'SBM_SUBMIT_AT',
    type: 'datetime',
    nullable: true,
    comment: 'วันที่ส่งแบบประเมิน',
  })
  submitAt: Date | null;

  @Column({ name: 'USR_ID' , comment: 'รหัสผู้ใช้ที่ส่งแบบประเมิน' })
  userId: number;

  @Column({ name: 'ASM_ID' , comment: 'รหัสแบบประเมินที่เกี่ยวข้อง' })
  assessmentId: number;

  @ManyToOne(() => User, (user) => user.submissions)
  @JoinColumn({ name: 'USR_ID' })
  user: User;

  @ManyToOne(() => Assessment, (assessment) => assessment.submissions, {
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'ASM_ID' })
  assessment: Assessment;

  @OneToMany(() => Response, (response) => response.submission, {
    cascade: true,
  })
  responses: Response[];
}
