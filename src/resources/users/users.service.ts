import {
  Injectable,
  NotFoundException,
  ConflictException,
  BadRequestException,
  InternalServerErrorException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, In, Like, Any, FindManyOptions } from 'typeorm';
import { User } from './entities/user.entity';
import { CreateUserDto } from './dto/create-user.dto';
import {
  UpdateUserDto,
  UpdateUserPasswordDto,
  UpdateUserRolesDto,
} from './dto/update-user.dto';
import { Role } from 'src/resources/roles/entities/role.entity';
import { Faculty } from 'src/resources/faculties/entities/faculty.entity';
import { FacultyUsers } from 'src/resources/faculties/entities/faculty-user.entity';
import { Career } from 'src/resources/individual-develop-plans/careers/entities/careers.entity';
import { CareerRecords } from 'src/resources/individual-develop-plans/career-records/entites/career-records.entity';
import * as bcrypt from 'bcrypt';
import { instanceTo<PERSON>lain } from 'class-transformer';
import { DataParams, DataResponse, createPaginatedResponse } from 'src/types';
import { FacultyUserRoles } from 'src/resources/faculties/entities/faculty-user-role.entity';
const SALT_ROUNDS = 10; // For bcrypt

export interface UserListItem {
  id: number;
  firstName: string;
  lastName: string;
  faculties: {
    id: number;
    faculty: any;
    roles: any[];
  }[];
}

export interface FacultyWithUserStatus {
  id: number;
  nameTh: string;
  nameEn: string;
  isUserInFaculty: boolean;
}

export interface FacultyUserItem {
  id: number;
  name: string;
  email: string;
  firstName: string;
  lastName: string;
  code: string;
  facultyId: number;
  facultyName: string;
  roles: {
    id: number;
    name: string;
    description: string;
  }[];
}

@Injectable()
export class UsersService {
  constructor(
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    @InjectRepository(Role)
    private readonly roleRepository: Repository<Role>,
    @InjectRepository(Faculty)
    private readonly facultyRepository: Repository<Faculty>,
    @InjectRepository(FacultyUsers)
    private readonly facultyUsersRepository: Repository<FacultyUsers>,
    @InjectRepository(FacultyUserRoles)
    private readonly facultyUserRolesRepository: Repository<FacultyUserRoles>,
    @InjectRepository(Career)
    private readonly careerRepository: Repository<Career>,
    @InjectRepository(CareerRecords)
    private readonly careerRecordsRepository: Repository<CareerRecords>,
  ) {}

  // async create(createUserDto: CreateUserDto): Promise<Omit<User, 'password'>> {
  //   const { email, password, roleIds, ...userData } = createUserDto;

  //   const existingUser = await this.userRepository.findOneBy({ email });
  //   if (existingUser) {
  //     throw new ConflictException(`User with email "${email}" already exists.`);
  //   }

  //   const hashedPassword = await bcrypt.hash(password, SALT_ROUNDS);
  //   if (!hashedPassword) {
  //     throw new InternalServerErrorException('Failed to hash password.');
  //   }

  //   const user = this.userRepository.create({
  //     ...userData,
  //     email,
  //     password: hashedPassword,
  //   });

  //   if (roleIds && roleIds.length > 0) {
  //     const roles = await this.roleRepository.findBy({ id: In(roleIds) });
  //     if (roles.length !== roleIds.length) {
  //       const foundRoleIdsSet = new Set(roles.map((role) => role.id));
  //       const notFoundIds = roleIds.filter((id) => !foundRoleIdsSet.has(id));
  //       throw new BadRequestException(
  //         `One or more roles not found: IDs ${notFoundIds.join(', ')}.`,
  //       );
  //     }
  //     user.roles = roles;
  //     console.log('user.roles', user.roles);
  //   } else {
  //     // Optional: Assign a default role if no roleIds are provided
  //     // const defaultRole = await this.roleRepository.findOneBy({ name: 'student' }); // Example
  //     // if (defaultRole) user.roles = [defaultRole];
  //     user.roles = []; // Or ensure it's initialized if not eager loaded sometimes
  //   }

  //   const savedUser = await this.userRepository.save(user);
  //   return instanceToPlain(savedUser) as Omit<User, 'password'>;
  // }

  // ชั่วคราวไม่ใช้
  create(createUserDto: CreateUserDto) {
    return 'This action adds a new faculty';
  }

  async findAll(
    pag: DataParams,
    facultyFilter?: number,
    roleFilter?: number,
  ): Promise<DataResponse<UserListItem>> {
    // Build where conditions
    let whereConditions: FindManyOptions<User>['where'] = {};

    if (pag.search) {
      whereConditions = [
        { firstName: Like(`%${pag.search}%`) },
        { lastName: Like(`%${pag.search}%`) },
      ];
    }

    // For faculty and role filters, we'll filter after getting the data
    // This is simpler and more maintainable
    const [users, total] = await this.userRepository.findAndCount({
      relations: {
        facultyUsers: {
          faculty: true,
          facultyUserRoles: {
            role: true,
          },
        },
      },
      where: pag.search ? whereConditions : {},
      order: {
        [pag.sortBy || 'id']: pag.order || 'ASC',
      },
      skip: (Number(pag.page) - 1) * Number(pag.limit),
      take: Number(pag.limit),
    });

    // Filter users based on faculty and role after fetching
    let filteredUsers = users;

    if (facultyFilter || roleFilter) {
      filteredUsers = users.filter((user) => {
        if (!user.facultyUsers || user.facultyUsers.length === 0) {
          return false;
        }

        // Check faculty filter
        if (facultyFilter) {
          const hasMatchingFaculty = user.facultyUsers.some(
            (fu) => fu.facultyId === facultyFilter,
          );
          if (!hasMatchingFaculty) return false;
        }

        // Check role filter
        if (roleFilter) {
          const hasMatchingRole = user.facultyUsers.some((fu) =>
            fu.facultyUserRoles?.some((fur) => fur.role?.id === roleFilter),
          );
          if (!hasMatchingRole) return false;
        }

        return true;
      });
    }

    return createPaginatedResponse(
      filteredUsers.map((user: any) => {
        return {
          id: user.id,
          firstName: user.firstName,
          lastName: user.lastName,
          faculties:
            user.facultyUsers?.map((fu: any) => {
              // Extract roles for this specific faculty
              const facultyRoles =
                fu.facultyUserRoles?.map((fur: any) => fur.role) || [];

              return {
                id: fu.facultyId,
                faculty: fu.faculty,
                roles: facultyRoles,
              };
            }) ?? [],
        };
      }),
      filteredUsers.length, // Use filtered count
      Number(pag.page),
      Number(pag.limit),
    );
  }

  async findOne(
    id: number,
    loadRelations: any = {
      facultyUsers: {
        faculty: true,
        facultyUserRoles: {
          role: true,
        },
      },
    },
  ): Promise<UserListItem> {
    console.log('GET FIND ONE');

    const user = await this.userRepository.findOne({
      where: { id },
      relations: loadRelations,
    });

    if (!user) {
      throw new NotFoundException(`User with ID "${id}" not found`);
    }

    // Extract all roles from faculty users, same as in findAll
    const allRoles =
      user.facultyUsers?.flatMap(
        (facultyUser: any) =>
          facultyUser.facultyUserRoles?.map(
            (facultyUserRole: any) => facultyUserRole.role,
          ) || [],
      ) || [];

    // Map the user data to the same structure as in findAll
    const mappedUser = {
      id: user.id,
      firstName: user.firstName,
      lastName: user.lastName,
      faculties:
        user.facultyUsers?.map((fu: any) => {
          // Extract roles for this specific faculty
          const facultyRoles =
            fu.facultyUserRoles?.map((fur: any) => fur.role) || [];

          return {
            id: fu.facultyId,
            faculty: fu.faculty,
            roles: facultyRoles,
          };
        }) ?? [],
    };

    console.log('USER RETURN', mappedUser);

    return mappedUser;
  }

  async findOneByEmail(
    email: string,
    loadRelations: any = {
      facultyUsers: {
        faculty: true,
        facultyUserRoles: {
          role: true,
        },
      },
    },
  ): Promise<User | null> {
    // This method might be used internally for auth, so it returns the full User object including password
    return this.userRepository.findOne({
      where: { email },
      relations: loadRelations,
    });
  }

  // async update(
  //   id: number,
  //   updateUserDto: UpdateUserDto & {
  //     currentPassword?: string;
  //     newPassword?: string;
  //     roleIds?: number[];
  //   },
  // ): Promise<Omit<User, 'password'>> {
  //   const user = await this.userRepository.findOne({
  //     where: { id },
  //     relations: ['roles'],
  //   });

  //   if (!user) {
  //     throw new NotFoundException(`User with ID "${id}" not found.`);
  //   }

  //   // Email update check
  //   if (updateUserDto.email && updateUserDto.email !== user.email) {
  //     const existingUserWithNewEmail = await this.userRepository.findOneBy({
  //       email: updateUserDto.email,
  //     });
  //     if (existingUserWithNewEmail && existingUserWithNewEmail.id !== id) {
  //       throw new ConflictException(
  //         `User with email "${updateUserDto.email}" already exists.`,
  //       );
  //     }
  //   }
  //   const { newPassword, currentPassword, roleIds, ...otherFields } =
  //     updateUserDto;
  //   this.userRepository.merge(user, otherFields);
  //   // Password update
  //   if (updateUserDto.newPassword) {
  //     console.log('in password');
  //     console.log('PASSWORD ', updateUserDto.newPassword);
  //     const isTestOldPasswordMatching = await bcrypt.compare(
  //       updateUserDto.currentPassword,
  //       user.password,
  //     );
  //     console.log('Boolean isMatch', isTestOldPasswordMatching);

  //     if (!updateUserDto.currentPassword) {
  //       throw new BadRequestException(
  //         'Current password is required to change password.',
  //       );
  //     }
  //     const isOldPasswordMatching = await bcrypt.compare(
  //       updateUserDto.currentPassword,
  //       user.password,
  //     );
  //     if (!isOldPasswordMatching) {
  //       throw new BadRequestException('Old password does not match.');
  //     }
  //     if (updateUserDto.newPassword.length < 8) {
  //       throw new BadRequestException(
  //         'New password must be at least 8 characters long.',
  //       );
  //     }

  //     user.password = await bcrypt.hash(updateUserDto.newPassword, SALT_ROUNDS);
  //     console.log('USER PASSWORD', user.password);
  //   }

  //   // Roles update
  //   if (updateUserDto.roleIds) {
  //     // Clear existing roles
  //     user.roles = [];

  //     if (updateUserDto.roleIds.length > 0) {
  //       const newRoles = await this.roleRepository.findBy({
  //         id: In(updateUserDto.roleIds),
  //       });
  //       if (newRoles.length !== updateUserDto.roleIds.length) {
  //         const foundRoleIds = newRoles.map((r) => r.id);
  //         const notFoundIds = updateUserDto.roleIds.filter(
  //           (id) => !foundRoleIds.includes(id),
  //         );
  //         throw new BadRequestException(
  //           `One or more roles not found: IDs ${notFoundIds.join(', ')}.`,
  //         );
  //       }
  //       user.roles = newRoles;
  //       console.log('In update Role', user.roles);
  //     }
  //   }

  //   // Merge other fields (excluding password, roles)

  //   const updatedUser = await this.userRepository.save(user);

  //   return instanceToPlain(updatedUser) as Omit<User, 'password'>;
  // }

  // ชั่วคราวไม่ใช้
  update(
    id: number,
    updateUserDto: UpdateUserDto & {
      currentPassword?: string;
    },
  ) {
    return `This action updates a #${id} user`;
  }

  async remove(id: number): Promise<void> {
    const user = await this.userRepository.findOneBy({ id });
    if (!user) {
      throw new NotFoundException(`User with ID "${id}" not found.`);
    }
    await this.userRepository.remove(user);
  }

  async getFacultiesWithUserStatus(
    userId: number,
    pag: DataParams,
  ): Promise<DataResponse<FacultyWithUserStatus>> {
    const user = await this.userRepository.findOneBy({ id: userId });
    if (!user) {
      throw new NotFoundException(`User with ID "${userId}" not found.`);
    }
    const [allFaculties, total] = await this.facultyRepository.findAndCount({
      where: pag.search
        ? [
            { nameTh: Like(`%${pag.search}%`) },
            { nameEn: Like(`%${pag.search}%`) },
          ]
        : {},
      order: {
        [pag.sortBy || 'id']: pag.order || 'ASC',
      },
      skip: (pag.page - 1) * pag.limit,
      take: pag.limit,
    });

    const userFacultyUsers = await this.facultyUsersRepository.find({
      where: { userId },
      relations: ['faculty'],
    });
    const userFacultyIds = userFacultyUsers.map((fu) => fu.facultyId);
    const data = allFaculties.map((faculty) => ({
      id: faculty.id,
      nameTh: faculty.nameTh,
      nameEn: faculty.nameEn,
      isUserInFaculty: userFacultyIds.includes(faculty.id),
    }));

    return createPaginatedResponse(data, total, pag.page, pag.limit);
  }

  async updateUserFaculties(
    userId: number,
    facultyIds: number[],
  ): Promise<{
    message: string;
    addedFaculties: Faculty[];
    removedFaculties: Faculty[];
  }> {
    const user = await this.userRepository.findOneBy({ id: userId });
    if (!user) {
      throw new NotFoundException(`User with ID "${userId}" not found.`);
    }
    if (facultyIds.length > 0) {
      const faculties = await this.facultyRepository.findBy({
        id: In(facultyIds),
      });
      if (faculties.length !== facultyIds.length) {
        const foundFacultyIds = faculties.map((f) => f.id);
        const notFoundIds = facultyIds.filter(
          (id) => !foundFacultyIds.includes(id),
        );
        throw new BadRequestException(
          `One or more faculties not found: IDs ${notFoundIds.join(', ')}.`,
        );
      }
    }
    const currentFacultyUsers = await this.facultyUsersRepository.find({
      where: { userId },
      relations: ['faculty'],
    });
    const currentFacultyIds = currentFacultyUsers.map((fu) => fu.facultyId);
    const facultiesToAdd = facultyIds.filter(
      (id) => !currentFacultyIds.includes(id),
    );
    const facultiesToRemove = currentFacultyIds.filter(
      (id) => !facultyIds.includes(id),
    );
    if (facultiesToRemove.length > 0) {
      await this.facultyUsersRepository.delete({
        userId,
        facultyId: In(facultiesToRemove),
      });
    }
    if (facultiesToAdd.length > 0) {
      const newFacultyUsers = facultiesToAdd.map((facultyId) =>
        this.facultyUsersRepository.create({ userId, facultyId }),
      );
      await this.facultyUsersRepository.save(newFacultyUsers);
    }
    const addedFaculties =
      facultiesToAdd.length > 0
        ? await this.facultyRepository.findBy({ id: In(facultiesToAdd) })
        : [];
    const removedFaculties =
      facultiesToRemove.length > 0
        ? await this.facultyRepository.findBy({ id: In(facultiesToRemove) })
        : [];

    return {
      message: 'User faculties updated successfully',
      addedFaculties,
      removedFaculties,
    };
  }

  async updateRoles(
    userId: number,
    roleIds: number[],
    facultyUserId: number,
  ): Promise<{
    message: string;
    addedRoles: Role[];
    removedRoles: Role[];
  }> {
    // Implementation for updating user roles
    console.log('RolesId', roleIds);
    console.log('FalcultyUserId', facultyUserId);

    const user = this.userRepository.findOneBy({ id: userId });
    if (!user) {
      throw new NotFoundException(`User with ID "${userId}" not found.`);
    }
    if (roleIds.length > 0) {
      const roles = await this.roleRepository.findBy({ id: In(roleIds) });
      console.log('roles', roles);
      if (roles.length !== roleIds.length) {
        const foundRoleIds = roles.map((r) => r.id);

        const notFoundIds = roleIds.filter((id) => !foundRoleIds.includes(id));
        throw new BadRequestException(
          `One or more roles not found: IDs ${notFoundIds.join(', ')}.`,
        );
      }
    }
    const currentRoles = await this.facultyUserRolesRepository.find({
      where: { facultyUser: { userId } },
      relations: ['role'],
    });
    console.log('currentRoles', currentRoles);

    // if (!currentRoles || currentRoles.length === 0) {
    //   // No current roles, create FacultyUserRoles for all roleIds
    //   const newUserRoles = roleIds.map((roleId) =>
    //   this.facultyUserRolesRepository.create({
    //     facultyUser: { userId },
    //     role: { id: roleId },
    //   }),
    //   );
    //   await this.facultyUserRolesRepository.save(newUserRoles);
    // }
    const currentFaculty = await this.facultyUsersRepository.findOne({
      where: { id: facultyUserId },
      relations: ['facultyUserRoles'],
    });
    console.log('currentFaculty', currentFaculty);

    const currentRoleIds = currentRoles.map((r) => r.id);
    const rolesToAdd = roleIds.filter((id) => !currentRoleIds.includes(id));
    console.log('rolesToAdd', rolesToAdd);
    const rolesToRemove = currentRoleIds.filter((id) => !roleIds.includes(id));
    console.log('rolesToRemove', rolesToRemove);

    if (currentFaculty) {
      if (rolesToAdd.length > 0) {
        const newUserRoles = rolesToAdd.map((roleId) =>
          this.facultyUserRolesRepository.create({
            facultyUser: currentFaculty,
            role: { id: roleId },
          }),
        );
        await this.facultyUserRolesRepository.save(newUserRoles);
      }

      if (rolesToRemove.length > 0) {
        await this.facultyUserRolesRepository.delete({
          facultyUser: currentFaculty,
          id: In(rolesToRemove),
        });
      }
    }

    const addedRoles =
      rolesToAdd.length > 0
        ? await this.roleRepository.findBy({ id: In(rolesToAdd) })
        : [];
    const removedRoles =
      rolesToRemove.length > 0
        ? await this.roleRepository.findBy({ id: In(rolesToRemove) })
        : [];

    return {
      message: 'User roles updated successfully',
      addedRoles,
      removedRoles,
    };
  }

  async getUserFacultyUsers(
    userId: number,
    pag: DataParams,
  ): Promise<DataResponse<FacultyUserItem>> {
    try {
      // Check if user exists
      const user = await this.userRepository.findOneBy({ id: userId });
      if (!user) {
        throw new NotFoundException(`User with ID "${userId}" not found.`);
      }

      // Find faculty users associated with this user
      const [facultyUsers, total] =
        await this.facultyUsersRepository.findAndCount({
          where: { userId },
          relations: {
            faculty: true,
            users: true,
            facultyUserRoles: {
              role: true,
            },
          },
          order: {
            [pag.sortBy || 'id']: pag.order || 'ASC',
          },
          skip: (pag.page - 1) * pag.limit,
          take: pag.limit,
        });

      // Transform the data to return only relevant fields
      const data = facultyUsers.map((facultyUser) => {
        // Extract roles from faculty user roles
        const roles =
          facultyUser.facultyUserRoles?.map((fur) => ({
            id: fur.role.id,
            name: fur.role.name,
            description: fur.role.description,
          })) || [];

        return {
          id: facultyUser.id,
          userId: facultyUser.users.id,
          name: facultyUser.users.name,
          email: facultyUser.users.email,
          firstName: facultyUser.users.firstName,
          lastName: facultyUser.users.lastName,
          code: facultyUser.users.code,
          facultyId: facultyUser.faculty.id,
          facultyName: facultyUser.faculty.nameTh, // Using Thai name as default
          roles,
        };
      });

      return createPaginatedResponse(data, total, pag.page, pag.limit);
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new InternalServerErrorException(
        `Failed to retrieve faculty users: ${error.message}`,
      );
    }
  }

  async getPersonalPlan(
    pag: DataParams,
    careerFilter?: string,
    facultyFilter?: number,
    roleFilter?: number,
    careerTypeFilter?: string,
  ): Promise<DataResponse<{ id: number; name: string; careerName: string }>> {
    const queryBuilder = this.userRepository
      .createQueryBuilder('user')
      .leftJoin('user.facultyUsers', 'fu')
      .leftJoin('fu.faculty', 'faculty')
      .leftJoin('fu.facultyUserRoles', 'fur')
      .leftJoin('fur.role', 'role')
      .leftJoin('user.careerRecords', 'cr')
      .leftJoin(Career, 'career', 'career.id = cr.career_Id')
      .select([
        'user.id',
        'user.firstName',
        'user.lastName',
        'career.career_name',
      ])
      .distinct(true);

    this.applyFilters(queryBuilder, {
      careerFilter,
      facultyFilter,
      roleFilter,
      careerTypeFilter,
    });
    this.applySearchAndSort(queryBuilder, pag);

    const [total, results] = await Promise.all([
      queryBuilder.getCount(),
      queryBuilder
        .skip((pag.page - 1) * pag.limit)
        .take(pag.limit)
        .getRawMany(),
    ]);

    const data = results.map((result) => ({
      id: result.user_USR_ID,
      name: `${result.user_USR_FIRST_NAME} ${result.user_USR_LAST_NAME}`,
      careerName: result.career_CR_CAREER_NAME || 'ไม่ระบุ',
    }));

    return {
      data,
      total,
      currentPage: pag.page,
      itemsPerPage: pag.limit,
      totalPages: Math.ceil(total / pag.limit),
      hasPrev: pag.page > 1,
      hasNext: pag.page * pag.limit < total,
    };
  }

  private applyFilters(
    queryBuilder: any,
    filters: {
      careerFilter?: string;
      facultyFilter?: number;
      roleFilter?: number;
      careerTypeFilter?: string;
    },
  ) {
    const { careerFilter, facultyFilter, roleFilter, careerTypeFilter } =
      filters;

    if (careerFilter) {
      queryBuilder.andWhere('career.career_name LIKE :careerName', {
        careerName: `%${careerFilter}%`,
      });
    }
    if (facultyFilter) {
      queryBuilder.andWhere('faculty.id = :facultyId', {
        facultyId: facultyFilter,
      });
    }
    if (roleFilter) {
      queryBuilder.andWhere('role.id = :roleId', { roleId: roleFilter });
    }
    if (careerTypeFilter) {
      queryBuilder.andWhere('career.career_type LIKE :careerType', {
        careerType: `%${careerTypeFilter}%`,
      });
    }
  }

  private applySearchAndSort(queryBuilder: any, pag: DataParams) {
    if (pag.search) {
      queryBuilder.andWhere(
        '(user.firstName LIKE :search OR user.lastName LIKE :search OR career.career_name LIKE :search)',
        { search: `%${pag.search}%` },
      );
    }

    const sortFields = {
      id: 'user.id',
      firstName: 'user.firstName',
      lastName: 'user.lastName',
      careerName: 'career.career_name',
    };

    const sortField = sortFields[pag.sortBy] || 'user.id';
    queryBuilder.orderBy(sortField, pag.order || 'ASC');
  }
}
