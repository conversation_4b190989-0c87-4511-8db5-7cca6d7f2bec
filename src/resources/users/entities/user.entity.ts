import { Assessment } from 'src/resources/assessment-forms/entities/assessment.entity';
import { Submission } from 'src/resources/assessment-forms/entities/submission.entity';
import { Program } from 'src/resources/programs/entities/program.entity';
import { Role } from 'src/resources/roles/entities/role.entity';
import {
  Enti<PERSON>,
  PrimaryGeneratedColumn,
  Column,
  OneToMany,
  ManyToMany,
  JoinTable,
  JoinColumn,
} from 'typeorm';
import { Exclude } from 'class-transformer';
import { UserSkill } from 'src/resources/individual-develop-plans/skills/entities/user-skill.entity';
import { PermAuditSkill } from 'src/resources/individual-develop-plans/skills/entities/perm-audit-skill.entity';
import { Skill } from 'src/resources/individual-develop-plans/skills/entities/skill.entity';
import { CareerRecords } from 'src/resources/individual-develop-plans/career-records/entites/career-records.entity';

import { TypePlan } from 'src/resources/individual-develop-plans/type-plans/entities/type-plan.entity';
import { FacultyUsers } from 'src/resources/faculties/entities/faculty-user.entity';

@Entity('HRD_T_USERS', {
  comment: 'ตารางผู้ใช้งานระบบ'
})
export class User {
  @PrimaryGeneratedColumn({ name: 'USR_ID' , comment: 'รหัสผู้ใช้' })
  id: number;

  @Column({ name: 'USR_NAME', length: 100 , comment: 'ชื่อผู้ใช้' })
  name: string;

  @Column({ name: 'USR_EMAIL', unique: true, length: 100 , comment: 'อีเมลของผู้ใช้' })
  email: string;

  @Column({ name: 'USR_PASSWORD', comment: 'รหัสผ่านของผู้ใช้' })
  @Exclude() // Exclude password from serialization
  password: string;

  @Column({ name: 'USR_FIRST_NAME', length: 100, comment: 'ชื่อจริงของผู้ใช้' })
  firstName: string;

  @Column({ name: 'USR_LAST_NAME', length: 100, comment: 'นามสกุลของผู้ใช้' })
  lastName: string;

  @Column({ name: 'USR_CODE', length: 50, comment: 'รหัสประจำตัวผู้ใช้' })
  code: string;

  @Column({ name: 'USR_START_DATE', type: 'datetime', nullable: true, comment: 'วันที่เริ่มต้นงาน (สามารถเป็นค่าว่างได้)' })
  startDate: Date;

  @OneToMany(() => Program, (program) => program.creator)
  programs: Program[];

  @OneToMany(() => Assessment, (assessment) => assessment.creator)
  assessments: Assessment[];

  @OneToMany(() => Submission, (submission) => submission.user)
  submissions: Submission[];

  @OneToMany(() => UserSkill, (us) => us.user, {
    cascade: true,
  })
  userSkills: UserSkill[];

  @OneToMany(() => PermAuditSkill, (pa) => pa.user)
  permAuditSkills: PermAuditSkill[];

  @OneToMany(() => Skill, (s) => s.evaluator)
  evaluatedSkills: Skill[];

  @OneToMany(() => CareerRecords, (cr) => cr.user)
  careerRecords: CareerRecords[];

  @OneToMany(() => FacultyUsers, (du) => du.users)
  facultyUsers: FacultyUsers[];

  @OneToMany(() => TypePlan, (typePlan) => typePlan.privatePlanUser, {
    nullable: true,
  })
  privatePlans: TypePlan[];
}
