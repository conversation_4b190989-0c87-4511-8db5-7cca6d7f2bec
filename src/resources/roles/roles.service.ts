import {
  Injectable,
  NotFoundException,
  ConflictException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { In, Like, Repository, FindManyOptions } from 'typeorm';
import { Role } from './entities/role.entity';
import { CreateRoleDto } from './dto/create-role.dto';
import { UpdateRoleDto } from './dto/update-role.dto';
import { Permission } from '../permissions/entities/permission.entity';
import type { DataParams, DataResponse } from 'src/types/params';

export interface CreateRoleResponse {
  data: {
    id: number;
    name: string;
    description: string;
    permissions: Permission[];
  };
  message: string;
  statusCode: number;
}

@Injectable()
export class RolesService {
  constructor(
    @InjectRepository(Role)
    private readonly roleRepository: Repository<Role>,
    @InjectRepository(Permission)
    private readonly permissionRepository: Repository<Permission>,
  ) {}

  async create(createRoleDto: CreateRoleDto): Promise<CreateRoleResponse> {
    const { name, description, permissionIds } = createRoleDto;

    if (!description || description.trim() === '') {
      throw new ConflictException('Description must not be empty or null.');
    }

    const existingRole = await this.roleRepository.findOne({ where: { name } });
    if (existingRole) {
      throw new ConflictException(`Role with name "${name}" already exists.`);
    }

    const newRole = this.roleRepository.create({ name, description });

    // ดึง default permissions
    const defaultPermissions = await this.permissionRepository.find({
      where: { isDefault: true },
    });

    // ดึง permissions จาก permissionIds ถ้ามี
    let extraPermissions: Permission[] = [];
    if (permissionIds && permissionIds.length > 0) {
      extraPermissions = await this.permissionRepository.find({
        where: { id: In(permissionIds) },
      });

      if (extraPermissions.length !== permissionIds.length) {
        throw new NotFoundException(
          'Some permissions in permissionIds not found.',
        );
      }
    }

    // รวม permissions ทั้งหมดและกำจัดซ้ำ
    const permissionMap = new Map<number, Permission>();
    [...defaultPermissions, ...extraPermissions].forEach((p) =>
      permissionMap.set(p.id, p),
    );
    newRole.permissions = Array.from(permissionMap.values());
    const savedRole = await this.roleRepository.save(newRole);

    return {
      data: {
        id: savedRole.id,
        name: savedRole.name,
        description: savedRole.description,
        permissions: savedRole.permissions,
      },
      message: 'Role created successfully',
      statusCode: 201,
    };
  }

  // async addPermissionsToRole(roleId: number, permissionIds: number[]) {
  //   const role = await this.roleRepository.findOne({
  //     where: { id: roleId },
  //     relations: ['permissions'],
  //   });
  //   if (!role) throw new NotFoundException('Role not found');
  //   const permissions = await this.permissionRepository.find({
  //     where: { id: In(permissionIds) },
  //   });
  //   if (permissions.length === 0) throw new NotFoundException('Permissions not found');
  //   const newPermissions = permissions.filter(
  //     (p) => !role.permissions.some((rp) => rp.id === p.id),
  //   );
  //   role.permissions.push(...newPermissions);
  //   return this.roleRepository.save(role);
  // }

  async findAll(pag?: DataParams): Promise<DataResponse<Role>> {
    const queryOptions: FindManyOptions<Role> = {
      relations: ['permissions'],
      cache: true,
    };

    // ถ้ามี pagination parameters
    if (pag) {
      if (pag.search) {
        queryOptions.where = { name: Like(`%${pag.search}%`) };
      }
      if (pag.order) {
        queryOptions.order = { id: pag.order };
      }
      if (pag.page && pag.limit) {
        queryOptions.skip = (pag.page - 1) * pag.limit;
        queryOptions.take = pag.limit;
      }
    }

    const [roles, total] = await this.roleRepository.findAndCount(queryOptions);

    return {
      data: roles,
      total,
      currentPage: pag?.page || 1,
      itemsPerPage: pag?.limit || 10,
      totalPages: Math.ceil(total / pag.limit),
      hasNext: pag ? total > pag.page * pag.limit : false,
      hasPrev: pag ? pag.page > 1 : false,
    };
  }

  async findOne(id: number): Promise<Role> {
    const role = await this.roleRepository
      .createQueryBuilder('role')
      .leftJoinAndSelect(
        'role.permissions',
        'permission',
        'permission.status = :status',
        { status: true },
      )
      .where('role.id = :id', { id })
      .getOne();

    if (!role) {
      throw new NotFoundException(`Role with ID "${id}" not found`);
    }
    return role;
  }

  async update(id: number, updateRoleDto: UpdateRoleDto): Promise<Role> {
    const role = await this.findOne(id);
    const { name, description, permissionIds } = updateRoleDto;

    if (name && name !== role.name) {
      const existingRole = await this.roleRepository.findOneBy({ name });
      if (existingRole && existingRole.id !== id) {
        throw new ConflictException(`Role with name "${name}" already exists.`);
      }
      role.name = name;
    }

    if (description === undefined || description.trim() === '') {
      throw new ConflictException('Description must not be empty.');
    }
    role.description = description;

    if (permissionIds !== undefined) {
      const permissions = permissionIds.length
        ? await this.permissionRepository.find({
            where: { id: In(permissionIds) },
          })
        : [];

      if (permissionIds.length !== permissions.length) {
        throw new NotFoundException(
          'Some permissions in permissionIds not found.',
        );
      }

      role.permissions = permissions;
    }

    return this.roleRepository.save(role);
  }

  async remove(id: number): Promise<void> {
    const role = await this.findOne(id);
    await this.roleRepository.remove(role);
  }

  // async removePermissionsFromRole(roleId: number, permissionIds: number[]) {
  //   const role = await this.roleRepository.findOne({
  //     where: { id: roleId },
  //     relations: ['permissions'],
  //   });
  //   if (!role) throw new NotFoundException('Role not found');
  //   const permissionsToRemove = await this.permissionRepository.find({
  //     where: { id: In(permissionIds) },
  //   });
  //   if (permissionsToRemove.length === 0) throw new NotFoundException('Permissions not found');
  //   role.permissions = role.permissions.filter(
  //     (p) => !permissionIds.includes(p.id),
  //   );

  //   return this.roleRepository.save(role);
  // }
}
