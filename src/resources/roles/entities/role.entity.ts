import { FacultyUserRoles } from 'src/resources/faculties/entities/faculty-user-role.entity';
import { Permission } from 'src/resources/permissions/entities/permission.entity';

import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  ManyToMany,
  JoinTable,
  OneToMany,
} from 'typeorm';

@Entity('HRD_T_ROLES', {
  comment:
    'ตารางบทบาท (บุคลากร/หัวหน้าบุคลากร/ผู้บริหาร/กบพบ) ที่ใช้ในการกำหนดสิทธิ์การเข้าถึงระบบ',
})
export class Role {
  @PrimaryGeneratedColumn({ name: 'ROL_ID', comment: 'รหัสบทบาท' })
  id: number;

  @Column({ name: 'ROL_NAME', unique: true, nullable: false, comment: 'ชื่อบทบาท' })
  name: string;

  @Column({ name: 'ROL_DEPARTMENT', nullable: false, comment: 'ส่วนงาน' ,default: ''})
  department: string;

  @Column({ name: 'ROL_DESCRIPTION', nullable: true, comment: 'รายละเอียดบทบาท' })
  description: string;

  @ManyToMany(() => Permission, (permission) => permission.roles)
  @JoinTable({
    name: 'HRD_T_ROLES_HAS_PERMISSIONS',
    joinColumn: { name: 'ROL_ID', referencedColumnName: 'id' },
    inverseJoinColumn: { name: 'PER_ID', referencedColumnName: 'id' },
  })
  permissions: Permission[];

  @OneToMany(() => FacultyUserRoles, (facultyRoles) => facultyRoles.role, {
    cascade: true,
  })
  facultyRoles: FacultyUserRoles[];
}
