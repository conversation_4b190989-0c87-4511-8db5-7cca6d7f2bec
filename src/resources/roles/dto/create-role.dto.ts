import { ApiProperty } from '@nestjs/swagger';
import {
  IsString,
  IsNotEmpty,
  IsInt,
  IsArray,
  IsOptional,
} from 'class-validator';

export class CreateRoleDto {
  @ApiProperty({
    description: 'ชื่อบทบาท',
    example: 'Admin',
    type: String,
  })
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiProperty({
    description: 'ส่วนงาน',
    example: 'IT Department',
    type: String,
  })
  @IsString()
  @IsNotEmpty()
  department: string;

  @ApiProperty({
    description: 'รายละเอียดบทบาท',
    example: 'Administrator with full access',
    type: String,
    required: false,
  })
  @IsString()
  @IsOptional()
  description?: string;

  @ApiProperty({
    description: 'รหัสสิทธิ์ที่เกี่ยวข้อง',
    example: [1, 2, 3],
    type: [Number],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @IsInt({ each: true })
  permissionIds?: number[];
}
