import { Type } from 'class-transformer';
import { IsBoolean, IsOptional, IsString } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class CreatePermissionDto {
  @ApiProperty({
    description: 'ชื่อสิทธิ์',
    example: 'manage_users',
    type: String,
  })
  @IsString()
  name: string;

  @ApiProperty({
    description: 'รายละเอียดสิทธิ์ภาษาอังกฤษ',
    example: 'Manage users permission',
    type: String,
  })
  @IsString()
  nameEn: string;

  @ApiProperty({
    description: 'รายละเอียดสิทธิ์ภาษาไทย',
    example: 'สิทธิ์การจัดการผู้ใช้',
    type: String,
  })
  @IsString()
  descTh: string;

  @ApiProperty({
    description: 'สถานะการใช้งาน',
    example: true,
    type: Boolean,
    required: false,
  })
  @IsBoolean()
  @IsOptional()
  @Type(() => Boolean)
  status?: boolean;

  @ApiProperty({
    description: 'เป็นสิทธิ์เริ่มต้นหรือไม่',
    example: false,
    type: Boolean,
    required: false,
  })
  @IsBoolean()
  @IsOptional()
  @Type(() => Boolean)
  isDefault?: boolean;
}
