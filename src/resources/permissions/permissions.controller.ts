import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UseInterceptors,
  ParseIntPipe,
  ParseBoolPipe,
  UseGuards,
} from '@nestjs/common';
import { PermissionsService } from './permissions.service';
import { CreatePermissionDto } from './dto/create-permission.dto';
import { UpdatePermissionDto, SetPermissionStatusDto } from './dto/update-permission.dto';
import { ApiBearerAuth, ApiBody, ApiConsumes } from '@nestjs/swagger';
import { AnyFilesInterceptor } from '@nestjs/platform-express';
import { DefaultQueryParams } from 'src/utils/default-query.decorator';
import type { DataParams } from 'src/types/params';
import { AuthGuard } from 'src/auth/auth.guard';
import { RequirePermissions } from 'src/common/decorators/permissions.decorator';

@ApiBearerAuth()
@UseGuards(AuthGuard)
@Controller('permissions')
export class PermissionsController {
  constructor(private readonly permissionsService: PermissionsService) {}

  @Post()
  @RequirePermissions('permission_create')
  @ApiConsumes('multipart/form-data')
  @ApiBody({ type: CreatePermissionDto })
  @UseInterceptors(AnyFilesInterceptor())
  create(@Body() createPermissionDto: CreatePermissionDto) {
    return this.permissionsService.create(createPermissionDto);
  }

  @Get()
  @DefaultQueryParams()
  findAll(@Query() query: DataParams) {
    return this.permissionsService.findAll(query);
  }

  @Get('/status')
  findAllByStatus(@Query('perStatus', ParseBoolPipe) perStatus: boolean) {
    return this.permissionsService.findAllByStatus(perStatus);
  }

  @Get(':id')
  findOne(@Param('id', ParseIntPipe) id: number) {
    return this.permissionsService.findOne(id);
  }

  @Patch(':id/set-status')
  @ApiConsumes('multipart/form-data')
  @ApiBody({ type: SetPermissionStatusDto })
  @UseInterceptors(AnyFilesInterceptor())
  setStatus(
    @Param('id', ParseIntPipe) id: number,
    @Body() updatePermissionDto: SetPermissionStatusDto,
  ) {
    return this.permissionsService.setPermissionStatus(
      id,
      updatePermissionDto.status,
    );
  }

  @Patch(':id')
  @ApiConsumes('multipart/form-data')
  @ApiBody({ type: UpdatePermissionDto })
  @UseInterceptors(AnyFilesInterceptor())
  update(
    @Param('id', ParseIntPipe) id: number,
    @Body() updatePermissionDto: UpdatePermissionDto,
  ) {
    return this.permissionsService.update(id, updatePermissionDto);
  }

  @Delete(':id')
  remove(@Param('id', ParseIntPipe) id: number) {
    return this.permissionsService.remove(id);
  }
}
