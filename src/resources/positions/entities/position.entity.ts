import { TypePlan } from 'src/resources/individual-develop-plans/type-plans/entities/type-plan.entity';
import { Column, Entity, OneToMany, PrimaryGeneratedColumn } from 'typeorm';

@Entity('HRD_T_POSITIONS', { comment: 'ตารางตำแหน่งสายบริหาร' })
export class Position {
  @PrimaryGeneratedColumn({ name: 'POSITION_ID', comment: 'รหัสตำแหน่ง' })
  id: number;

  @Column({ name: 'POSITION_NAME', comment: 'ชื่อตำแหน่งสายบริหาร' })
  name: string;

  @OneToMany(() => TypePlan, (typePlan) => typePlan.position)
  typePlans: TypePlan[];
}
