import {
  Injectable,
  BadRequestException,
  InternalServerErrorException,
} from '@nestjs/common';
import { CreateFacultyDto } from './dto/create-faculty.dto';
import { UpdateFacultyDto } from './dto/update-faculty.dto';
import { InjectRepository } from '@nestjs/typeorm';
import { Faculty } from './entities/faculty.entity';
import { FacultyUsers } from './entities/faculty-user.entity';
import { FacultyUserRoles } from './entities/faculty-user-role.entity';
import { Repository, Like } from 'typeorm';
import { DataParams, DataResponse, createPaginatedResponse } from 'src/types';
import { FacultyRoleItem } from './interfaces/faculty-role.interface';
import { PaginationQueryDto } from 'src/common/dto/pagination-query.dto';
import { PaginationResponseDto } from 'src/common/dto/pagination-response.dto';
import { Role } from 'src/resources/roles/entities/role.entity';
import { FacultyUserItem } from 'src/resources/users/users.service';

export interface FacultyListItem {
  id: number;
  nameEn: string;
  nameTh: string;
  roles: Role[];
}

export interface UserByFaculty {
  user: {
    id: number;
    roleId: number;
    lastName: string;
    firstName: string;
    roles?: Role[];
  };
  facultyId: number;
  facultyName: string;
}

@Injectable()
export class FacultiesService {
  constructor(
    @InjectRepository(Faculty)
    private readonly facultyRepository: Repository<Faculty>,
    @InjectRepository(FacultyUsers)
    private readonly facultyUsersRepository: Repository<FacultyUsers>,
    @InjectRepository(FacultyUserRoles)
    private readonly facultyUserRolesRepository: Repository<FacultyUserRoles>,
  ) {}

  create(createFacultyDto: CreateFacultyDto) {
    return this.facultyRepository.save(createFacultyDto);
  }

  async findAll(pag: DataParams): Promise<DataResponse<FacultyListItem>> {
    console.log('Service faculties findAll');
    const [faculties, total] = await this.facultyRepository.findAndCount({
      where: {
        nameTh: pag.search ? Like(`%${pag.search}%`) : undefined,
        nameEn: pag.search ? Like(`%${pag.search}%`) : undefined,
      },
      relations: {
        facultyUsers: {
          facultyUserRoles: {
            role: true,
          },
        },
      },
      order: {
        id: pag.order || 'ASC',
      },
      skip: (pag.page - 1) * pag.limit,
      take: pag.limit,
      cache: true,
    });

    return createPaginatedResponse(
      faculties.map((faculty: any) => {
        const facultyUsers = faculty.facultyUsers || [];

        // Collect all unique roles from all users in this faculty
        const roleMap = new Map();

        facultyUsers.forEach((facultyUser) => {
          const facultyUserRoles = facultyUser.facultyUserRoles || [];

          facultyUserRoles.forEach((facultyUserRole) => {
            const role = facultyUserRole.role;

            // Add role if it doesn't exist to avoid duplicates
            if (!roleMap.has(role.id)) {
              roleMap.set(role.id, role);
            }
          });
        });

        // Convert role map to array
        const roles = Array.from(roleMap.values());

        return {
          id: faculty.id,
          nameEn: faculty.nameEn,
          nameTh: faculty.nameTh,
          roles,
        };
      }),
      total,
      pag.page,
      pag.limit,
    );
  }

  findOne(id: number) {
    const faculty = this.facultyRepository.findOne({ where: { id } });
    if (!faculty) {
      throw new Error(`Faculty with id ${id} not found`);
    }
    return faculty;
  }

  update(id: number, updateFacultyDto: UpdateFacultyDto) {
    return this.facultyRepository.update(id, updateFacultyDto);
  }

  remove(id: number) {
    return this.facultyRepository.delete(id);
  }

  async getFacultiesRoles(
    pag: DataParams,
    facultyId?: number,
    roleId?: number,
  ): Promise<DataResponse<FacultyRoleItem>> {
    try {
      // Build the query with optional filters
      const queryBuilder = this.facultyRepository.createQueryBuilder('faculty');

      // Join with related entities
      queryBuilder
        .leftJoinAndSelect('faculty.facultyUsers', 'facultyUsers')
        .leftJoinAndSelect('facultyUsers.users', 'user')
        .leftJoinAndSelect('facultyUsers.facultyUserRoles', 'facultyUserRoles')
        .leftJoinAndSelect('facultyUserRoles.role', 'role');

      // Apply filters
      if (facultyId) {
        queryBuilder.andWhere('faculty.id = :facultyId', { facultyId });
      }

      if (roleId) {
        queryBuilder.andWhere('role.id = :roleId', { roleId });
      }

      // Apply search if provided
      if (pag.search) {
        queryBuilder.andWhere(
          '(faculty.nameTh LIKE :search OR faculty.nameEn LIKE :search OR user.name LIKE :search OR user.email LIKE :search)',
          { search: `%${pag.search}%` },
        );
      }

      // Apply sorting
      const sortField = pag.sortBy || 'faculty.id';
      const sortOrder = pag.order || 'ASC';
      queryBuilder.orderBy(sortField, sortOrder);

      // Apply pagination
      queryBuilder.skip((pag.page - 1) * pag.limit).take(pag.limit);

      // Execute the query
      const [faculties, total] = await queryBuilder.getManyAndCount();

      // Transform the data to return only relevant fields
      const data = faculties.map((faculty) => {
        const facultyUsers = faculty.facultyUsers || [];

        // Group users by user ID to avoid duplicates
        const userMap = new Map();

        facultyUsers.forEach((facultyUser) => {
          const user = facultyUser.users;
          const userId = user.id;

          if (!userMap.has(userId)) {
            // Initialize user entry
            userMap.set(userId, {
              id: user.id,
              name: user.name,
              email: user.email,
              firstName: user.firstName,
              lastName: user.lastName,
              code: user.code,
              startDate: user.startDate,
              roles: [],
            });
          }

          // Add roles for this user
          const userEntry = userMap.get(userId);
          const facultyUserRoles = facultyUser.facultyUserRoles || [];

          facultyUserRoles.forEach((facultyUserRole) => {
            const role = facultyUserRole.role;

            // Check if role already exists to avoid duplicates
            const roleExists = userEntry.roles.some((r) => r.id === role.id);
            if (!roleExists) {
              userEntry.roles.push({
                id: role.id,
                name: role.name,
                description: role.description,
              });
            }
          });
        });

        // Convert user map to array
        const users = Array.from(userMap.values());

        return {
          facultyId: faculty.id,
          facultyName: faculty.nameTh,
          facultyNameEn: faculty.nameEn,
          users,
        };
      });

      return createPaginatedResponse(data, total, pag.page, pag.limit);
    } catch (error) {
      if (error instanceof BadRequestException) {
        throw error;
      }
      throw new InternalServerErrorException(
        `Failed to retrieve faculties with roles: ${error.message}`,
      );
    }
  }

  async getUsersFaculty(
    facultyId: number,
    pag: DataParams,
  ): Promise<DataResponse<FacultyUserItem>> {
    try {
      // Find the faculty first to get faculty information
      const faculty = await this.facultyRepository.findOne({
        where: { id: facultyId },
      });

      if (!faculty) {
        throw new BadRequestException(`Faculty with id ${facultyId} not found`);
      }

      // Build query with pagination and search
      const queryBuilder =
        this.facultyUsersRepository.createQueryBuilder('facultyUsers');

      // Join with related entities
      queryBuilder
        .leftJoinAndSelect('facultyUsers.users', 'user')
        .leftJoinAndSelect('facultyUsers.facultyUserRoles', 'facultyUserRoles')
        .leftJoinAndSelect('facultyUserRoles.role', 'role')
        .where('facultyUsers.facultyId = :facultyId', { facultyId });

      // Apply search if provided
      if (pag.search) {
        queryBuilder.andWhere(
          '(user.name LIKE :search OR user.email LIKE :search OR user.firstName LIKE :search OR user.lastName LIKE :search OR user.code LIKE :search)',
          { search: `%${pag.search}%` },
        );
      }

      // Apply sorting
      const sortField = pag.sortBy || 'user.id';
      const sortOrder = pag.order || 'ASC';
      queryBuilder.orderBy(sortField, sortOrder);

      // Apply pagination
      queryBuilder.skip((pag.page - 1) * pag.limit).take(pag.limit);

      // Execute the query
      const [facultyUsers, total] = await queryBuilder.getManyAndCount();

      // Transform the data to match the desired format
      const data: FacultyUserItem[] = facultyUsers.map((facultyUser) => {
        const user = facultyUser.users;
        const facultyUserRoles = facultyUser.facultyUserRoles || [];

        // Extract roles for this user
        const roles = facultyUserRoles.map((fur) => ({
          id: fur.role.id,
          name: fur.role.name,
          description: fur.role.description,
        }));

        return {
          id: user.id,
          name: user.name,
          email: user.email,
          firstName: user.firstName,
          lastName: user.lastName,
          code: user.code,
          facultyId: faculty.id,
          facultyName: faculty.nameTh,
          roles: roles,
        };
      });

      return {
        data,
        total,
        currentPage: pag.page,
        itemsPerPage: pag.limit,
        totalPages: Math.ceil(total / pag.limit),
        hasNext: total > pag.page * pag.limit,
        hasPrev: pag.page > 1,
      };
    } catch (error) {
      if (error instanceof BadRequestException) {
        throw error;
      }
      throw new InternalServerErrorException(
        `Failed to retrieve users for faculty ${facultyId}: ${error.message}`,
      );
    }
  }
}
