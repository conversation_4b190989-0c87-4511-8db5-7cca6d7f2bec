/**
 * Interface representing a faculty with its users and their roles
 */
export interface FacultyRoleItem {
  facultyId: number;
  facultyName: string;
  facultyNameEn: string;
  users: {
    id: number;
    name: string;
    email: string;
    firstName: string;
    lastName: string;
    code: string;
    startDate: Date | null;
    roles: {
      id: number;
      name: string;
      description: string;
    }[];
  }[];
}