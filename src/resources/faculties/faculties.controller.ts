import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UseGuards,
} from '@nestjs/common';
import { FacultiesService } from './faculties.service';
import { CreateFacultyDto } from './dto/create-faculty.dto';
import { UpdateFacultyDto } from './dto/update-faculty.dto';
import { FacultyRolesQueryDto } from './dto/faculty-roles-query.dto';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
} from '@nestjs/swagger';
import type { DataParams, DataResponse } from 'src/types/params';

import { FacultyListItem } from './faculties.service';
import { FacultyUserItem } from 'src/resources/users/users.service';
import { DefaultQueryParams } from 'src/utils/default-query.decorator';
import { AuthGuard } from 'src/auth/auth.guard';

@ApiBearerAuth()
@UseGuards(AuthGuard)
@ApiTags('Faculties')
@Controller('faculties')
export class FacultiesController {
  constructor(private readonly facultiesService: FacultiesService) {}

  @Post()
  create(@Body() createFacultyDto: CreateFacultyDto) {
    return this.facultiesService.create(createFacultyDto);
  }

  @Get()
  @ApiOperation({ summary: 'Get all faculties with pagination and relations' })
  @DefaultQueryParams()
  @ApiResponse({
    status: 200,
    description:
      'Returns a paginated list of faculties with their users and roles',
  })
  findAll(@Query() query: DataParams): Promise<DataResponse<FacultyListItem>> {
    return this.facultiesService.findAll(query);
  }

  @Get(':id')
  findOne(@Param('id') id: string) {
    return this.facultiesService.findOne(+id);
  }

  @Patch(':id')
  update(@Param('id') id: string, @Body() updateFacultyDto: UpdateFacultyDto) {
    return this.facultiesService.update(+id, updateFacultyDto);
  }

  @Delete(':id')
  remove(@Param('id') id: string) {
    return this.facultiesService.remove(+id);
  }

  @Get(':facultyId/:roleId/roles')
  @ApiOperation({ summary: 'Get faculties with their users and roles' })
  @ApiResponse({
    status: 200,
    description:
      'Returns a paginated list of faculties with their users and role assignments',
  })
  getFacultiesRoles(
    @Param('facultyId') facultyId: string,
    @Param('roleId') roleId: string,
    @Query() query: DataParams,
  ) {
    return this.facultiesService.getFacultiesRoles(query, +facultyId, +roleId);
  }

  @Get(':facultyId/users')
  @ApiOperation({
    summary: 'Get all users in a specific faculty with their roles',
  })
  @DefaultQueryParams()
  @ApiResponse({
    status: 200,
    description:
      'Returns a paginated list of users in the specified faculty with their roles',
  })
  getUsersFaculty(
    @Param('facultyId') facultyId: string,
    @Query() query: DataParams,
  ): Promise<DataResponse<FacultyUserItem>> {
    return this.facultiesService.getUsersFaculty(+facultyId, query);
  }
}
