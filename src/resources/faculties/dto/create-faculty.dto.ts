import { IsString } from "class-validator";
import { ApiProperty } from '@nestjs/swagger';

export class CreateFacultyDto {
  @ApiProperty({
    description: 'ชื่อคณะภาษาไทย',
    example: 'คณะวิศวกรรมศาสตร์',
    type: String,
  })
  @IsString()
  nameTh: string;

  @ApiProperty({
    description: 'ชื่อคณะภาษาอังกฤษ',
    example: 'Faculty of Engineering',
    type: String,
  })
  @IsString()
  nameEn: string;
}
