import { Module } from '@nestjs/common';
import { FacultiesService } from './faculties.service';
import { FacultiesController } from './faculties.controller';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Faculty } from './entities/faculty.entity';
import { FacultyUsers } from './entities/faculty-user.entity';
import { FacultyUserRoles } from './entities/faculty-user-role.entity';

@Module({
  imports: [TypeOrmModule.forFeature([Faculty,FacultyUsers,FacultyUserRoles])], // Add your entity here
  controllers: [FacultiesController],
  providers: [FacultiesService],
})
export class FacultiesModule {}
