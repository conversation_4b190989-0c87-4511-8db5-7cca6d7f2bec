import {
  <PERSON>um<PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  ManyToOne,
  OneToMany,
  PrimaryGeneratedColumn,
} from 'typeorm';
import { User } from 'src/resources/users/entities/user.entity';
import { Faculty } from './faculty.entity';
import { FacultyUserRoles } from './faculty-user-role.entity';

@Entity('HRD_T_FACULTY_USERS', { comment: 'ตารางผู้ใช้ในคณะ' })
export class FacultyUsers {
  @PrimaryGeneratedColumn({ name: 'FU_ID', comment: 'รหัสผู้ใช้ในคณะ' })
  id: number;

  @Column({ name: 'FAC_ID', nullable: false, comment: 'รหัสคณะ' })
  facultyId: number;

  @Column({ name: 'USR_ID', nullable: false, comment: 'รหัสผู้ใช้' })
  userId: number;

  @ManyToOne(() => Faculty, (faculty) => faculty.facultyUsers)
  @JoinColumn({ name: 'FAC_ID' })
  faculty: Faculty;

  @ManyToOne(() => User, (user) => user.facultyUsers)
  @JoinColumn({ name: 'USR_ID' })
  users: User;

  @OneToMany(
    () => FacultyUserRoles,
    (facultyRoles) => facultyRoles.facultyUser,
  )
  facultyUserRoles: FacultyUserRoles[];

  // One to many faculty user role
}
