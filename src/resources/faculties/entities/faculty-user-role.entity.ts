import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>o<PERSON><PERSON>,
  PrimaryGeneratedColumn,
} from 'typeorm';
import { Role } from 'src/resources/roles/entities/role.entity';
import { FacultyUsers } from './faculty-user.entity';

@Entity('HRD_T_FACULTY_USER_ROLES', { comment: 'ตารางบทบาทผู้ใช้ในคณะ' })
export class FacultyUserRoles {
  @PrimaryGeneratedColumn({ name: 'FUR_ID', comment: 'รหัสบทบาทผู้ใช้ในคณะ' })
  id: number;

  @Column({ name: 'FU_ID', nullable: false, comment: 'รหัสผู้ใช้ในคณะ' })
  facultyUserId: number;

  @Column({ name: 'ROL_ID', nullable: false, comment: 'รหัสบทบาท' })
  roleId: number;

  @ManyToOne(() => FacultyUsers, (fu) => fu.facultyUserRoles)
  @JoinColumn({ name: 'FU_ID' })
  facultyUser: FacultyUsers;

  @ManyToOne(() => Role, {
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'ROL_ID' })
  role: Role;
}
