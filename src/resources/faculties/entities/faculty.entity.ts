import { Column, <PERSON>tity, OneToMany, PrimaryGeneratedColumn } from 'typeorm';
import { DevelopmentPlan } from 'src/resources/individual-develop-plans/development-plans/entities/development-plan.entity';
import { FacultyUsers } from './faculty-user.entity';

@Entity('HRD_T_FACULTIES', { comment: 'ตารางคณะ' })
export class Faculty {
  @PrimaryGeneratedColumn({ name: 'FAC_ID', comment: 'รหัสคณะ' })
  id: number;

  @Column({ name: 'FAC_NAME_TH', nullable: false, comment: 'ชื่อคณะ (TH)' })
  nameTh: string;

  @Column({ name: 'FAC_NAME_EN', nullable: false, comment: 'ชื่อคณะ (EN)' })
  nameEn: string;

  @OneToMany(() => FacultyUsers, (facultyUser) => facultyUser.faculty, {
    cascade: true,
  })
  facultyUsers: FacultyUsers[];

  @OneToMany(
    () => DevelopmentPlan,
    (developmentPlan) => developmentPlan.faculty,
  )
  developmentPlans: DevelopmentPlan[];
}
