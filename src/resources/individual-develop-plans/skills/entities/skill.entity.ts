import { Program } from 'src/resources/programs/entities/program.entity';
import { User } from 'src/resources/users/entities/user.entity';
import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  OneToMany,
  ManyToOne,
  JoinColumn,
  ManyToMany,
  JoinTable,
} from 'typeorm';
import { UserSkill } from './user-skill.entity';
import { PermAuditSkill } from './perm-audit-skill.entity';
import { CompetencySkill } from './competency-skill.entity';
import { PlanType, TypePlan } from '../../type-plans/entities/type-plan.entity';

@Entity('IDP_T_SKILLS', { comment: 'ทักษะของบุคลากร (Skills)' })
export class Skill {
  @PrimaryGeneratedColumn({ name: 'SK_ID', comment: 'รหัสทักษะ' })
  id: number;

  @Column({ name: 'SK_NAME', comment: 'ชื่อทักษะ' })
  name: string;

  @Column({ name: 'SK_DESCRIPTION', nullable: true, comment: 'รายละเอียดทักษะ' })
  description: string;

  @Column({ name: 'SK_TRACKING', nullable: true, comment: 'ติดตามผล' })
  tracking: boolean;

  @Column({
    name: 'SK_CAREER_TYPE',
    type: 'enum',
    enum: PlanType,
    enumName: 'PLAN_TYPE_ENUM',
    nullable: true,
    comment: 'ประเภทสายงาน'
  })
  career_type: PlanType | null;

  @Column({ name: 'PROGRAM_ID' , comment: 'รหัสโปรแกรมที่ทักษะนี้เกี่ยวข้อง' })
  programId: number;

  @Column({ name: 'EVALUATOR_ID', nullable: true, comment: 'รหัสผู้ประเมิน' })
  evaluatorId: number;

  @Column({ name: 'FAC_ID', nullable: true, comment: 'รหัสคณะ' })
  facId?: number;

  @ManyToOne(() => Program, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'PROGRAM_ID' })
  program: Program;

  @ManyToOne(() => User, { onDelete: 'SET NULL' })
  @JoinColumn({ name: 'EVALUATOR_ID' })
  evaluator: User;

  @OneToMany(() => UserSkill, (us) => us.skill)
  userSkills: UserSkill[];

  @OneToMany(() => PermAuditSkill, (pa) => pa.skill)
  permAuditSkills: PermAuditSkill[];

  @OneToMany(() => CompetencySkill, (cs) => cs.skill)
  competencySkills: CompetencySkill[];

  @ManyToMany(() => TypePlan, (typePlan) => typePlan.skills )
  typePlans: TypePlan[];
}
