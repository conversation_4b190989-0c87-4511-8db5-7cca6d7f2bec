import { User } from 'src/resources/users/entities/user.entity';
import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  ManyToOne,
  JoinColumn,
  CreateDateColumn,
  Column,
} from 'typeorm';
import { Skill } from './skill.entity';

@Entity('IDP_T_PERM_AUDIT_SKILLS', { comment: 'ทักษะที่ตรวจสอบสิทธิ์' })
export class PermAuditSkill {
  @PrimaryGeneratedColumn({ name: 'PAS_ID', comment: 'รหัสทักษะที่ตรวจสอบสิทธิ์' })
  id: number;

  @ManyToOne(() => User, (user) => user.permAuditSkills, {
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'USER_ID' })
  user: User;

  @ManyToOne(() => Skill, (skill) => skill.permAuditSkills, {
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'SKILL_ID' })
  skill: Skill;

  @CreateDateColumn({ name: 'PAS_CREATED_AT', comment: 'วันที่สร้าง' })
  createdAt: Date;
}
