import { CompetencySkill } from 'src/resources/individual-develop-plans/skills/entities/competency-skill.entity';
import { Entity, PrimaryGeneratedColumn, Column, OneToMany } from 'typeorm';
import { CompetencyType } from '../enum/competencies-type.enum';

@Entity('IDP_T_COMPETENCIES', {comment: 'ตารางสมรรถนะ'})
export class Competency {
  @PrimaryGeneratedColumn({ name: 'CPT_ID', comment: 'รหัสสมรรถนะ' })
  id: number;

  @Column({
    name: 'CPT_CAREER_TYPE',
    type: 'enum',
    enum: CompetencyType,
    comment: 'ประเภทอาชีพ'
  })
  career_type: CompetencyType;

  @Column({ name: 'CPT_NAME', comment: 'ชื่อสมรรถนะ' })
  name: string;

  @Column({ name: 'CPT_DESCRIPTION', nullable: true, comment: 'รายละเอียดสมรรถนะ' })
  description: string;

  @OneToMany(() => CompetencySkill, (cs) => cs.competency)
  competencySkills: CompetencySkill[];
}
