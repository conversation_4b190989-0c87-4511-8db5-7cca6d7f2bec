import { ApiProperty } from '@nestjs/swagger';
import { Career } from '../entities/careers.entity';
import { PaginationResponseDto } from 'src/common/dto/pagination-response.dto';

export class CareersResponseDto extends PaginationResponseDto<Career> {
  @ApiProperty({ type: [Career] })
  data: Career[];

  constructor(data: Career[], total: number, page: number, limit: number) {
    super(data, total, page, limit);
  }
}
