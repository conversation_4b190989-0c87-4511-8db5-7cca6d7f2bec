import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Career } from './entities/careers.entity';
import { Repository, Like } from 'typeorm';
import { CareersQueryDto } from './dto/careers-query.dto';
import { CreateCareerDto } from './dto/create-career.dto';
import { UpdateCareerDto } from './dto/update-career.dto';

@Injectable()
export class CareersService {
  constructor(
    @InjectRepository(Career)
    private readonly careerRepo: Repository<Career>,
  ) {}
  // Get all careers with pagination and filtering
  async findAll(query: CareersQueryDto): Promise<[Career[], number]> {
    const {
      page = 1,
      limit = 10,
      sortBy = 'id',
      order = 'ASC',
      search,
      career_type,
      career_name,
      career_rank,
    } = query;

    const skip = (page - 1) * limit;
    const where: any = {};

    // Apply search filter
    if (search) {
      where.career_name = Like(`%${search}%`);
    }

    // Apply specific filters if provided
    if (career_type !== undefined) {
      where.career_type = career_type;
    }

    if (career_name !== undefined) {
      where.career_name = career_name;
    }

    if (career_rank !== undefined) {
      where.career_rank = career_rank;
    }

    // Execute query with pagination
    const [data, total] = await this.careerRepo.findAndCount({
      where,
      order: { [sortBy]: order },
      skip,
      take: limit,
    });

    return [data, total];
  }

  // Get career names for dropdown (grouped by name)
  async findCareerNamesForDropdown(
    query: CareersQueryDto,
  ): Promise<[string[], number]> {
    const { page = 1, limit = 10, search } = query;

    const skip = (page - 1) * limit;

    let queryBuilder = this.careerRepo
      .createQueryBuilder('career')
      .select('career.career_name', 'name')
      .where('career.career_name IS NOT NULL')
      .groupBy('career.career_name');

    // Apply search filter
    if (search) {
      queryBuilder = queryBuilder.andWhere('career.career_name LIKE :search', {
        search: `%${search}%`,
      });
    }

    // Get total count for distinct career names
    const totalQueryBuilder = queryBuilder.clone();
    const totalResult = await totalQueryBuilder.getRawMany();
    const total = totalResult.length;

    // Apply pagination and ordering
    const careers = await queryBuilder
      .orderBy('career.career_name', 'ASC')
      .skip(skip)
      .take(limit)
      .getRawMany();

    const careerNames = careers.map((career) => career.name);

    return [careerNames, total];
  }

  // Get a single career by id
  async findOne(id: number): Promise<Career> {
    const career = await this.careerRepo.findOne({
      where: { id },
    });
    if (!career) {
      throw new NotFoundException(`Career with ID ${id} not found`);
    }
    return career;
  }

  // Create a new career
  async create(createCareerDto: CreateCareerDto): Promise<Career> {
    const career = this.careerRepo.create({
      career_type: createCareerDto.career_type,
      career_name: createCareerDto.career_name,
      career_rank: createCareerDto.career_rank || null,
    });
    return this.careerRepo.save(career);
  }

  // Update a career
  async update(id: number, updateCareerDto: UpdateCareerDto): Promise<Career> {
    const career = await this.findOne(id);
    const updateData: Partial<Career> = {};

    if (updateCareerDto.career_type !== undefined) {
      updateData.career_type = updateCareerDto.career_type;
    }
    if (updateCareerDto.career_name !== undefined) {
      updateData.career_name = updateCareerDto.career_name;
    }
    if (updateCareerDto.career_rank !== undefined) {
      updateData.career_rank = updateCareerDto.career_rank;
    }

    await this.careerRepo.update(id, updateData);
    return this.findOne(id);
  }

  // Remove a career
  async remove(id: number): Promise<void> {
    const result = await this.careerRepo.delete(id);
    if (result.affected === 0) {
      throw new NotFoundException(`Career with ID ${id} not found`);
    }
  }
}
