import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  <PERSON><PERSON><PERSON>,
  OneToMany,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
} from 'typeorm';
import { CareerRecords } from '../../career-records/entites/career-records.entity';
import { TypePlan } from '../../type-plans/entities/type-plan.entity';

@Entity('IDP_T_CAREERS', {comment: 'ข้อมูลอาชีพของบุคลากร (Career Information)'})
export class Career {
  @PrimaryGeneratedColumn({ name: 'CR_ID', comment: 'รหัสอาชีพ' })
  id: number;

  @Column({ name: 'CR_CAREER_TYPE', comment: 'ประเภทอาชีพ (วิชาการ/สนับสนุน)' })
  career_type: string;

  @Column({ name: 'CR_CAREER_NAME', comment: 'ชื่ออาชีพ (อาจารย์/นักบัญชี)' })
  career_name: string;

  @Column({ name: 'CR_CAREER_RANK', comment: 'ระดับตำแหน่ง (ผู้ช่วยศาสตราจารย์/ชำนาญการ)' })
  career_rank: string;

  @JoinColumn({ name: 'CR_CRR_ID' })
  @OneToMany(() => CareerRecords, (cr) => cr.career_Id)
  careerRecords: CareerRecords[];

  @OneToMany(() => TypePlan, (typePlan) => typePlan.career)
  typePlans: TypePlan[];
}
