import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { DevelopmentPlansService } from './development-plans.service';
import { DevelopmentPlansController } from './development-plans.controller';
import { DevelopmentPlan } from './entities/development-plan.entity';
import { AgeWorkCriteria } from '../age-work/entities/age-work-criteria.entity';
import { CareerRecords } from '../career-records/entites/career-records.entity';
import { UsersModule } from 'src/resources/users/users.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([DevelopmentPlan, AgeWorkCriteria, CareerRecords]),
    UsersModule,
  ],
  controllers: [DevelopmentPlansController],
  providers: [DevelopmentPlansService],
  exports: [DevelopmentPlansService],
})
export class DevelopmentPlansModule {}
