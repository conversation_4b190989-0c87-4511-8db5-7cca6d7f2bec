import { ApiProperty } from '@nestjs/swagger';

export class PersonalPlanItemDto {
  @ApiProperty({
    description: 'User ID',
    example: 1,
    type: 'number',
  })
  id: number;

  @ApiProperty({
    description: 'User full name',
    example: 'จอห์น โด',
    type: 'string',
  })
  name: string;

  @ApiProperty({
    description: 'Career name',
    example: 'อาจารย์',
    type: 'string',
  })
  careerName: string;
}
