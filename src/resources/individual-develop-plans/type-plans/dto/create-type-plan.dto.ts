import { Is<PERSON>num, IsOptional, <PERSON>N<PERSON><PERSON>, IsArray } from 'class-validator';
import { PlanType } from '../entities/type-plan.entity';
import { ApiProperty } from '@nestjs/swagger';

export class CreateTypePlanDto {
  @ApiProperty({
    description: 'ID ของ Development Plan',
    required: true,
    example: 1,
  })
  @IsNumber()
  developmentPlanId: number;

  @ApiProperty({
    description: 'ประเภทของ Type Plan',
    required: true,
    enum: PlanType,
    example: PlanType.GENERAL_STAFF,
  })
  @IsEnum(PlanType)
  name: PlanType;

  @ApiProperty({
    description: 'ID ของ Age Work',
    required: false,
    example: 1,
  })
  @IsNumber()
  @IsOptional()
  ageWorkId?: number | null;

  @ApiProperty({
    description: 'ID ของ Career',
    required: false,
    example: 1,
  })
  @IsNumber()
  @IsOptional()
  careerId?: number | null;

  @ApiProperty({
    description: 'ID ของ Private User',
    required: false,
    example: 1,
  })
  @IsNumber()
  @IsOptional()
  privateUserId?: number | null;

  @ApiProperty({
    description: 'ID ของ Position',
    required: false,
    example: 1,
  })
  @IsNumber()
  @IsOptional()
  positionId?: number | null;

  @ApiProperty({
    description: 'ID ของ Skill',
    required: false,
    example: [1, 2, 3],
  })
  @IsOptional()
  @IsArray()
  skillIds?: number[];
}
