import { User } from "src/resources/users/entities/user.entity";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>To<PERSON>ne, PrimaryGeneratedColumn } from "typeorm";
import { Career } from "../../careers/entities/careers.entity";

@Entity('IDP_T_CAREER_RECORDS', { comment: 'บันทึกประวัติการทำงาน' })
export class CareerRecords {
 
  
  @PrimaryGeneratedColumn({ name: 'CRR_ID' , comment: 'รหัสบันทึกประวัติการทำงาน' })
  id: number;

  @JoinColumn(
    { name: 'CRR_CAREER_ID' }
  )
  @ManyToOne(() => Career, (career) => career.careerRecords)
  career_Id: number;

  @JoinColumn({ name: 'CRR_USER_ID' })
  @ManyToOne(() => User, (user) => user.careerRecords)
  user: User;

  @Column({ name: 'CRR_START_DATE', comment: 'วันที่เริ่มงาน' })
  start_date: Date;

  @Column({ name: 'CRR_END_DATE', nullable: true, comment: 'วันที่สิ้นสุดงาน' })
  end_date: Date;

 
}