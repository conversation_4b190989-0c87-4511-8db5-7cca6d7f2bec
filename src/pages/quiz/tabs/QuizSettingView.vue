<template>
  <div class="fit q-mx-auto row justify-center q-mt-xl">
    <q-card class="q-pa-xl container">
      <q-card-section class="text-h4 text-weight-bold"
        >ตั้งค่า
        <q-separator class="q-mt-lg" />
      </q-card-section>

      <q-card-section class="q-gutter-y-md">
        <div class="fit row items-center justify-between">
          <div>
            <div class="text-subtitle1">กำหนดขอบเขตเวลา</div>
            <div class="text-caption text-grey">
              กำหนดวันที่และเวลาเพื่อเปิด-ปิดแบบทดสอบแบบอัตโนมัติ
            </div>
          </div>

          <div class="row q-gutter-x-lg">
            <SelectDate v-model:model-value="selectedDate" label="เลือกวันเริ่มต้น" />
            <SelectDate
              v-model:model-value="selectedDateEnd"
              label="เลือกวันสิ้นสุด"
              :disable="selectedDate === '' || selectedDate === null"
              :rules="[
                (val: any) =>
                  !val ||
                  val >= selectedDate ||
                  'วันที่สิ้นสุดต้องมากกว่าหรือเท่ากับวันที่เริ่มต้น',
              ]"
            />
          </div>
        </div>

        <q-separator />
        <div class="row items-center justify-between">
          <div>
            <div class="text-subtitle1">ตั้งเวลาทำแบบทดสอบ</div>
            <div class="text-caption text-grey">กำหนดเวลาในการทำแบบทดสอบของผู้ทำแบบสอบถาม</div>
          </div>
          <div class="row q-gutter-x-md">
            <HourDropdown v-model:model-value="hour" />
            <MinDropdown v-model:model-value="minute" />
          </div>
        </div>

        <q-separator />
        <div class="row items-center justify-between">
          <div>
            <div class="text-subtitle1">จำนวนครั้งที่สามารถทำแบบทดสอบ</div>
            <div class="text-caption text-grey">
              กำหนดจำนวนครั้งที่สามารถทำแบบทดสอบของผู้ทำแบบสอบถาม
            </div>
          </div>
          <TextField
            v-model:model-value="attemptLimit"
            placeholder="กรุณากรอกข้อมูล..."
            type="number"
          />
        </div>

        <q-separator />
        <div class="row items-center justify-between">
          <div>
            <div class="text-subtitle1">ร้อยละขั้นต่ำเพื่อผ่านแบบทดสอบ</div>
            <div class="text-caption text-grey">กำหนดเกณฑ์คะแนนผ่านของแบบสอบถาม</div>
          </div>
          <TextField
            v-model:model-value="passRatio"
            placeholder="กรุณากรอกข้อมูล..."
            type="number"
          />
        </div>

        <q-separator />
        <div class="row items-center justify-between">
          <div>
            <div class="text-subtitle1">เป็นต้นแบบ</div>
            <div class="text-caption text-grey">กำหนดให้ฟอร์มนี้เป็นต้นแบบสำหรับสำเนาเท่านั้น</div>
          </div>
          <Toggle v-model:model-value="isPrototype" />
        </div>
      </q-card-section>
    </q-card>
  </div>
</template>

<script setup lang="ts">
import { onMounted, onUnmounted, computed } from 'vue';
import SelectDate from 'src/components/common/SelectDate.vue';
import TextField from 'src/components/common/TextField.vue';
import HourDropdown from 'src/components/common/HourDropdown.vue';
import MinDropdown from 'src/components/common/MinDropdown.vue';
import Toggle from 'src/components/common/ToggleBtn.vue';
import { useRoute } from 'vue-router';
import { useAssessmentStore } from 'src/stores/asm';
import { useAutoSave } from 'src/composables/useAutoSave';
import { useQuizSettings } from 'src/composables/useQuizSettings';
import { quizSettingsService } from 'src/services/asm/quizSettingsService';

const route = useRoute();
const paramId = route.params.id as string;
const assessmentStore = useAssessmentStore();

// Create computed ref for the current assessment
const currentAssessment = computed({
  get: () => assessmentStore.currentAssessment,
  set: (value) => assessmentStore.setCurrentAssessment(value),
});

// Use the quiz settings composable
const {
  selectedDate,
  selectedDateEnd,
  hour,
  minute,
  attemptLimit,
  passRatio,
  isPrototype,
  loadFromAssessment,
} = useQuizSettings(currentAssessment);

// Use the auto-save composable with new quiz settings service
useAutoSave(currentAssessment, {
  debounceMs: 500,
  onSave: async (data) => {
    // Only send settings-related fields to the new endpoint
    const settingsData = {
      startAt: data.startAt || undefined,
      endAt: data.endAt || undefined,
      timeout: data.timeout,
      submitLimit: data.submitLimit,
      passRatio: data.passRatio,
      isPrototype: data.isPrototype,
    };

    await quizSettingsService.updateSettings(Number(paramId), settingsData);
  },
  onError: (error) => {
    console.error('Error saving quiz settings:', error);
  },
});

onMounted(async () => {
  try {
    // Use the new quiz settings API to get only settings-related data
    const settingsData = await quizSettingsService.getSettings(Number(paramId));

    // Convert settings data to full assessment format for store compatibility
    const assessmentData = {
      ...settingsData,
      // Add minimal required fields for store compatibility
      creatorUserId: '0', // Changed to string
      programId: 0,
      createdAt: '',
      linkURL: '',
      responseEdit: false,
      status: false,
      totalScore: 0,
    };

    assessmentStore.setCurrentAssessment(assessmentData);

    if (assessmentStore.currentAssessment) {
      loadFromAssessment(assessmentData);
    } else {
      console.error('No assessment settings data found');
    }
  } catch (error) {
    console.error('Error fetching quiz settings:', error);
  }
});

onUnmounted(() => {
  if (assessmentStore.currentAssessment) {
    assessmentStore.clearCurrentAssessment();
  }
});
</script>

<style scoped>
.custom-card {
  width: 1100px;
  height: 600px;
  border-radius: 12px;
  flex: none;
  order: 0;
  flex-grow: 0;
}
</style>
