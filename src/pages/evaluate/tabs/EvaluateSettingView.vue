<script setup lang="ts">
import { computed, watch, onMounted, onActivated, onDeactivated, onUnmounted } from 'vue';
import { useBlockCreatorStore } from 'src/stores/block_creator';
import SelectDate from 'src/components/common/SelectDate.vue';
import Toggle from 'src/components/common/ToggleBtn.vue';
import { useRoute } from 'vue-router';
import { useAutoSave } from 'src/composables/useAutoSave';
import { useEvaluateSettings } from 'src/composables/useEvaluateSettings';
import { evaluateSettingsService } from 'src/services/asm/evaluateSettingsService';

const route = useRoute();
const paramId = route.params.id as string;
const blockCreatorStore = useBlockCreatorStore();

// Create computed ref for the current assessment
const currentAssessment = computed({
  get: () => blockCreatorStore.currentAssessment,
  set: (value) => {
    if (value) {
      blockCreatorStore.currentAssessment = value;
    }
  },
});

// Use the evaluate settings composable
const {
  startDate,
  endDate,
  responseEdit,
  globalIsRequired,
  isPrototype,
  limitOneSubmission,
  loadFromAssessment,
  calculateActualGlobalIsRequired,
} = useEvaluateSettings(currentAssessment);

// Use the auto-save composable with new evaluate settings service
useAutoSave(currentAssessment, {
  debounceMs: 500,
  onSave: async (data) => {
    // Only send settings-related fields to the new endpoint
    const settingsData = {
      startAt: data.startAt || undefined,
      endAt: data.endAt || undefined,
      responseEdit: data.responseEdit,
      submitLimit: data.submitLimit,
      isPrototype: data.isPrototype,
    };

    await evaluateSettingsService.updateSettings(Number(paramId), settingsData);
  },
  onError: (error) => {
    console.error('Error saving evaluate settings:', error);
  },
});

// Separate watcher for globalIsRequired since it requires complex block updates
watch(globalIsRequired, async (newValue, oldValue) => {
  if (newValue === oldValue || !blockCreatorStore.currentAssessment) {
    return;
  }

  console.log('Global isRequired changed by user:', newValue);

  // Update the globalIsRequired property in the assessment
  blockCreatorStore.currentAssessment.globalIsRequired = Boolean(newValue);

  // Update all itemBlocks to match the global setting
  if (blockCreatorStore.currentAssessment.itemBlocks) {
    // Import AssessmentService for API calls
    const { AssessmentService } = await import('src/services/asm/assessmentService');
    const assessmentService = new AssessmentService('evaluate');

    // Update each eligible block both locally and via API
    const updatePromises = blockCreatorStore.currentAssessment.itemBlocks
      .filter((itemBlock) => itemBlock.type !== 'HEADER' && itemBlock.type !== 'IMAGE')
      .map(async (itemBlock) => {
        // Update local state first (optimistic update)
        itemBlock.isRequired = Boolean(newValue);

        try {
          // Update via API
          const updatedBlock = await assessmentService.updateBlock({
            ...itemBlock,
            isRequired: Boolean(newValue),
          });

          if (updatedBlock) {
            console.log(`✅ Updated block ${itemBlock.id} isRequired to ${newValue} via API`);
            // Update with API response (in case there are differences)
            Object.assign(itemBlock, updatedBlock);
          }
        } catch (error) {
          console.error(`❌ Failed to update block ${itemBlock.id} via API:`, error);
          // Note: We don't revert here as the global operation should continue
        }
      });

    // Wait for all API calls to complete
    await Promise.allSettled(updatePromises);

    console.log('Updated isRequired for all itemBlocks:', newValue);

    // Trigger reactivity
    blockCreatorStore.currentAssessment.itemBlocks = [
      ...blockCreatorStore.currentAssessment.itemBlocks,
    ];
  }
});

onMounted(async () => {
  try {
    // Use the new evaluate settings API to get only settings-related data
    const settingsData = await evaluateSettingsService.getSettings(Number(paramId));

    // Convert settings data to full assessment format for store compatibility
    const assessmentData = {
      ...settingsData,
      // Add minimal required fields for store compatibility
      creatorUserId: '0', // Changed to string
      programId: 0,
      createdAt: '',
      linkURL: '',
      responseEdit: settingsData.responseEdit || false,
      status: false,
      totalScore: 0,
      timeout: 0,
      passRatio: 0.5,
    };

    blockCreatorStore.currentAssessment = assessmentData;

    if (blockCreatorStore.currentAssessment) {
      loadFromAssessment(assessmentData);

      // Load the full assessment with itemBlocks for global isRequired calculation
      await blockCreatorStore.fetchAssessmentById(Number(paramId));
      if (blockCreatorStore.currentAssessment) {
        const actualGlobalIsRequired = calculateActualGlobalIsRequired();
        globalIsRequired.value = actualGlobalIsRequired;

        // Update the stored value if it doesn't match the actual state
        if (blockCreatorStore.currentAssessment.globalIsRequired !== actualGlobalIsRequired) {
          blockCreatorStore.currentAssessment.globalIsRequired = actualGlobalIsRequired;
          console.log(
            `🔄 Corrected globalIsRequired from stored value to actual state: ${actualGlobalIsRequired}`,
          );
        }
      }
    }
  } catch (error) {
    console.error('Error loading evaluate settings:', error);
    // Fallback to the original method
    await blockCreatorStore.fetchAssessmentById(Number(paramId));
    if (blockCreatorStore.currentAssessment) {
      loadFromAssessment(blockCreatorStore.currentAssessment);
      const actualGlobalIsRequired = calculateActualGlobalIsRequired();
      globalIsRequired.value = actualGlobalIsRequired;
    }
  }
});

onUnmounted(() => {
  console.log('EvaluateSettingView component unmounted');
});

// Handle keep-alive activation
onActivated(() => {
  console.log('EvaluateSettingView component activated');
  // Refresh data from store if needed
  if (blockCreatorStore.currentAssessment) {
    console.log('Refreshing settings from store on activation');
    loadFromAssessment(blockCreatorStore.currentAssessment);
    const actualGlobalIsRequired = calculateActualGlobalIsRequired();
    globalIsRequired.value = actualGlobalIsRequired;

    // Update the stored value if it doesn't match the actual state
    if (blockCreatorStore.currentAssessment.globalIsRequired !== actualGlobalIsRequired) {
      blockCreatorStore.currentAssessment.globalIsRequired = actualGlobalIsRequired;
      console.log(`🔄 Corrected globalIsRequired on activation: ${actualGlobalIsRequired}`);
    }
  }
});

// Handle keep-alive deactivation
onDeactivated(() => {
  console.log('EvaluateSettingView component deactivated');
});
</script>

<template>
  <q-page class="q-pa-md">
    <q-card class="evaluate-item" style="border-radius: 10px; max-height: 100vh; overflow-y: auto">
      <!-- Header -->
      <q-card-section class="row items-center justify-between">
        <div class="text-h5 text-weight-bold">ตั้งค่า</div>
      </q-card-section>

      <q-separator class="q-mt-md" />

      <q-card-section class="column q-gutter-md">
        <div class="row items-start justify-between">
          <div>
            <div class="text-h5 text-weight-bold">กำหนดขอบเขตเวลา</div>
            <div class="text-grey text-h10">กำหนดวันที่เพื่อเปิด-ปิดแบบสอบถามอัตโนมัติ</div>
          </div>

          <div class="row q-gutter-sm">
            <SelectDate
              :model-value="startDate"
              @update:model-value="startDate = $event"
              label="วันที่เริ่มต้น"
            />
            <SelectDate
              :model-value="endDate"
              @update:model-value="endDate = $event"
              label="วันที่สิ้นสุด"
              :disable="startDate === '' || startDate === null"
              :rules="[
                (val: any) => {
                  return val >= startDate || 'วันที่สิ้นสุดต้องมากกว่าหรือเท่ากับวันที่เริ่มต้น';
                },
              ]"
            />
          </div>
        </div>
        <q-separator class="q-mt-md" />

        <!-- Responses -->
        <q-expansion-item
          :model-value="responseEdit || limitOneSubmission"
          header-class="q-pa-none justify-start"
        >
          <template #header>
            <q-item-section>
              <div class="text-h5 text-weight-bold">การตอบกลับ</div>
              <div class="text-grey text-h10">กำหนดวิธีการตอบกลับของผู้ตอบ</div>
            </q-item-section>
          </template>
          <div class="column">
            <div class="sub-row">
              <div>
                <div class="text-h6">สามารถแก้ไขคำตอบ</div>
              </div>
              <Toggle v-model:model-value="responseEdit" />
            </div>
            <div class="sub-row">
              <div>
                <div class="text-h6">จำกัดการส่งได้แค่ครั้งเดียว</div>
              </div>
              <Toggle v-model:model-value="limitOneSubmission" />
            </div>
          </div>
        </q-expansion-item>

        <q-separator class="q-mt-md" />

        <!-- Default Questions -->
        <q-expansion-item :model-value="globalIsRequired" header-class="q-pa-none justify-start">
          <template #header>
            <q-item-section>
              <div>
                <div class="text-h5 text-weight-bold">ค่าตั้งต้นของคำถาม</div>
                <div class="text-grey text-h10">กำหนดค่าตั้งต้นของคำถามทั้งหมดของแบบสอบถามนี้</div>
              </div>
            </q-item-section>
          </template>
          <div class="column">
            <div class="sub-row">
              <div>
                <div class="text-h6">จำเป็นต้องตอบทุกข้อ</div>
              </div>
              <Toggle v-model:model-value="globalIsRequired" />
            </div>
          </div>
        </q-expansion-item>
        <q-separator class="q-mt-md" />
        <div class="row items-start justify-between">
          <div>
            <div class="text-h5 text-weight-bold">เป็นต้นแบบ</div>
            <div class="text-grey text-h10">กำหนดให้ฟอร์มนี้เป็นต้นแบบสำหรับสำเนาเท่านั้น</div>
          </div>
          <div class="q-mt-md">
            <Toggle v-model:model-value="isPrototype" />
          </div>
        </div>
      </q-card-section>
    </q-card>
  </q-page>
</template>
<style scoped>
.sub-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px; /* q-mb-md */
  margin-top: 16px; /* q-mt-md */
  margin-left: 24px; /* q-ml-lg */
}
</style>
