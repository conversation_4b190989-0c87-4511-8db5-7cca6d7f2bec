<script setup lang="ts">
import { ref, onMounted, computed, nextTick } from 'vue';
import { useAuthStore } from 'src/stores/auth';
import { useRouter } from 'vue-router';
import { QIcon, QInput, QBtn, QForm, QCheckbox, QImg, QPage } from 'quasar';
import MainFooter from 'src/components/MainFooter.vue';

// Composables
const router = useRouter();
const authStore = useAuthStore();

// Reactive state
const visiblePassword = ref(false);
const form = ref<QForm | null>(null);
const rememberMe = ref(false);
const isLoading = ref(false);

// Computed properties
const loginButtonLabel = computed(() => (isLoading.value ? 'กำลังเข้าสู่ระบบ...' : 'เข้าสู่ระบบ'));

const isFormValid = computed(
  () => !!(authStore.loginUsername?.trim() && authStore.loginPassword?.trim()),
);

// Form validation rules
const usernameRules = [(val: string) => !!val || 'กรุณากรอกชื่อผู้ใช้'];
const passwordRules = [(val: string) => !!val || 'กรุณากรอกรหัสผ่าน'];

// Methods
const login = async () => {
  if (isLoading.value) return;

  const valid = await form.value?.validate();
  if (!valid) return;

  try {
    isLoading.value = true;
    const success = await authStore.login();

    if (success) {
      // Handle remember me functionality
      if (rememberMe.value) {
        localStorage.setItem('rememberMe', 'true');
      } else {
        localStorage.removeItem('rememberMe');
      }

      // Wait for auth state to be fully updated
      await nextTick();
      
      // Small delay to ensure all async operations complete
      await new Promise(resolve => setTimeout(resolve, 100));

      // Handle redirect - use push instead of replace to avoid navigation issues
      const redirectPath = localStorage.getItem('redirectAfterLogin');
      if (redirectPath && redirectPath !== '/login') {
        localStorage.removeItem('redirectAfterLogin');
        await router.push(redirectPath).catch((err) => {
          console.error('Redirect error:', err);
          // Fallback to home if redirect fails
          router.push({ name: 'home' }).catch(console.error);
        });
      } else {
        await router.push({ name: 'home' }).catch(console.error);
      }
    }
  } catch (error) {
    console.error('Login error:', error);
  } finally {
    isLoading.value = false;
  }
};

const togglePasswordVisibility = () => {
  visiblePassword.value = !visiblePassword.value;
};

// Lifecycle
onMounted(async () => {
  // Reset all auth state and clear form fields when login page loads
  authStore.resetState();

  // Check if user wants to be remembered
  const shouldRemember = localStorage.getItem('rememberMe') === 'true';
  rememberMe.value = shouldRemember;

  // Reset form validation state after next tick
  await nextTick();
  form.value?.resetValidation();
});
</script>

<template>
  <q-page class="login-page flex justify-center">
    <div class="login-bg-gradient"></div>
    <div class="row items-center" style="gap: 100px">
      <div class="col-auto brand-img-container">
        <q-img
          src="/brand/brand-login.webp"
          class="bg-logo-img q-mx-auto flex"
          loading="lazy"
          no-spinner
          :ratio="1"
        />
      </div>
      <div class="col-auto login-section">
        <div class="logo-container">
          <q-img
            src="/brand/buu-logo-og.png"
            class="login-logo"
            loading="lazy"
            no-spinner
            :ratio="1"
          />
          <q-img
            src="/brand/brand-login.webp"
            class="brand-logo-small"
            loading="lazy"
            no-spinner
            :ratio="1"
          />
        </div>
        <div class="login-title">มหาวิทยาลัยบูรพา</div>
        <div class="col q-mt-lg">
          <q-card class="q-pa-lg" style="width: 400px">
            <div class="login-subtitle">
              <q-icon name="lock" color="primary" size="20px" />
              <span class="text-bold">เข้าสู่ระบบ HRD</span>
            </div>
            <q-form ref="form" class="q-gutter-y-md q-mt-lg" @submit="login()">
              <q-input
                color="accent"
                dense
                outlined
                data-cy="login_username"
                label="อีเมล/รหัสพนักงาน"
                v-model="authStore.loginUsername"
                :error="authStore.incorrectUsernamePasswordStatus"
                :error-message="
                  authStore.incorrectUsernamePasswordStatus
                    ? 'ชื่อผู้ใช้หรือรหัสผ่านไม่ถูกต้อง'
                    : undefined
                "
                :rules="usernameRules"
                :disable="isLoading"
                @keyup.enter="login"
                class="login-input"
                autocomplete="user"
              >
                <template v-slot:prepend>
                  <q-icon name="account_circle" color="grey-6"></q-icon>
                </template>
              </q-input>
              <q-input
                color="accent"
                :type="visiblePassword ? 'text' : 'password'"
                dense
                outlined
                data-cy="login_password"
                label="รหัสผ่าน"
                v-model="authStore.loginPassword"
                :error="authStore.incorrectUsernamePasswordStatus"
                :error-message="
                  authStore.incorrectUsernamePasswordStatus
                    ? 'ชื่อผู้ใช้หรือรหัสผ่านไม่ถูกต้อง'
                    : undefined
                "
                :rules="passwordRules"
                :disable="isLoading"
                @keyup.enter="login"
                class="login-input"
                autocomplete="current-password"
              >
                <template v-slot:prepend>
                  <q-icon name="key" color="grey-6"></q-icon>
                </template>
                <template v-slot:append>
                  <q-icon
                    :data-cy="visiblePassword ? 'i-eye' : 'i-eyeOff'"
                    :name="visiblePassword ? 'visibility' : 'visibility_off'"
                    color="grey-4"
                    @click="togglePasswordVisibility"
                    class="cursor-pointer"
                    :disable="isLoading"
                  ></q-icon>
                </template>
              </q-input>
              <q-checkbox v-model="rememberMe" label="จำฉันไว้ในระบบ" :disable="isLoading" />
              <q-btn
                type="submit"
                unelevated
                dense
                class="login-btn"
                data-cy="login_btn"
                :label="loginButtonLabel"
                :loading="isLoading"
                :disable="!isFormValid || isLoading"
                color="primary"
              />
              <a href="https://myid.buu.ac.th/" class="forgot-password-link">ลืมรหัสผ่าน?</a>
            </q-form>
          </q-card>
        </div>
      </div>
    </div>

    <div class="login-header-outer"></div>

    <MainFooter style="position: fixed !important" />
  </q-page>
</template>

<style scoped>
/* Layout Components */
.login-page {
  min-height: 100vh;
  position: relative;
  overflow: hidden;
}

.login-bg-gradient {
  position: fixed;
  inset: 0;
  background-image: url('/mockup/BG.svg');
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  z-index: -1;
}

.login-bg-logo {
  position: fixed;
  inset: 0;
  width: 60vw;
  z-index: 1;
  pointer-events: none;
  display: flex;
  justify-content: center;
  align-items: center;
}

.bg-logo-img {
  width: 700px;
  height: 700px;
  object-fit: contain;
}

/* Header Section */
.login-header-outer {
  position: absolute;
  top: 110px;
  right: 58px;
  width: 448px;
  text-align: center;
  z-index: 4;
  pointer-events: none;
}

.login-logo {
  width: 120px;
  height: 120px;
  margin: 0 auto 8px;
  pointer-events: auto;
  display: flex;
}

.brand-logo-small {
  width: 120px;
  height: 120px;
  margin: 0 auto 8px;
  pointer-events: auto;
  display: none; /* Hidden by default, shown in smaller screens */
}

.logo-container {
  display: flex;
  align-items: center;
  justify-content: center;
  width: fit-content !important;
  margin: auto;
  gap: 20px;
}

.login-title {
  font-size: 20px;
  font-weight: 600;
  color: #222;
  pointer-events: auto;
  display: flex;
  justify-content: center;
}

.login-subtitle {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  color: #222;
  font-size: 18px;
  font-weight: bold;
}

.login-form {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

/* Form Elements */
.login-label {
  font-weight: 500;
  color: #222;
  font-size: 15px;
  margin-bottom: 4px;
}

.login-input {
  font-size: 16px;
}

.login-btn {
  width: 100%;
  background: #ffcb05 !important;
  color: #222 !important;
  box-shadow: 0 2px 8px 0 rgba(255, 203, 5, 0.13);
  border-radius: 8px;
  letter-spacing: 0.5px;
  font-size: 16px;
  font-weight: bold;
}

.login-btn:disabled {
  opacity: 0.6;
}

.forgot-password-link {
  color: #ffb300;
  font-weight: 500;
  font-size: 14px;
  text-decoration: none;
  text-align: right;
  display: block;
}

.cursor-pointer {
  cursor: pointer;
}

/* Desktop Small / Laptop */
@media (max-width: 1280px) {
  .brand-img-container {
    display: none !important;
  }

  .brand-logo-small {
    display: flex !important;
  }

  .login-page .row {
    justify-content: center;
  }
}

/* Tablet Responsive */
@media (max-width: 768px) and (min-width: 601px) {
  .bg-logo-img {
    width: 500px !important;
    height: 500px !important;
  }

  .login-logo {
    width: 100px !important;
    height: 100px !important;
  }

  .brand-logo-small {
    width: 100px !important;
    height: 100px !important;
  }
}

/* Mobile Responsive */
@media (max-width: 600px) {
  .login-bg-gradient {
    background-image: url('/mockup/BG-PHONE.svg');
    background-position: right center;
  }

  .brand-img-container {
    display: none !important;
  }

  .login-section {
    width: 100% !important;
    display: flex;
    flex-direction: column;
    align-items: center;
  }

  .logo-container {
    flex-direction: row;
    gap: 20px;
  }

  .bg-logo-img {
    width: 300px !important;
    height: 300px !important;
  }

  .login-logo {
    width: 80px !important;
    height: 80px !important;
    margin-bottom: 8px;
  }

  .brand-logo-small {
    width: 80px !important;
    height: 80px !important;
    margin-bottom: 8px;
  }

  .login-form-container {
    position: static;
    transform: none;
    width: calc(100% - 16px);
    margin: 0 8px;
    max-width: 340px;
    margin-left: auto;
    margin-right: auto;
  }

  .login-subtitle {
    font-size: 15px;
    margin-bottom: 16px;
  }

  .login-btn {
    height: 36px;
  }
}

/* Tablet Responsive */
@media (max-width: 768px) and (min-width: 601px) {
  .bg-logo-img {
    width: 500px !important;
    height: 500px !important;
  }

  .login-logo {
    width: 100px !important;
    height: 100px !important;
  }

  .brand-logo-small {
    width: 100px !important;
    height: 100px !important;
  }
}

/* Large Mobile/Small Tablet */
@media (max-width: 480px) {
  .bg-logo-img {
    width: 250px !important;
    height: 250px !important;
  }

  .login-logo {
    width: 60px !important;
    height: 60px !important;
  }

  .brand-logo-small {
    width: 60px !important;
    height: 60px !important;
  }

  .login-page {
    padding: 10rem !important;
  }
}
</style>
