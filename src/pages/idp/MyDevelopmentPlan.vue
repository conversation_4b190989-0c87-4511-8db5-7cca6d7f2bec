<template>
  <q-page padding>
    <div class="text-h5 q-mb-sm">จัดการแแผนพัฒนารายบุคคล</div>
    <div class="text-h6 q-mb-md">ข้อมูลบุคลากร</div>

    <div class="column items-start">
      <UserProfile class="q-mb-md" />
      <TabNavigation :tabs="tabItems" v-model="selectedTab" />
    </div>
    <div v-if="selectedTab === 'core'">
      <div v-if="!selectedSkill" style="display: flex; gap: 24px">
        <TableManagePlan
          :key="selectedSkill ? 'detail1' : 'list1'"
          :rows="rows"
          :columns="columns"
          :selectedRowId="selectedRowId1"
          @set-selected-row-id="setSelectedRowId1"
          @select-row="(row: SkillRow) => handleSelectSkill(row, 1)"
        />
        <TableManagePlan
          :key="selectedSkill ? 'detail2' : 'list2'"
          :rows="rows1"
          :columns="columns1"
          :selectedRowId="selectedRowId2"
          @set-selected-row-id="setSelectedRowId2"
          @select-row="(row: SkillRow) => handleSelectSkill(row, 2)"
        />
      </div>
      <div v-else>
        <div style="display: flex; gap: 24px; align-items: flex-start">
          <div style="flex: 1; min-width: 0">
            <TableManagePlan
              :key="selectedSkill ? 'detail1' : 'list1'"
              :rows="rows"
              :columns="columns"
              :selectedRowId="selectedRowId1"
              @set-selected-row-id="setSelectedRowId1"
              @select-row="(row: SkillRow) => handleSelectSkill(row, 1)"
            />
            <TableManagePlan
              :key="selectedSkill ? 'detail2' : 'list2'"
              :rows="rows1"
              :columns="columns1"
              :selectedRowId="selectedRowId2"
              @set-selected-row-id="setSelectedRowId2"
              @select-row="(row: SkillRow) => handleSelectSkill(row, 2)"
            />
          </div>
          <div style="width: 620px; min-width: 520px">
            <div v-if="selectedSkill" style="margin-bottom: 16px">
              <SkillCard :skill="selectedSkill" @close="handleClearSkill" />
            </div>
            <CourseCard />
          </div>
        </div>
      </div>
    </div>
    <div v-if="selectedTab === 'academic'">
      <div style="display: flex; justify-content: flex-end; margin-bottom: 16px">
        <q-select
          v-model="selectedPlan"
          :options="planOptions"
          label="เลือกแผนบุคลากร"
          outlined
          dense
          style="width: 320px"
        />
      </div>
      <TimeLine />
    </div>
    <div v-if="selectedTab === 'support'">
      <div class="row justify-end" style="margin-top: 20px">
        <SearchBar v-if="showSearch" @search="handleSearch" />
      </div>
      <div style="margin-top: 24px">
        <q-table
          :rows="name"
          :columns="FollowUpColumns"
          row-key="id"
          flat
          bordered
          wrap-cells
          separator="cell"
          :loading="loading"
          :pagination="pagination"
        >
          <template v-slot:body-cell-actions="{ row }">
            <q-td class="text-center">
              <div class="q-gutter-x-sm flex justify-center">
                <q-btn
                  dense
                  unelevated
                  class="view-icon"
                  icon="visibility"
                  @click="onClickDialogApprove(row)"
                />
              </div>
            </q-td>
          </template>
          <template v-slot:body-cell-is_active="{ row }">
            <q-td class="flex flex-center" style="height: 54px !important">
              <div :class="['status-chip', getStatusClass(row.status)]" style="width: 100px">
                {{ getStatusLabel(row.status) }}
              </div>
            </q-td>
          </template>
        </q-table>
      </div>
      <q-dialog v-model="showDialogApprove">
        <DialogApprove
          v-if="showDialogApprove"
          :skill-name="skillNameForDialog"
          :status="dialogStatus"
          :reason="dialogReason"
          :evidences="dialogEvidences"
          @close="closeDialogApprove"
        />
      </q-dialog>
    </div>
    <div v-if="selectedTab === 'admin'">
      <div class="row justify-end" style="margin-top: 20px">
        <SearchBar v-if="showSearch" @search="handleSearch" />
      </div>
      <div style="margin-top: 24px">
        <q-table
          :rows="skillEvaluationRows"
          :columns="SkillEvaluationColumns"
          row-key="id"
          flat
          bordered
          wrap-cells
          separator="cell"
          :loading="loading"
          :pagination="pagination"
        >
          <template v-slot:body-cell-is_pass="{ row }">
            <q-td class="flex flex-center">
              <div
                :class="['status-chip', getSkillEvaluationStatusClass(row.is_pass)]"
                style="width: 150px"
              >
                {{ getSkillEvaluationStatusLabel(row.is_pass) }}
              </div>
            </q-td>
          </template>
        </q-table>
      </div>
    </div>
  </q-page>
</template>

<script setup lang="ts">
import UserProfile from 'src/components/common/UserProfile.vue';
import { ref } from 'vue';
import TabNavigation from 'src/components/common/TabNavigation.vue';
import TableManagePlan from 'src/components/plan/TableManagePlan.vue';
import SkillCard from 'src/components/plan/SkillCard.vue';
import CourseCard from 'src/components/plan/CourseCard.vue';
import TimeLine from 'src/components/plan/TimeLine.vue';
import { FollowUpColumns } from 'src/data/table_columns';
import DialogApprove from 'src/components/idp/DialogApprove.vue';
import SearchBar from 'src/components/SearchBar.vue';
import { SkillEvaluationColumns } from 'src/data/table_columns';
const TAB_KEY = 'selected-plan-tab';
const selectedTab = ref(localStorage.getItem(TAB_KEY) || 'core');
const loading = ref(false);
const showDialogApprove = ref(false);
const skillNameForDialog = ref('');
const showSearch = ref(true);
const emit = defineEmits<Emits>();
interface Emits {
  (e: 'search', keyword: string): void;
  (e: 'create'): void;
}
const handleSearch = (keyword: string) => {
  emit('search', keyword);
  if (!keyword) {
    name.value = allNames;
  } else {
    name.value = allNames.filter((item) => item.name.toLowerCase().includes(keyword.toLowerCase()));
  }
};

const tabItems = [
  { label: 'แผนพัฒนา', value: 'core' },
  { label: 'เส้นทางพัฒนา', value: 'academic' },
  { label: 'ติดตามหลักฐาน', value: 'support' },
  { label: 'ความรู้และทักษะ', value: 'admin' },
];
const columns = [
  {
    name: 'name',
    label: 'ความรู้และทักษะทั่วไป',
    field: 'name',
    align: 'left',
  },
];

const rows = [
  { id: 1, name: 'การอ่าน การตีความ และการใช้กฎระเบียบทั่วไปของมหาวิทยาลัย', hasSkill: true },
  { id: 2, name: 'การเขียนหนังสือหรือเอกสารราชการ' },
  { id: 3, name: 'การพัฒนาทักษะภาษาอังกฤษ', hasSkill: true },
  { id: 4, name: 'การทำงานเป็นทีม' },
  { id: 5, name: 'การทำงานเป็นทีม' },
];
const columns1 = [
  {
    name: 'name',
    label: 'ความรู้และทักษะเฉพาะด้านวิชาการ',
    field: 'name',
    align: 'left',
  },
];

const rows1 = [
  { id: 1, name: 'การจัดการเรียนการสอน การให้คำปรึกษา การวัด และประเมินผล' },
  { id: 2, name: 'การพัฒนาสื่อการสอนหรือการจัดการเรียนการสอนรูปแบบใหม่' },
  { id: 3, name: 'การบริหารโครงการวิจัยและการบริการวิชาการ' },
  { id: 4, name: 'แนวทางการพัฒนาผลงานทางวิชาการ' },
  { id: 5, name: 'แนวทางการพัฒนาผลงานทางวิชาการ' },
];
const name = ref([
  { id: 1, name: 'การจัดการเรียนการสอน การให้คำปรึกษา การวัด และประเมินผล', status: 'approved' },
  { id: 2, name: 'การใช้งานระบบสารสนเทศของมหาวิทยาลัย', status: 'pending' },
  { id: 3, name: 'การใช้งานอุปกรณ์และโปรแกรมคอมพิวเตอร์ในสำนักงาน', status: 'rejected' },
  { id: 4, name: 'แนวทางการพัฒนาผลงานทางวิชาการ', status: 'approved' },
  { id: 5, name: 'การพัฒนาทักษะภาษาอังกฤษ', status: 'pending' },
  { id: 6, name: 'การทำงานเป็นทีม', status: 'revise' },
]);
const allNames = [
  { id: 1, name: 'การจัดการเรียนการสอน การให้คำปรึกษา การวัด และประเมินผล', status: 'approved' },
  { id: 2, name: 'การใช้งานระบบสารสนเทศของมหาวิทยาลัย', status: 'pending' },
  { id: 3, name: 'การใช้งานอุปกรณ์และโปรแกรมคอมพิวเตอร์ในสำนักงาน', status: 'rejected' },
  { id: 4, name: 'แนวทางการพัฒนาผลงานทางวิชาการ', status: 'approved' },
  { id: 5, name: 'การพัฒนาทักษะภาษาอังกฤษ', status: 'pending' },
  { id: 6, name: 'การทำงานเป็นทีม', status: 'revise' },
];
const skillEvaluationRows = [
  {
    id: 1,
    name: 'การทำงานเป็นทีม ',
    type: 'ทั่วไป',
    way: 'อบรม ',
    date: '26/05/2568',
    is_pass: 'pass',
  },
  {
    id: 2,
    name: 'การพัฒนาทักษะภาษาอังกฤษ ',
    type: 'ทั่วไป',
    way: 'e-learning ',
    date: '26/05/2568',
    is_pass: 'pending',
  },
  {
    id: 3,
    name: 'การใช้ระบบสารสนเทศของมหาวิทยาลัย',
    type: 'วิชาการ',
    way: 'อบรม ',
    date: '26/05/2568',
    is_pass: 'pass',
  },
  {
    id: 4,
    name: 'แนวทางการพัฒนาผลงานทางวิชาการ',
    type: 'วิชาการ',
    way: 'in-house ',
    date: '30/05/2568',
    is_pass: 'reject',
  },
];
const selectedSkill = ref<null | { id: number; name: string }>(null);
const selectedRowId1 = ref<number | null>(null);
const selectedRowId2 = ref<number | null>(null);
const pagination = ref({
  sortBy: 'id',
  descending: false,
  page: 1,
  rowsPerPage: 10,
  rowsNumber: 0,
});
type SkillRow = { id: number; name: string };

function handleSelectSkill(skill: SkillRow, table: 1 | 2) {
  if (selectedSkill.value && selectedSkill.value.id === skill.id) {
    handleClearSkill();
    return;
  }
  selectedSkill.value = skill;
  if (table === 1) {
    selectedRowId2.value = null;
  } else {
    selectedRowId1.value = null;
  }
}
function handleClearSkill() {
  selectedSkill.value = null;
  selectedRowId1.value = null;
  selectedRowId2.value = null;
}

function setSelectedRowId1(id: number | null) {
  selectedRowId1.value = id;
}
function setSelectedRowId2(id: number | null) {
  selectedRowId2.value = id;
}
const dialogStatus = ref('');
const dialogReason = ref('');
function onClickDialogApprove(row: { name: string; status: string }) {
  skillNameForDialog.value = row.name;
  dialogStatus.value = row.status;
  dialogReason.value = row.status === 'rejected' ? 'ไม่สามารถนำมาเป็นหลักฐานได้' : '';
  showDialogApprove.value = true;
}

function closeDialogApprove() {
  showDialogApprove.value = false;
}

function getStatusLabel(status: string) {
  if (status === 'approved') return 'อนุมัติ';
  if (status === 'pending') return 'รออนุมัติ';
  if (status === 'rejected') return 'ไม่อนุมัติ';
  if (status === 'revise') return 'ส่งกลับแก้ไข';
  return '';
}
function getStatusClass(status: string) {
  if (status === 'approved') return 'status-approved';
  if (status === 'pending') return 'status-pending';
  if (status === 'rejected') return 'status-rejected';
  if (status === 'revise') return 'status-revise';
  return '';
}

function getSkillEvaluationStatusLabel(is_pass: string) {
  if (is_pass === 'pass') return 'ผ่านการประเมิน';
  if (is_pass === 'pending') return 'อยู่ระหว่างการประเมิน';
  if (is_pass === 'reject') return 'ไม่ผ่านการประเมิน';
  return '';
}

function getSkillEvaluationStatusClass(is_pass: string) {
  if (is_pass === 'pass') return 'status-approved';
  if (is_pass === 'pending') return 'status-pending';
  if (is_pass === 'reject') return 'status-rejected';
  return '';
}
const selectedPlan = ref('');
const planOptions = [
  { label: 'แผนบุคลากรสายวิชาการ', value: 'academic' },
  { label: 'แผนบุคลากรสายสนับสนุน', value: 'support' },
];
const dialogEvidences = ref([
  { name: 'certificate1.pdf', url: '/mockup/certificate.pdf' },
  { name: 'certificate2.pdf', url: '/mockup/certificate.pdf' },
]);
</script>

<style scoped lang="scss">
.right-panel {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  min-width: 320px;
  max-width: 420px;
}
.status-chip {
  height: 25px;
  border-radius: 50px;
  background-color: #e0e0e0;
  color: #333;
  font-weight: 500;
  font-size: 14px;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 0 14px;
  box-sizing: border-box;
}
.status-approved {
  background: rgba(37, 173, 69, 0.15);
  border: 1px solid #25ad45;
  color: #25ad45;
}
.status-pending {
  background: rgba(255, 193, 7, 0.15);
  border: 1px solid #ffc107;
  color: #ffc107;
}
.status-rejected {
  background: rgba(220, 53, 69, 0.15);
  border: 1px solid #dc3545;
  color: #dc3545;
}
.status-revise {
  background: rgba(255, 193, 7, 0.15);
  border: 1px solid #ff8400;
  color: #ff8400;
}
:deep(.q-table td),
:deep(.q-table th) {
  vertical-align: middle !important;
}
</style>
