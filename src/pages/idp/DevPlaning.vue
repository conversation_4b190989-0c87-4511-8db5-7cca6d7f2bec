<template>
  <q-page padding>
    <div v-if="loading" class="text-center">
      <q-spinner size="lg" />
      <div class="q-mt-sm">กำลังโหลดข้อมูล...</div>
    </div>
    <div v-else-if="developmentPlan">
      <div class="text-h5 q-mb-md text-weight-bold">
        จัดการแผนพัฒนาบุคลากร ปี {{ globalStore.developmentPlanYear }}
      </div>
      <div class="text-body1 q-mb-md text-weight-bold">
        แผนพัฒนาบุคลากร ปี {{ globalStore.developmentPlanYear }}
      </div>
      <div class="q-mb-md row items-center">
        <span class="q-mr-sm">สถานะ :</span>
        <StatusCapsule :published="developmentPlan.isActive" class="q-mr-sm" />
        <q-btn class="q-ml-sm bg-black text-white" @click="openPublicationDialog">เผยแพร่</q-btn>
      </div>
      <div class="q-mb-md row items-center">
        <span class="q-mr-sm">ค้นหาตาม :</span>
        <SearchDropDownBar placeholder="หน่วยงาน/ส่วนงาน" :options="searchOptions" />
      </div>
      <div v-if="selectedTab === 'position'" class="q-mb-md row items-center">
        <span class="q-mr-sm">ค้นหาตาม :</span>
        <SearchDropDownBar placeholder="ประเภทสายงาน" :options="searchTypeOptions" />
        <SearchDropDownBar placeholder="ตำแหน่ง" :options="searchPositionOptions" />
        <SearchDropDownBar placeholder="ระดับ" :options="searchRankOptions" />
      </div>
      <div class="q-mb-md">
        <TabNavigation
          v-model="selectedTab"
          :tabs="devPlanTabs.map((tab) => ({ label: tab.label, value: tab.name }))"
          class="q-mb-md"
        />
      </div>
    </div>
    <div v-else class="text-center text-grey-6">ไม่พบข้อมูลแผนพัฒนา</div>

    <div v-if="!loading && developmentPlan">
      <div v-if="selectedTab === 'all'">
        <div class="q-mb-md">
          <div class="row items-center justify-between q-mb-md">
            <span class="text-weight-bold">ความรู้และทักษะทั่วไปของบุคลากร</span>
            <q-btn
              label="เพิ่ม"
              color="accent"
              icon="add"
              dense
              size="md"
              class="q-pr-sm"
              @click="() => handleAddItem('all')"
            />
          </div>
          <div v-if="categorizedData['all']?.length">
            <div v-for="(typePlan, index) in categorizedData['all']" :key="index" class="q-mb-md">
              <CompetencyListSection
                :title="`${typePlan.ageWork?.name || 'ทั่วไป'}`"
                :items="typePlan.skills?.map((skill: any) => skill.name) || []"
                v-model="isListOpen"
                @remove="(idx: number) => removeSkill('all', index, idx)"
                @add="() => handleAddItemWithAgeWork('all', typePlan.ageWork || null)"
              />
            </div>
          </div>
          <div v-else class="text-center text-grey-6 q-pa-md">ไม่มีข้อมูลสำหรับแท็บนี้</div>
        </div>
      </div>
      <div v-else-if="selectedTab === 'admin'">
        <div class="row items-center justify-between q-mb-md">
          <span class="text-weight-bold">ความรู้และทักษะทั่วไปของผู้บริหาร</span>
          <q-btn
            label="เพิ่ม"
            color="accent"
            icon="add"
            dense
            size="md"
            class="q-pr-sm"
            @click="() => handleAddItem('admin')"
          />
        </div>
        <div v-if="categorizedData['admin']?.length">
          <div v-for="(typePlan, index) in categorizedData['admin']" :key="index" class="q-mb-md">
            <CompetencyListSection
              :title="`${typePlan.name}`"
              :items="typePlan.skills?.map((skill: any) => skill.name) || []"
              v-model="isListOpenAdmin"
              @remove="(idx: number) => removeSkill('admin', index, idx)"
              @add="() => handleAddItem('admin')"
            />
          </div>
        </div>
        <div v-else class="text-center text-grey-6 q-pa-md">ไม่มีข้อมูลสำหรับแท็บนี้</div>
      </div>
      <div v-else-if="selectedTab === 'academic'">
        <div class="row items-center justify-between q-mb-md">
          <span class="text-weight-bold">ความรู้และทักษะเฉพาะด้านสายวิชาการ</span>
          <q-btn
            label="เพิ่ม"
            color="accent"
            icon="add"
            dense
            size="md"
            class="q-pr-sm"
            @click="() => handleAddItem('academic')"
          />
        </div>
        <div v-if="categorizedData['academic']?.length">
          <div
            v-for="(typePlan, index) in categorizedData['academic']"
            :key="index"
            class="q-mb-md"
          >
            <CompetencyListSection
              :title="`${typePlan.ageWork?.name || 'ทั่วไป'}`"
              :items="typePlan.skills?.map((skill: any) => skill.name) || []"
              v-model="isListOpenAcademic"
              @remove="(idx: number) => removeSkill('academic', index, idx)"
              @add="() => handleAddItemWithAgeWork('academic', typePlan.ageWork || null)"
            />
          </div>
        </div>
        <div v-else class="text-center text-grey-6 q-pa-md">ไม่มีข้อมูลสำหรับแท็บนี้</div>
      </div>
      <div v-else-if="selectedTab === 'support'">
        <div class="row items-center justify-between q-mb-md">
          <span class="text-weight-bold">ความรู้และทักษะเฉพาะด้านสายสนับสนุน</span>
          <q-btn
            label="เพิ่ม"
            color="accent"
            icon="add"
            dense
            size="md"
            class="q-pr-sm"
            @click="() => handleAddItem('support')"
          />
        </div>
        <div v-if="categorizedData['support']?.length">
          <div v-for="(typePlan, index) in categorizedData['support']" :key="index" class="q-mb-md">
            <CompetencyListSection
              :title="`${typePlan.ageWork?.name || 'ทั่วไป'}`"
              :items="typePlan.skills?.map((skill: any) => skill.name) || []"
              v-model="isListOpenSupport"
              @remove="(idx: number) => removeSkill('support', index, idx)"
              @add="() => handleAddItemWithAgeWork('support', typePlan.ageWork || null)"
            />
          </div>
        </div>
        <div v-else class="text-center text-grey-6 q-pa-md">ไม่มีข้อมูลสำหรับแท็บนี้</div>
      </div>
      <div v-else-if="selectedTab === 'adminonly'">
        <div class="row items-center justify-between q-mb-md">
          <span class="text-weight-bold">ความรู้และทักษะเฉพาะด้านผู้บริหาร</span>
          <q-btn
            label="เพิ่ม"
            color="accent"
            icon="add"
            dense
            size="md"
            class="q-pr-sm"
            @click="() => handleAddItem('adminonly')"
          />
        </div>
        <div v-if="categorizedData['adminonly']?.length">
          <div
            v-for="(typePlan, index) in categorizedData['adminonly']"
            :key="index"
            class="q-mb-md"
          >
            <CompetencyListSection
              :title="`${typePlan.position?.name || 'ตำแหน่ง'}`"
              :items="typePlan.skills?.map((skill: any) => skill.name) || []"
              v-model="isListOpenAdminOnly"
              @remove="(idx: number) => removeSkill('adminonly', index, idx)"
              @add="() => handleAddItem('adminonly')"
            />
          </div>
        </div>
        <div v-else class="text-center text-grey-6 q-pa-md">ไม่มีข้อมูลสำหรับแท็บนี้</div>
      </div>
      <div v-else-if="selectedTab === 'position'">
        <div class="row items-center justify-between q-mb-md">
          <span class="text-weight-bold">ความรู้และทักษะเฉพาะด้านของตำแหน่ง</span>
          <q-btn
            label="เพิ่ม"
            color="accent"
            icon="add"
            dense
            size="md"
            class="q-pr-sm"
            @click="() => handleAddItem('position')"
          />
        </div>
        <div v-if="groupedPositionData && Object.keys(groupedPositionData).length">
          <div
            v-for="(careerGroup, careerName) in groupedPositionData"
            :key="careerName"
            class="q-mb-lg"
          >
            <div
              class="text-subtitle1 text-weight-bold q-mb-md flex items-center"
              style="cursor: pointer"
            >
              <q-icon
                :name="
                  careerFolderStates[String(careerName)]
                    ? 'keyboard_arrow_down'
                    : 'keyboard_arrow_right'
                "
                color="dark"
                size="sm"
                class="q-mr-sm"
                @click="toggleCareerFolder(String(careerName))"
              />
              <q-icon name="folder" class="q-mr-sm" color="black" size="sm" />
              {{ careerName }}
            </div>
            <div v-if="careerFolderStates[String(careerName)]" class="career-content">
              <div
                v-for="(ageWorkGroup, ageWorkName) in careerGroup"
                :key="`${careerName}-${ageWorkName}`"
                class="q-mb-md q-ml-md"
              >
                <CompetencyListSection
                  :title="`${ageWorkName}`"
                  :items="ageWorkGroup.skills"
                  v-model="isListOpen"
                  @remove="
                    (idx: number) =>
                      removeSkillFromGrouped(String(careerName), String(ageWorkName), idx)
                  "
                  @add="() => handleAddItem('position')"
                />
              </div>
            </div>
          </div>
        </div>
        <div v-else class="text-center text-grey-6 q-pa-md">ไม่มีข้อมูลสำหรับแท็บนี้</div>
      </div>
      <div v-else>
        <div class="q-pa-md bg-grey-3 q-mb-md">เนื้อหาอื่น ๆ (mock)</div>
      </div>
    </div>

    <!-- No dialog template needed here, handled by $q.dialog in script -->

    <!-- Confirm Publication Dialog -->
    <ConfirmPublicationBtn
      v-model="showPublicationDialog"
      @confirm="handlePublicationConfirm"
      @cancel="handlePublicationCancel"
    />
  </q-page>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch, computed } from 'vue';
import CompetencyListSection from 'src/components/competency/CompetencyListSection.vue';
import { useQuasar } from 'quasar';
import StatusCapsule from 'src/components/common/StatusCapsule.vue';
import { useRoute } from 'vue-router';
import type { DevelopmentPlan, TypePlan } from 'src/types/idp';
import type { Skill } from 'src/types/models';
import type { IdpFormData, IdpTabs } from 'src/types/idp-tab-plan';
import { useGlobalStore } from 'src/stores/global';
import SearchDropDownBar from 'src/components/SearchDropDownBar.vue';
import { useDevPlansService } from 'src/services/idp/devPlansService';
import CreateIdpTabPlan from 'src/components/idp/tab-plan/CreateIdpTabPlan.vue';
import ConfirmPublicationBtn from 'src/components/idp/tab-plan/dialog/ConfirmPublicationBtn.vue';

// Initialize service
const devPlansService = useDevPlansService();

import { devPlanTabs } from 'src/data/devPlanTabs';

import TabNavigation from 'src/components/common/TabNavigation.vue';

// Mock options for SearchDropDownBar
const searchOptions = [
  'กองบริหารและพัฒนาทรัพยากรบุคคล',
  'สํานักคอมพิวเตอร์',
  'กองแผนงาน',
  'กองกิจการนิสิต',
];

const searchTypeOptions = ['ทุกสายงาน', 'สายวิชาการ', 'สายสนับสนุนวิชาการ'];

const searchPositionOptions = [
  'ทุกตําแหน่ง',
  'นักวิชาการเงินและบัญชี',
  'นักวิชาการศึกษา',
  'นักวิชาการคอมพิวเตอร์',
  'นักวิชาการพัสดุ',
];

const searchRankOptions = ['ปฏิบัติการ', 'ชํานาญการ', 'ชํานาญการพิเศษ', 'ทรงคุณวุฒิ'];

const selectedTab = ref(devPlanTabs[0]?.name ?? '');
const isListOpen = ref(true);
const isListOpenAdmin = ref(true);
const isListOpenAcademic = ref(true);
const isListOpenSupport = ref(true);
const isListOpenAdminOnly = ref(true);
const careerFolderStates = ref<Record<string, boolean>>({});
const showPublicationDialog = ref(false);
const $q = useQuasar();

// Function to open publication dialog
function openPublicationDialog() {
  showPublicationDialog.value = true;
}

// Function to handle publication confirmation
function handlePublicationConfirm() {
  // TODO: Add publication logic here
  console.log('Publishing development plan...');

  $q.notify({
    type: 'positive',
    message: 'เผยแพร่แผนพัฒนาเรียบร้อยแล้ว',
    position: 'bottom',
  });
}

// Function to handle publication cancellation
function handlePublicationCancel() {
  console.log('Publication cancelled');
}

// Function to toggle career folder state
function toggleCareerFolder(careerName: string) {
  careerFolderStates.value[careerName] = !careerFolderStates.value[careerName];
}

// Function to handle add item in different tabs
function handleAddItem(tabName: string) {
  handleAddItemWithAgeWork(tabName, null);
}

// Function to handle add item with age work data
function handleAddItemWithAgeWork(
  tabName: string,
  ageWorkData: { startYear?: number; endYear?: number; name?: string } | null,
) {
  // Map ageWork data to ageWork value
  let ageWorkValue = '';
  let disableAgeWork = false;

  if (ageWorkData) {
    // Map ageWork range to value
    if (ageWorkData.startYear && ageWorkData.endYear) {
      const startYear = ageWorkData.startYear;
      const endYear = ageWorkData.endYear;

      if (startYear === 1 && endYear === 2) {
        ageWorkValue = '1-2YEAR';
      } else if (startYear === 3 && endYear === 5) {
        ageWorkValue = '3-5YEAR';
      } else if (startYear === 6 && endYear === 8) {
        ageWorkValue = '6-8YEAR';
      } else if (startYear >= 9) {
        ageWorkValue = '9UP';
      }
    }
    disableAgeWork = true; // Disable when coming from CompetencyListSection
  }

  // Reset form data
  const formData: IdpFormData = {
    ageWork: ageWorkValue,
    skills: [],
    disableAgeWork: disableAgeWork,
  };

  let tabType: IdpTabs;
  if (tabName === 'all') {
    tabType = 'common-i';
  } else if (tabName === 'admin') {
    tabType = 'common-m';
  } else if (tabName === 'academic') {
    tabType = 'career';
  } else if (tabName === 'support') {
    tabType = 'manager';
  } else if (tabName === 'adminonly') {
    tabType = 'specialized';
  } else if (tabName === 'position') {
    tabType = 'support';
  } else {
    console.log(`Add item for tab: ${tabName}`);
    return;
  }

  // Open dialog using $q.dialog
  $q.dialog({
    component: CreateIdpTabPlan,
    componentProps: {
      tabs: tabType,
      form: formData,
    },
  })
    .onOk((result: IdpFormData) => {
      handleTabPlanSave(result);
    })
    .onCancel(() => {
      handleTabPlanCancel();
    });
}

// Function to handle tab plan save
function handleTabPlanSave(formData: IdpFormData) {
  console.log('Saving tab plan data:', formData);

  // Show success message
  $q.notify({
    type: 'positive',
    message: 'บันทึกข้อมูลเรียบร้อยแล้ว',
    position: 'bottom',
  });
}

// Function to handle tab plan cancel
function handleTabPlanCancel() {
  console.log('Tab plan cancelled');
}

// Function to remove skill from categorized data
function removeSkill(tabName: string, typePlanIndex: number, skillIndex: number) {
  $q.dialog({
    title: 'ยืนยันการลบ',
    message: 'คุณต้องการลบทักษะนี้หรือไม่?',
    cancel: true,
    persistent: true,
  }).onOk(() => {
    const typePlans = categorizedData.value[tabName];
    if (typePlans && typePlans[typePlanIndex]?.skills) {
      typePlans[typePlanIndex].skills.splice(skillIndex, 1);
    }
  });
}

// Function to remove skill from grouped position data
function removeSkillFromGrouped(careerName: string, ageWorkName: string, skillIndex: number) {
  $q.dialog({
    title: 'ยืนยันการลบ',
    message: 'คุณต้องการลบทักษะนี้หรือไม่?',
    cancel: true,
    persistent: true,
  }).onOk(() => {
    // Find the original typePlan in position data and remove the skill
    const positionData = categorizedData.value['position'];
    if (positionData) {
      for (const typePlan of positionData) {
        const planCareerName = typePlan.career?.careerName || 'ไม่ระบุสายงาน';
        const planAgeWorkName = typePlan.ageWork?.name || 'ทั่วไป';

        if (planCareerName === careerName && planAgeWorkName === ageWorkName && typePlan.skills) {
          typePlan.skills.splice(skillIndex, 1);
          break;
        }
      }
    }
  });
}

// ตัวอย่าง mock tab name ที่รองรับ overview, plan, result
// devPlanTabs ในที่นี้ควรมี name: 'overview', 'plan', 'result' อย่างน้อย 1 อัน

const route = useRoute();
const globalStore = useGlobalStore();
const loading = ref(false);
const developmentPlan = ref<DevelopmentPlan | null>(null);

// Mapping between TypePlan names and tab names
const typePlanToTabMapping = {
  ทั่วไปบุคลากร: 'all',
  ทั่วไปผู้บริหาร: 'admin',
  เฉพาะด้านวิชาการ: 'academic',
  เฉพาะสายสนับสนุน: 'support',
  เฉพาะด้านบริหาร: 'adminonly',
  ตำแหน่ง: 'position',
};

// Computed properties to categorize data by tabs
const categorizedData = computed(() => {
  if (!developmentPlan.value?.typePlans) {
    return {};
  }

  const categories: Record<string, TypePlan[]> = {};

  developmentPlan.value.typePlans.forEach((typePlan) => {
    const tabName = typePlanToTabMapping[typePlan.name];
    if (tabName) {
      if (!categories[tabName]) {
        categories[tabName] = [];
      }
      categories[tabName].push(typePlan);
    }
  });

  // เรียงลำดับข้อมูลในแต่ละ category ตาม ageWork.startYear
  Object.keys(categories).forEach((tabName) => {
    if (categories[tabName]) {
      categories[tabName].sort((a, b) => {
        const startYearA = a.ageWork?.startYear || 0;
        const startYearB = b.ageWork?.startYear || 0;
        return startYearA - startYearB;
      });
    }
  });

  return categories;
});

// Computed property for grouped position data by career and ageWork
const groupedPositionData = computed(() => {
  const positionData = categorizedData.value['position'];
  if (!positionData?.length) {
    return {};
  }

  const grouped: Record<string, Record<string, { skills: string[] }>> = {};

  positionData.forEach((typePlan) => {
    // ลองใช้ข้อมูลจากหลายแหล่งเพื่อหา career name
    const careerName =
      ((typePlan.career as unknown as Record<string, unknown>)?.career_name as string) ||
      typePlan.career?.careerName ||
      typePlan.position?.name ||
      'ไม่ระบุสายงาน';

    const ageWorkName = typePlan.ageWork?.name || 'ทั่วไป';
    const skills = typePlan.skills?.map((skill: Skill) => skill.name) || [];

    if (!grouped[careerName]) {
      grouped[careerName] = {};
    }

    if (!grouped[careerName][ageWorkName]) {
      grouped[careerName][ageWorkName] = { skills: [] };
    }

    grouped[careerName][ageWorkName].skills.push(...skills);
  });

  return grouped;
});

// Function to extract year from plan name
const extractYearFromPlanName = (planName: string): string => {
  const yearMatch = planName.match(/ปี\s*(\d{4})/);
  return yearMatch?.[1] ?? 'XXXX';
};

// Function to update global store with dynamic breadcrumb
const updateDevelopmentPlanBreadcrumb = (plan: DevelopmentPlan | null) => {
  if (plan) {
    const year = extractYearFromPlanName(plan.name);
    globalStore.setDevelopmentPlanYear(year);
  }
};

// Function to fetch development plan by ID
const fetchDevelopmentPlan = async (id: number) => {
  loading.value = true;
  try {
    const plan = await devPlansService.fetchOne(id);
    developmentPlan.value = plan;
    updateDevelopmentPlanBreadcrumb(plan);
  } catch (error) {
    console.error('Error fetching development plan:', error);
    developmentPlan.value = null;
    updateDevelopmentPlanBreadcrumb(null);
  } finally {
    loading.value = false;
  }
};

onMounted(() => {
  const planId = Number(route.params.id);
  if (planId && !isNaN(planId)) {
    void fetchDevelopmentPlan(planId);
  }
});

// Watch for route parameter changes
watch(
  () => route.params.id,
  (newId) => {
    const planId = Number(newId);
    if (planId && !isNaN(planId)) {
      void fetchDevelopmentPlan(planId);
    }
  },
);

// Clear breadcrumb when component unmounts
onUnmounted(() => {
  globalStore.clearDevelopmentPlanYear();
});
</script>

<style scoped></style>
