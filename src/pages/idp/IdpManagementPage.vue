<template>
  <q-page padding class="q-gutter-y-lg">
    <IndividualDevelopmentHeader
      title="จัดการแผนพัฒนารายบุคคล"
      :tabs="tabOptions"
      :default-tab="activeTab"
      @search="onSearchUpdate"
      @tab-change="onTabChange"
      @filter-change="onFilterChange"
      @confirm-filter="onConfirmFilter"
    />

    <q-table
      :rows="rows"
      :columns="individualDevelopmentManagementColumns"
      row-key="id"
      flat
      bordered
      wrap-cells
      separator="cell"
      :loading="loading"
      :pagination="pagination"
      @request="onRequest"
    >
      <template v-slot:body-cell-id="{ rowIndex }">
        <q-td class="text-center">
          {{ rowIndex + 1 }}
        </q-td>
      </template>

      <template v-slot:body-cell-status="{ row }">
        <q-td class="text-center">
          <StatusCapsule :published="row.status === 'active'" />
        </q-td>
      </template>

      <template v-slot:body-cell-actions="{ row }">
        <q-td class="text-center">
          <q-btn
            dense
            unelevated
            class="view-icon"
            icon="edit"
            @click="onClickEdit(row)"
            style="background-color: #303f9f"
          />
        </q-td>
      </template>
    </q-table>

    <!-- Form Dialog -->
    <IndividualDevelopmentFormDialog
      v-model="showFormDialog"
      :edit-item="selectedItem"
      @saved="handleSaved"
    />
  </q-page>
</template>

<script setup lang="ts">
import { useQuasar, type QTableProps } from 'quasar';
import { individualDevelopmentManagementColumns } from 'src/data/table_columns';
import { ref, onMounted } from 'vue';
import StatusCapsule from 'src/components/common/StatusCapsule.vue';
import IndividualDevelopmentHeader from 'src/components/individual-development/IndividualDevelopmentHeader.vue';
import IndividualDevelopmentFormDialog from 'src/components/individual-development/IndividualDevelopmentFormDialog.vue';
import { useIndividualDevelopmentPlan } from 'src/composables/useIndividualDevelopmentPlan';
import type { IndividualDevelopmentPlan } from 'src/types/models';

// Types
interface FilterOptions {
  search: string;
  department: string | null;
  status: string | null;
  tab: string;
}

// Composables
const { rows: plansData, loading, fetchPlans } = useIndividualDevelopmentPlan();
const $q = useQuasar();

// Dialog states
const showFormDialog = ref(false);

// Selected items
const selectedItem = ref<IndividualDevelopmentPlan | null>(null);

// Tab and filter state
const activeTab = ref('general');
const currentFilters = ref<FilterOptions>({
  search: '',
  department: null,
  status: null,
  tab: 'general',
});

// Tab options
const tabOptions = [
  { label: 'บุคลากรทั่วไป', value: 'general' },
  { label: 'ผู้บริหาร', value: 'management' },
];

// Reactive data
const rows = ref<IndividualDevelopmentPlan[]>([]);
const searchKeyword = ref('');

// Pagination configuration with proper typing
const pagination = ref<{
  sortBy: string;
  descending: boolean;
  page: number;
  rowsPerPage: number;
  rowsNumber: number;
}>({
  sortBy: 'id',
  descending: false,
  page: 1,
  rowsPerPage: 10,
  rowsNumber: 0,
});

// Methods
const fetchIndividualDevelopmentPlans = async (requestProp?: {
  pagination: QTableProps['pagination'];
}) => {
  loading.value = true;
  try {
    await fetchPlans();

    const paginationToUse = requestProp?.pagination || pagination.value;

    // Filter by search keyword and other filters
    let filteredData = plansData.value;
    
    // Apply tab filter (simulate different data for different tabs)
    if (currentFilters.value.tab === 'management') {
      // Filter for management personnel (you can adjust this logic)
      filteredData = filteredData.filter(item => 
        item.position.includes('ผู้จัดการ') || 
        item.position.includes('หัวหน้า') ||
        item.position.includes('ผู้อำนวยการ')
      );
    }
    
    // Apply search filter
    if (searchKeyword.value) {
      const keyword = searchKeyword.value.toLowerCase();
      filteredData = filteredData.filter(
        (item: IndividualDevelopmentPlan) =>
          item.employee_name.toLowerCase().includes(keyword) ||
          item.position.toLowerCase().includes(keyword) ||
          item.development_goals.toLowerCase().includes(keyword) ||
          item.department.toLowerCase().includes(keyword),
      );
    }
    
    // Apply department filter
    if (currentFilters.value.department) {
      filteredData = filteredData.filter(item => 
        item.department === currentFilters.value.department
      );
    }
    
    // Apply status filter
    if (currentFilters.value.status) {
      filteredData = filteredData.filter(item => 
        item.status === currentFilters.value.status
      );
    }

    // Sort data with null safety
    if (paginationToUse?.sortBy) {
      filteredData.sort((a: IndividualDevelopmentPlan, b: IndividualDevelopmentPlan) => {
        const sortBy = paginationToUse.sortBy as keyof IndividualDevelopmentPlan;
        const aVal = a[sortBy];
        const bVal = b[sortBy];

        // Handle null/undefined values
        if (aVal == null && bVal == null) return 0;
        if (aVal == null) return paginationToUse.descending ? 1 : -1;
        if (bVal == null) return paginationToUse.descending ? -1 : 1;

        if (aVal < bVal) return paginationToUse.descending ? 1 : -1;
        if (aVal > bVal) return paginationToUse.descending ? -1 : 1;
        return 0;
      });
    }

    // Pagination
    const start = ((paginationToUse?.page || 1) - 1) * (paginationToUse?.rowsPerPage || 10);
    const end = start + (paginationToUse?.rowsPerPage || 10);

    rows.value = filteredData.slice(start, end);
    pagination.value.rowsNumber = filteredData.length;

    // Update pagination with type safety
    if (requestProp?.pagination) {
      const { sortBy, descending, page, rowsPerPage } = requestProp.pagination;
      pagination.value = {
        sortBy: sortBy || 'id',
        descending: descending ?? false,
        page: page || 1,
        rowsPerPage: rowsPerPage || 10,
        rowsNumber: filteredData.length,
      };
    }
  } catch (error) {
    console.error('Error fetching individual development plans:', error);
    $q.notify({
      type: 'negative',
      message: 'เกิดข้อผิดพลาดในการโหลดข้อมูล',
    });
  } finally {
    loading.value = false;
  }
};

const onRequest = (props: { pagination: QTableProps['pagination'] }) => {
  fetchIndividualDevelopmentPlans(props);
};

const onSearchUpdate = (searchValue: string) => {
  searchKeyword.value = searchValue;
  pagination.value.page = 1; // Reset to first page on search
  fetchIndividualDevelopmentPlans();
};

const onConfirmFilter = (filters: FilterOptions) => {
  // Apply the confirmed filters
  currentFilters.value = { ...filters };
  searchKeyword.value = filters.search || '';
  pagination.value.page = 1; // Reset to first page
  fetchIndividualDevelopmentPlans();
};

const onClickEdit = (item: IndividualDevelopmentPlan) => {
  selectedItem.value = item;
  showFormDialog.value = true;
};

const onTabChange = (tab: string) => {
  activeTab.value = tab;
  currentFilters.value.tab = tab;
  applyFilters();
};

const onFilterChange = (filters: FilterOptions) => {
  currentFilters.value = filters;
  searchKeyword.value = filters.search;
  applyFilters();
};

const applyFilters = () => {
  pagination.value.page = 1; // Reset to first page
  fetchIndividualDevelopmentPlans();
};

const handleSaved = () => {
  showFormDialog.value = false;
  selectedItem.value = null;
  fetchIndividualDevelopmentPlans();
};

// Lifecycle
onMounted(() => {
  fetchIndividualDevelopmentPlans();
});
</script>

<style scoped>
/* Using global app styles for action buttons - no custom overrides needed */
</style>
