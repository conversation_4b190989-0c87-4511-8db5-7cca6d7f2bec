<template>
  <q-page class="q-pa-md">
    <q-table
      v-model:pagination="pagination"
      v-model:sortBy="sortBy"
      v-model:descending="descending"
      :rows="tableRows"
      :columns="userColumns"
      row-key="uniqueKey"
      @request="onRequest"
      separator="cell"
      ref="tableRef"
    >
      <!-- Rowspan implementation for grouped rows -->
      <template #body-cell-id="props">
        <q-td
          class="text-center border-r"
          :rowspan="isFirstRowForUser(props.row) ? getUserRowCount(props.row.id) : undefined"
          :style="isFirstRowForUser(props.row) ? '' : 'display: none;'"
        >
          {{ props.row.id }}
        </q-td>
      </template>

      <template #body-cell-name="props">
        <q-td
          class="text-left border-r"
          :rowspan="isFirstRowForUser(props.row) ? getUserRowCount(props.row.id) : undefined"
          :style="isFirstRowForUser(props.row) ? '' : 'display: none;'"
        >
          {{ props.row.firstName }} {{ props.row.lastName }}
        </q-td>
      </template>

      <template #body-cell-faculty="props">
        <q-td class="text-left border-r">
          {{ props.row.facultyName }}
        </q-td>
      </template>

      <template #body-cell-role="props">
        <q-td class="text-left border-r">
          {{ props.row.role }}
        </q-td>
      </template>

      <template #body-cell-actions="props">
        <q-td
          class="text-center"
          :rowspan="isFirstRowForUser(props.row) ? getUserRowCount(props.row.id) : undefined"
          :style="isFirstRowForUser(props.row) ? '' : 'display: none;'"
        >
          <div class="q-gutter-x-sm flex justify-center">
            <q-btn
              dense
              unelevated
              class="manage-role-icon"
              icon="supervisor_account"
              @click="onAssignRoleToUserForm(props.row as User)"
            />
            <q-btn
              dense
              unelevated
              class="view-icon"
              icon="business_center"
              @click="onAssignDepartmentsToUserForm(props.row as User)"
            />
          </div>
        </q-td>
      </template>
    </q-table>
  </q-page>
</template>

<script setup lang="ts">
import { type QTableProps, useQuasar } from 'quasar';
import { ascendingPaginationValue } from 'src/configs/pagination';
import { userColumns as baseUserColumns } from 'src/data/table_columns';
import { useUserService } from 'src/services/ums/userService';
import { useRoleService } from 'src/services/ums/roleService';
import { useAuthStore } from 'src/stores/auth';
import type { User, Role } from 'src/types/models';
import { defineAsyncComponent, onMounted, ref, watch } from 'vue';
import { computed } from 'vue';

// Type for table row
type TableRow = User & { facultyName: string; role: string; uniqueKey: string };

// Helper: Find the index of the first row for each user in tableRows
const isFirstRowForUser = (row: TableRow) => {
  // Use the uniqueKey to determine if this is the first row for the user
  const userRows = tableRows.value.filter((r) => r.id === row.id);
  if (userRows.length === 0) return true;

  // Return true if this row's uniqueKey matches the first row's uniqueKey for this user
  return row.uniqueKey === userRows[0]?.uniqueKey;
};

// Helper: Get the number of rows for a specific user
const getUserRowCount = (userId: string) => {
  return tableRows.value.filter((row) => row.id === userId).length;
};

const userColumns = computed(() => {
  return baseUserColumns.map((col) => {
    if (col.name === 'faculty') {
      return { ...col, field: 'facultyName', sortable: false }; // Disable sorting for faculty
    }
    if (col.name === 'role') {
      return { ...col, field: 'role', sortable: false }; // Disable sorting for role
    }
    if (col.name === 'id') {
      return {
        ...col,
        sortable: true,
        sort: (a: TableRow, b: TableRow) => Number(a.id) - Number(b.id),
      };
    }
    if (col.name === 'name') {
      return {
        ...col,
        sortable: true,
        sort: (a: TableRow, b: TableRow) => {
          const nameA = `${a.firstName} ${a.lastName}`.trim();
          const nameB = `${b.firstName} ${b.lastName}`.trim();
          return nameA.localeCompare(nameB, 'th');
        },
      };
    }
    return col;
  });
});

// Flatten users by faculty for table display
const tableRows = computed(() => {
  type RoleInFaculty = { id: number; name: string; description?: string };
  type UserFaculty = {
    id: number;
    faculty: { id: number; nameTh: string; nameEn?: string };
    roles: RoleInFaculty[];
  };
  const allRows: Array<User & { facultyName: string; role: string; uniqueKey: string }> = [];
  rows.value.forEach((user) => {
    const faculties = (user as User & { faculties?: UserFaculty[] }).faculties;
    if (Array.isArray(faculties) && faculties.length > 0) {
      faculties.forEach((facultyObj, index) => {
        let facultyName = '-';
        let role = '-';
        if (
          facultyObj &&
          typeof facultyObj === 'object' &&
          'faculty' in facultyObj &&
          facultyObj.faculty &&
          typeof facultyObj.faculty === 'object' &&
          'nameTh' in facultyObj.faculty
        ) {
          facultyName = (facultyObj.faculty as { nameTh?: string }).nameTh ?? '-';
        }
        if (
          'roles' in facultyObj &&
          Array.isArray(facultyObj.roles) &&
          facultyObj.roles.length > 0
        ) {
          role = facultyObj.roles.map((r: RoleInFaculty) => r.name).join(', ');
        }
        allRows.push({
          ...user,
          facultyName,
          role,
          uniqueKey: `${user.id}-${index}`, // Add unique key
        });
      });
    } else {
      allRows.push({
        ...user,
        facultyName: '-',
        role: '-',
        uniqueKey: `${user.id}-0`, // Add unique key
      });
    }
  });

  // Sort based on current sort settings
  const sortedRows = [...allRows];

  if (sortBy.value === 'id') {
    // Group by user first, then sort users
    const groupedByUser = new Map<string, TableRow[]>();
    sortedRows.forEach((row) => {
      if (!groupedByUser.has(row.id)) {
        groupedByUser.set(row.id, []);
      }
      groupedByUser.get(row.id)!.push(row);
    });

    // Sort users by ID
    const sortedUsers = Array.from(groupedByUser.entries()).sort(([idA], [idB]) => {
      const comparison = Number(idA) - Number(idB);
      return descending.value ? -comparison : comparison;
    });

    return sortedUsers.flatMap(([, userRows]) => userRows);
  } else if (sortBy.value === 'name') {
    // Group by user first, then sort users by name
    const groupedByUser = new Map<string, TableRow[]>();
    sortedRows.forEach((row) => {
      if (!groupedByUser.has(row.id)) {
        groupedByUser.set(row.id, []);
      }
      groupedByUser.get(row.id)!.push(row);
    });

    // Sort users by name
    const sortedUsers = Array.from(groupedByUser.entries()).sort(([, rowsA], [, rowsB]) => {
      const firstRowA = rowsA[0];
      const firstRowB = rowsB[0];
      if (!firstRowA || !firstRowB) return 0;

      const nameA = `${firstRowA.firstName} ${firstRowA.lastName}`.trim();
      const nameB = `${firstRowB.firstName} ${firstRowB.lastName}`.trim();
      const comparison = nameA.localeCompare(nameB, 'th');
      return descending.value ? -comparison : comparison;
    });

    return sortedUsers.flatMap(([, userRows]) => userRows);
  } else {
    // Default sort by user ID to ensure rows for the same user are grouped together
    sortedRows.sort((a, b) => Number(a.id) - Number(b.id));
    return sortedRows;
  }
});

const pagination = ref({ ...ascendingPaginationValue });
const rows = ref<User[]>([]);
const searchText = ref<string>('');
const tableRef = ref();
const sortBy = ref<string>('id');
const descending = ref<boolean>(false);
const roles = ref<Role[]>([]);
const selectedRoleId = ref<number | null>(null);
const authStore = useAuthStore();

const fetchUsers = () => {
  // Get current faculty ID from auth store
  const facultyFilter = authStore.currentFaculty?.id;

  // Use the selected role ID directly
  const roleFilter = selectedRoleId.value;

  console.log('Fetching users with filters:', {
    facultyFilter,
    roleFilter,
    selectedRoleId: selectedRoleId.value,
    currentFaculty: authStore.currentFaculty,
  });

  useUserService()
    .getUsers(
      pagination.value,
      searchText.value,
      facultyFilter ? Number(facultyFilter) : undefined,
      roleFilter ? Number(roleFilter) : undefined,
    )
    .then((res) => {
      rows.value = res.data || [];
      pagination.value.rowsNumber = res.total;

      // Re-setup hover effects after data is loaded and DOM is updated
      setTimeout(() => {
        setupHoverEffects();
      }, 200);
    })
    .catch((error) => {
      console.error('Error fetching users:', error);
    });
};

const fetchRoles = async () => {
  try {
    const response = await useRoleService().getRoles({ page: 1, rowsPerPage: 100 });
    roles.value = response.data || [];
  } catch (error) {
    console.error('Error fetching roles:', error);
  }
};

const onRequest: QTableProps['onRequest'] = ({ pagination: _pag }) => {
  // Update sorting if provided
  if (_pag.sortBy) {
    sortBy.value = _pag.sortBy;
  }
  if (_pag.descending !== undefined) {
    descending.value = _pag.descending;
  }

  pagination.value = { ...pagination.value, ..._pag };
  fetchUsers();
};
const onSearch = (keyword: string) => {
  searchText.value = keyword;
  pagination.value.page = 1;
  fetchUsers();
};

const onRoleFilterChange = (roleId: number | null) => {
  console.log('Role filter changed:', roleId);
  selectedRoleId.value = roleId;
  // Reset pagination to first page when applying filter
  pagination.value.page = 1;
  // Fetch users with new role filter
  fetchUsers();
};

onMounted(() => {
  fetchUsers();
  fetchRoles();

  // Add hover event listeners after table is rendered and data is loaded
  setTimeout(() => {
    setupHoverEffects();
  }, 300);
});

// Watch for auth store changes that affect user filtering
watch(
  () => [authStore.currentFacultyId, authStore.currentRoleName],
  ([newFacultyId, newRoleName], [oldFacultyId, oldRoleName]) => {
    // Only refetch if values actually changed and it's not the initial load
    const facultyChanged = newFacultyId !== oldFacultyId && oldFacultyId !== undefined;
    const roleChanged = newRoleName !== oldRoleName && oldRoleName !== undefined;

    if (facultyChanged || roleChanged) {
      console.log('Auth state changed:', {
        facultyChanged: facultyChanged ? { old: oldFacultyId, new: newFacultyId } : false,
        roleChanged: roleChanged ? { old: oldRoleName, new: newRoleName } : false,
      });
      // Reset pagination to first page when auth state changes
      pagination.value.page = 1;
      fetchUsers();
    }
  },
);

// Setup hover effects for grouped rows (using light color)
const setupHoverEffects = () => {
  if (!tableRef.value) return;

  const tableElement = tableRef.value.$el;
  const rows = tableElement.querySelectorAll('tbody tr');

  rows.forEach((row: Element, index: number) => {
    // Remove existing listeners first
    row.removeEventListener('mouseenter', () => {});
    row.removeEventListener('mouseleave', () => {});

    row.addEventListener('mouseenter', () => {
      highlightUserGroup(index);
    });

    row.addEventListener('mouseleave', () => {
      clearHighlight();
    });
  });
};

// Highlight all rows belonging to the same user with LIGHT color
const highlightUserGroup = (hoveredIndex: number) => {
  if (!tableRef.value || hoveredIndex >= tableRows.value.length) return;

  const tableElement = tableRef.value.$el;
  const rows = tableElement.querySelectorAll('tbody tr');
  const hoveredRow = tableRows.value[hoveredIndex];

  if (!hoveredRow) return;

  const userId = hoveredRow.id;

  // Clear any existing highlights first
  clearHighlight();

  // Find all rows with the same user ID and set light background
  tableRows.value.forEach((row, index) => {
    if (row.id === userId && index < rows.length && rows[index]) {
      // Set light color for all rows of the same user
      (rows[index] as HTMLElement).style.setProperty(
        'background-color',
        'rgba(255, 202, 40, 0.05)',
        'important',
      );
    }
  });
};

// Clear all hover highlights
const clearHighlight = () => {
  if (!tableRef.value) return;

  const tableElement = tableRef.value.$el;
  const rows = tableElement.querySelectorAll('tbody tr');

  rows.forEach((row: Element) => {
    (row as HTMLElement).style.removeProperty('background-color');
  });
};

const $q = useQuasar();
const onAssignRoleToUserForm = (user: User) => {
  $q.dialog({
    component: defineAsyncComponent(() => import('../dialog/AssignRoleToUserForm.vue')),
    persistent: true,
    componentProps: {
      user,
    },
  }).onOk(() => {
    // Dialog ได้ update ข้อมูลใน database แล้ว
    // เราแค่ refresh table และแสดง notification
    fetchUsers();
    $q.notify({
      type: 'positive',
      message: 'กำหนดบทบาทผู้ใช้งานเรียบร้อยแล้ว',
    });
  });
};

const onAssignDepartmentsToUserForm = (user: User) => {
  $q.dialog({
    component: defineAsyncComponent(() => import('../dialog/AssignDepartmentsToUserForm.vue')),
    persistent: true,
    componentProps: {
      user,
    },
  }).onOk(() => {
    fetchUsers();
    $q.notify({
      type: 'positive',
      message: 'กำหนดส่วนงานให้ผู้ใช้งานเรียบร้อยแล้ว',
    });
  });
};
// Expose the onSearch function to parent component
defineExpose({
  onSearch,
  onRoleFilterChange,
});
</script>
