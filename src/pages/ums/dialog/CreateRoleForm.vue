<template>
  <q-dialog ref="dialogRef" persistent>
    <q-card class="card-form" bordered flat style="max-width: 800px; width: 100%">
      <q-form ref="formRef" @submit.prevent="onClickSave()">
        <q-card-section class="text-h6"> {{ computedTitle }}</q-card-section>
        <q-card-section class="q-gutter-y-lg">
          <q-input color="full-width" v-model="formData.name" label="ชื่อบทบาท" outlined dense />
          <q-input
            color="full-width"
            v-model="formData.description"
            label="รายละเอียด"
            outlined
            dense
            type="textarea"
            :input-style="{ height: '100px', resize: 'none' }"
          />
        </q-card-section>
        <q-card-actions align="right" class="q-pa-md q-mt-lg">
          <div class="q-gutter-sm">
            <q-btn
              icon="close"
              label="ยกเลิก"
              flat
              color="grey-7"
              @click="onClickCancel"
              style="border: 1px solid rgba(0, 0, 0, 0.3)"
            />
            <q-btn color="positive" icon="check" label="ยืนยัน" @click="onClickSave" />
          </div>
        </q-card-actions>
      </q-form>
    </q-card>
  </q-dialog>
</template>

<script setup lang="ts">
import { QForm, useDialogPluginComponent } from 'quasar';
import { useRoleService } from 'src/services/ums/roleService';
import type { Role } from 'src/types/models';
import { computed, ref } from 'vue';

// Type for creating a new role (without id and userId)
type CreateRoleDto = Pick<Role, 'name' | 'description'>;

const props = defineProps<{
  role?: Role;
}>();

const { dialogRef, onDialogOK, onDialogCancel } = useDialogPluginComponent();

const formRef = ref<typeof QForm>();

const computedTitle = computed(() => {
  return props.role ? `แก้ไข Role - ${props.role.name}` : 'เพิ่ม Role';
});

const formData = ref({
  ...props.role,
});

const onClickSave = async () => {
  if (!formRef.value) {
    console.error('Form reference is not set');
    return;
  }
  const isValid = await formRef.value.validate();
  if (!isValid) return;

  try {
    const roleDto: CreateRoleDto = {
      name: formData.value.name || '',
      description: formData.value.description || '',
    };

    let result;
    if (props.role) {
      // Update existing role
      result = await useRoleService().updateRole(props.role.id, roleDto);
    } else {
      // Create new role
      result = await useRoleService().createRole(roleDto);
    }

    // Close dialog and return the result
    onDialogOK({
      createdRole: result,
    });
  } catch (error) {
    // Error handling is already done in the service
    console.error('Failed to save role:', error);
  }
};

const onClickCancel = () => {
  onDialogCancel();
};
</script>

<style scoped>
.q-btn {
  min-width: 100px;
}
</style>
