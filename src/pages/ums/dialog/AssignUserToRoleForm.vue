<template>
  <q-dialog ref="dialogRef" persistent>
    <q-card class="card-form" bordered flat style="max-width: 800px; width: 100%">
      <q-form ref="formRef" @submit.prevent="onClickSave()">
        <q-card-section class="text-h6"> {{ computedTitle }}</q-card-section>
        <q-card-section class="q-gutter-y-lg">
          <div class="row justify-between items-center">
            <q-select
              v-model="selectedFaculty"
              outlined
              dense
              use-input
              input-debounce="300"
              style="width: 250px"
              label="เลือกส่วนงาน"
              :options="facultyOptions"
              :loading="facultyLoading"
              @filter="onFacultyFilter"
              clearable
              map-options
              option-label="label"
              option-value="value"
              ref="facultySelectRef"
              :menu-max-height="200"
              virtual-scroll
              virtual-scroll-item-size="40"
              virtual-scroll-sticky-size-start="0"
              virtual-scroll-sticky-size-end="0"
              @virtual-scroll="onVirtualScroll"
            >
              <template v-slot:prepend>
                <q-icon name="search" />
              </template>
              <template v-slot:option="scope">
                <q-item v-bind="scope.itemProps">
                  <q-item-section>
                    <q-item-label>{{ scope.opt.label }}</q-item-label>
                  </q-item-section>
                </q-item>
              </template>
              <template v-slot:loading>
                <q-spinner-dots color="primary" size="24px" />
              </template>
              <template v-slot:no-option>
                <q-item>
                  <q-item-section class="text-grey"> ไม่พบคณะที่ต้องการ </q-item-section>
                </q-item>
              </template>
            </q-select>
            <div class="row q-gutter-sm items-center">
              <q-select
                v-model="selectedUser"
                outlined
                dense
                use-input
                input-debounce="0"
                clearable
                style="width: 250px"
                label="เลือกบุคลากร"
                :options="usersOptions"
                @filter="filterUser"
                :disable="!selectedFaculty"
                option-label="firstName"
                option-value="id"
              >
                <template v-slot:prepend>
                  <q-icon name="search" />
                </template>
              </q-select>
            </div>
          </div>
        </q-card-section>
        <q-card-section class="q-gutter-y-lg">
          <q-table :rows="rows" :columns="AssignUserToRoleColumns" row-key="id" separator="cell">
            <template #body-cell-actions="props">
              <q-td class="text-center">
                <div class="q-gutter-x-sm flex justify-center">
                  <q-checkbox
                    :model-value="props.row.selected"
                    @update:model-value="toggleUser(props.row)"
                  />
                </div>
              </q-td>
            </template>
            <template #no-data>
              <div
                class="text-center q-pa-lg text-grey-6 full-width"
                style="
                  display: flex;
                  flex-direction: column;
                  align-items: center;
                  justify-content: center;
                  min-height: 200px;
                "
              >
                <q-icon name="info" size="2em" class="q-mb-md" />
                <div class="text-body1">กรุณาเลือกส่วนงาน</div>
              </div>
            </template>
          </q-table>
        </q-card-section>
        <q-card-actions align="right" class="q-pa-md q-mt-lg">
          <div class="q-gutter-sm">
            <q-btn
              icon="close"
              label="ยกเลิก"
              flat
              color="grey-7"
              @click="onClickCancel"
              style="border: 1px solid rgba(0, 0, 0, 0.3)"
            />
            <q-btn color="positive" icon="check" label="ยืนยัน" @click="onClickSave" />
          </div>
        </q-card-actions>
      </q-form>
    </q-card>
  </q-dialog>
</template>

<script setup lang="ts">
import { QForm, useDialogPluginComponent } from 'quasar';
import type { Faculty, Role } from 'src/types/models';
import { computed, onMounted, ref, watch } from 'vue';
import { AssignUserToRoleColumns } from 'src/data/table_columns';
import { getFacultiesList, useFacultyService } from 'src/services/ums/facultiesService';
import type { FacultyUser } from 'src/types/data';
import { useRoleService } from 'src/services/ums/roleService';

// Original options data
const allUsers = ref<FacultyUser[]>([]);
const usersOptions = ref<FacultyUser[]>([]);

// Track selected users separately
const selectedUsers = ref<Set<number>>(new Set());

const rows = computed(() => {
  console.log('rows computed called, allUsers.value:', allUsers.value);
  if (!allUsers.value) {
    console.log('No users data, returning empty array');
    return [];
  }

  const mappedRows = allUsers.value.map((user) => ({
    id: user.id,
    name: user.firstName + ' ' + user.lastName,
    selected: selectedUsers.value.has(user.id), // Check if user is in selected set
  }));
  console.log('Mapped rows:', mappedRows);
  return mappedRows;
});

const props = defineProps<{
  role?: Role;
}>();

const { dialogRef, onDialogOK, onDialogCancel } = useDialogPluginComponent();

const formRef = ref<typeof QForm>();
const facultySelectRef = ref();
// const facultyUser = ref<UserByFaculty>();

const selectedFaculty = ref<Faculty | null>(null);
const selectedFacultyId = computed(() => selectedFaculty.value?.id);
const selectedUser = ref<string | null>(null);

// Faculty infinite scroll state
const faculties = ref<Faculty[]>([]);
const facultyLoading = ref(false);
const facultyHasMore = ref(true);
const facultySearch = ref('');
const currentPage = ref(1);
const pageSize = ref(20);

// Faculty options for q-select
const facultyOptions = computed(() =>
  faculties.value.map((faculty) => ({
    label: faculty.nameTh || faculty.nameEn,
    value: faculty,
    id: faculty.id,
  })),
);

// Filtered options for display

// Load faculties with pagination
const loadFaculties = async (reset = false) => {
  console.log('loadFaculties called:', {
    reset,
    currentPage: currentPage.value,
    facultyLoading: facultyLoading.value,
    facultyHasMore: facultyHasMore.value,
  });

  if (facultyLoading.value || (!facultyHasMore.value && !reset)) {
    console.log('loadFaculties early return:', {
      facultyLoading: facultyLoading.value,
      facultyHasMore: facultyHasMore.value,
      reset,
    });
    return;
  }

  try {
    facultyLoading.value = true;

    if (reset) {
      faculties.value = [];
      currentPage.value = 1;
      facultyHasMore.value = true;
    }

    const pagination = {
      page: currentPage.value,
      rowsPerPage: pageSize.value,
      rowsNumber: 0,
      sortBy: 'nameTh',
      descending: false,
    };

    console.log('Fetching faculties with pagination:', pagination);
    const response = await getFacultiesList(pagination);
    console.log('API response:', {
      dataLength: response.data.length,
      total: response.total,
      currentPage: currentPage.value,
    });

    if (reset) {
      faculties.value = response.data;
    } else {
      faculties.value.push(...response.data);
    }

    // Check if there are more pages
    facultyHasMore.value = response.data.length === pageSize.value;

    if (facultyHasMore.value) {
      currentPage.value++;
    }

    console.log('Faculties loaded:', {
      totalFaculties: faculties.value.length,
      hasMore: facultyHasMore.value,
      nextPage: currentPage.value,
      responseDataLength: response.data.length,
    });
  } catch (error) {
    console.error('Error loading faculties:', error);
  } finally {
    facultyLoading.value = false;
  }
};

// Handle faculty search
const onFacultySearch = async (value: string) => {
  facultySearch.value = value;
  // For now, just reset and reload - you can implement search API later
  await loadFaculties(true);
};

// Handle faculty filter for q-select
const onFacultyFilter = (val: string, update: (cb: () => void) => void) => {
  update(async () => {
    await onFacultySearch(val);
  });
};

// Handle virtual scroll for infinite loading
const onVirtualScroll = async (details: { index: number; from: number; to: number }) => {
  console.log('Virtual scroll triggered:', details);

  // Check if we're near the end of the loaded data
  const threshold = 5; // Load more when user is 5 items away from the end
  if (
    details.index >= faculties.value.length - threshold &&
    !facultyLoading.value &&
    facultyHasMore.value
  ) {
    console.log('Loading more faculties via virtual scroll...');
    await loadFaculties(false);
  }
};

const filterUser = (val: string, update: (fn: () => void) => void) => {
  update(() => {
    if (val === '') {
      usersOptions.value = allUsers.value || [];
    } else {
      const needle = val.toLowerCase();
      usersOptions.value =
        allUsers.value?.filter(
          (option) =>
            (option.firstName + ' ' + option.lastName).toLowerCase().includes(needle) ||
            (option.email && option.email.toLowerCase().includes(needle)),
        ) || [];
    }
  });
};

const fetchUserByFaculty = async (facultyId: number) => {
  console.log('fetchUserByFaculty called with facultyId:', facultyId);
  try {
    const response = await useFacultyService().getUserByFaculty(
      { page: 1, rowsPerPage: 20 },
      '',
      facultyId,
    );
    console.log('API response:', response);
    allUsers.value = response.data;
    console.log('allUsers.value updated:', allUsers.value);
  } catch (error) {
    console.error('Error in fetchUserByFaculty:', error);
    throw error;
  }
};

const fetchUsersWithRole = async (facultyId: string | number, roleId: number) => {
  console.log('fetchUsersWithRole called with facultyId:', facultyId, 'roleId:', roleId);
  try {
    // Convert facultyId to number if it's a string
    const numericFacultyId = typeof facultyId === 'string' ? parseInt(facultyId, 10) : facultyId;

    // Get all users in the faculty (this already includes their roles)
    const facultyUsersResponse = await useFacultyService().getUserByFaculty(
      { page: 1, rowsPerPage: 1000 }, // Get all users
      '',
      numericFacultyId,
    );

    console.log('Faculty users response:', facultyUsersResponse);

    // Filter users who have the specific role in this faculty
    const usersWithRole = facultyUsersResponse.data.filter(
      (user: FacultyUser) =>
        user.roles && user.roles.some((role: { id: number }) => role.id === roleId),
    );

    console.log('Users with role in faculty:', usersWithRole);

    // Pre-check users who have the role in this faculty
    const usersWithRoleIds = usersWithRole.map((user: FacultyUser) => user.id);
    selectedUsers.value = new Set(usersWithRoleIds);
    console.log('Pre-checked users with role in faculty:', Array.from(selectedUsers.value));
  } catch (error) {
    console.error('Error in fetchUsersWithRole:', error);
    // If there's an error, just clear the selection
    selectedUsers.value.clear();
  }
};

const computedTitle = computed(() => {
  return props.role ? `จัดการบุคลากร - ${props.role.name}` : 'จัดการบุคลากร';
});

const formData = ref({
  ...props.role,
});

const onClickSave = async () => {
  if (!formRef.value) {
    console.error('Form reference is not set');
    return;
  }
  const isValid = await formRef.value.validate();
  if (!isValid) return;
  if (!props.role?.id || !selectedFaculty.value?.id) {
    console.error('Missing required data');
    return;
  }

  const selectedUserIds = Array.from(selectedUsers.value);
  if (selectedUserIds.length === 0) {
    console.error('No users selected');
    return;
  }
  console.log('selectedUserIds', selectedUserIds);
  console.log('selectedFacultyId', selectedFacultyId.value);
  console.log('props.role?.id', props.role?.id);

  try {
    await useRoleService().updateFacultyUsersRole(
      props.role?.id as number,
      selectedUserIds,
      selectedFacultyId.value ? parseInt(selectedFacultyId.value, 10) : 0,
    );
  } catch (error) {
    console.error('Error updating faculty users role:', error);
  }
  onDialogOK({
    ...formData.value,
  });
};

const onClickCancel = () => {
  onDialogCancel();
};

const toggleUser = (user: Record<string, unknown>) => {
  console.log('User toggled:', user);
  const userId = user.id as number;

  if (selectedUsers.value.has(userId)) {
    selectedUsers.value.delete(userId);
  } else {
    selectedUsers.value.add(userId);
  }

  console.log('Selected users:', Array.from(selectedUsers.value));
};

onMounted(async () => {
  await loadFaculties(true);
});

// Watch for faculty selection to load users
watch(
  () => selectedFaculty.value,
  async (newFaculty) => {
    console.log('Faculty selection changed:', newFaculty);
    // Clear selected users when faculty changes
    selectedUsers.value.clear();

    if (newFaculty && props.role?.id) {
      console.log('Faculty selected:', newFaculty);
      try {
        // Load all users in the faculty first
        console.log('Calling fetchUserByFaculty with ID:', newFaculty.id);
        await fetchUserByFaculty(Number(newFaculty.id));
        console.log('Users loaded for faculty:', allUsers.value);
        console.log('Number of users loaded:', allUsers.value.length || 0);

        // Then check which users already have this role in this specific faculty
        console.log(
          'Calling fetchUsersWithRole with facultyId:',
          newFaculty.id,
          'roleId:',
          props.role.id,
        );
        await fetchUsersWithRole(newFaculty.id, props.role.id);
      } catch (error) {
        console.error('Error loading users for faculty:', error);
      }
    } else {
      // Clear users when no faculty is selected
      console.log('Clearing users - no faculty selected');
      allUsers.value = [];
    }
  },
);
</script>

<style scoped>
.q-btn {
  min-width: 100px;
}

/* Force scrollbar visibility for faculty dropdown */
:deep(.q-select .q-menu) {
  max-height: 200px !important;
  overflow-y: scroll !important;
}

:deep(.q-select .q-menu .q-list) {
  max-height: 200px !important;
  overflow-y: scroll !important;
}

/* Custom scrollbar styling */
:deep(.q-select .q-menu::-webkit-scrollbar) {
  width: 12px !important;
  display: block !important;
}

:deep(.q-select .q-menu::-webkit-scrollbar-track) {
  background: #f1f1f1 !important;
}

:deep(.q-select .q-menu::-webkit-scrollbar-thumb) {
  background: #888 !important;
  border-radius: 4px !important;
}

:deep(.q-select .q-menu::-webkit-scrollbar-thumb:hover) {
  background: #666 !important;
}
</style>
