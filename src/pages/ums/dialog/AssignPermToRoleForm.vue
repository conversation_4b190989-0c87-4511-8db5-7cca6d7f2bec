<template>
  <q-dialog ref="dialogRef" persistent>
    <q-card class="card-form" bordered flat style="max-width: 800px; width: 100%">
      <q-form ref="formRef" @submit.prevent="onClickSave()">
        <q-card-section class="text-h6"> {{ computedTitle }}</q-card-section>
        <q-card-section class="q-gutter-y-lg">
          <div class="row q-gutter-sm items-center justify-end">
            <SearchBar @search="handleSearch" />
          </div>
        </q-card-section>
        <q-card-section class="q-gutter-y-lg">
          <q-table
            v-model:pagination="pagination"
            :rows="rows"
            :columns="AssignPermToRoleColumns"
            row-key="id"
            separator="cell"
          >
            <template #body-cell-actions="props">
              <q-td class="text-center">
                <div class="q-gutter-x-sm flex justify-center">
                  <q-toggle
                    v-model="props.row.selected"
                    @update:model-value="toggleUser(props.row)"
                  />
                </div>
              </q-td>
            </template>
          </q-table>
        </q-card-section>
        <q-card-actions align="right" class="q-pa-md q-mt-lg">
          <div class="q-gutter-sm">
            <q-btn
              icon="close"
              label="ยกเลิก"
              flat
              color="grey-7"
              @click="onClickCancel"
              style="border: 1px solid rgba(0, 0, 0, 0.3)"
            />
            <q-btn color="positive" icon="check" label="ยืนยัน" @click="onClickSave" />
          </div>
        </q-card-actions>
      </q-form>
    </q-card>
  </q-dialog>
</template>

<script setup lang="ts">
import { QForm, useDialogPluginComponent } from 'quasar';
import { defaultPaginationValue } from 'src/configs/pagination';
import type { Role } from 'src/types/models';
import { computed, ref } from 'vue';
import { AssignPermToRoleColumns } from 'src/data/table_columns';
import SearchBar from 'src/components/SearchBar.vue';

const pagination = ref({ ...defaultPaginationValue });
const rows = ref<Array<Record<string, unknown>>>([
  {
    id: 1,
    name: 'จัดการผู้ใช้งาน',
    description: 'สามารถเพิ่ม แก้ไข ลบผู้ใช้งานในระบบ',
    module: 'User Management',
    selected: false,
  },
  {
    id: 2,
    name: 'จัดการบทบาท',
    description: 'สามารถสร้างและแก้ไขบทบาทผู้ใช้งาน',
    module: 'Role Management',
    selected: true,
  },
  {
    id: 3,
    name: 'ดูรายงาน',
    description: 'สามารถเข้าถึงและดูรายงานต่างๆ',
    module: 'Reports',
    selected: false,
  },
  {
    id: 4,
    name: 'จัดการหลักสูตร',
    description: 'สามารถสร้าง แก้ไข และลบหลักสูตรฝึกอบรม',
    module: 'Course Management',
    selected: true,
  },
  {
    id: 5,
    name: 'ประเมินผลงาน',
    description: 'สามารถประเมินผลงานและให้คะแนนผู้เข้าร่วม',
    module: 'Evaluation',
    selected: false,
  },
  {
    id: 6,
    name: 'จัดการแบบทดสอบ',
    description: 'สามารถสร้างและแก้ไขแบบทดสอบ',
    module: 'Quiz Management',
    selected: true,
  },
]);

const props = defineProps<{
  role?: Role;
}>();

const { dialogRef, onDialogOK, onDialogCancel } = useDialogPluginComponent();

const formRef = ref<typeof QForm>();

const computedTitle = computed(() => {
  return props.role ? `จัดการสิทธิ์ - ${props.role.name}` : 'จัดการบุคลากร';
});

const formData = ref({
  ...props.role,
});

const onClickSave = async () => {
  if (!formRef.value) {
    console.error('Form reference is not set');
    return;
  }
  const isValid = await formRef.value.validate();
  if (!isValid) return;
  onDialogOK({
    ...formData.value,
  });
};

const onClickCancel = () => {
  onDialogCancel();
};

const toggleUser = (user: Record<string, unknown>) => {
  console.log('Permission toggled:', user);
  // Handle permission toggle logic here
  // This is where you would update the selected permissions for the role
};

const originalRows = [
  {
    id: 1,
    name: 'จัดการผู้ใช้งาน',
    description: 'สามารถเพิ่ม แก้ไข ลบผู้ใช้งานในระบบ',
    module: 'User Management',
    selected: false,
  },
  {
    id: 2,
    name: 'จัดการบทบาท',
    description: 'สามารถสร้างและแก้ไขบทบาทผู้ใช้งาน',
    module: 'Role Management',
    selected: true,
  },
  {
    id: 3,
    name: 'ดูรายงาน',
    description: 'สามารถเข้าถึงและดูรายงานต่างๆ',
    module: 'Reports',
    selected: false,
  },
  {
    id: 4,
    name: 'จัดการหลักสูตร',
    description: 'สามารถสร้าง แก้ไข และลบหลักสูตรฝึกอบรม',
    module: 'Course Management',
    selected: true,
  },
  {
    id: 5,
    name: 'ประเมินผลงาน',
    description: 'สามารถประเมินผลงานและให้คะแนนผู้เข้าร่วม',
    module: 'Evaluation',
    selected: false,
  },
  {
    id: 6,
    name: 'จัดการแบบทดสอบ',
    description: 'สามารถสร้างและแก้ไขแบบทดสอบ',
    module: 'Quiz Management',
    selected: true,
  },
];

const handleSearch = (searchTerm: string) => {
  if (!searchTerm) {
    rows.value = [...originalRows];
    return;
  }

  rows.value = originalRows.filter(
    (row) =>
      row.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      row.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
      row.module.toLowerCase().includes(searchTerm.toLowerCase()),
  );
};
</script>

<style scoped>
.q-btn {
  min-width: 100px;
}
</style>
