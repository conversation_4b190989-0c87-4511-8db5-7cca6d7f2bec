<template>
  <q-dialog ref="dialogRef" persistent>
    <q-card class="card-form" bordered flat style="max-width: 800px; width: 100%">
      <q-form ref="formRef" @submit.prevent="onClickSave">
        <q-card-section class="text-h6">{{ computedTitle }}</q-card-section>
        <q-card-section class="q-gutter-y-lg">
          <div class="row justify-between items-center">
            <q-select
              v-model="selectedFacultyOption"
              outlined
              dense
              use-input
              input-debounce="0"
              style="width: 250px"
              label="เลือกส่วนงาน"
              :options="facultiesOptions"
              @filter="filterFalculties"
              clearable
            >
              <template v-slot:prepend>
                <q-icon name="search" />
              </template>
            </q-select>
            <div class="row q-gutter-sm items-center">
              <SearchBar @search="handleSearch" />
            </div>
          </div>
        </q-card-section>
        <q-card-section class="q-gutter-y-lg">
          <q-table
            v-model:pagination="pagination"
            :rows="rows"
            :columns="AssignRoleToUserColumns"
            row-key="id"
            separator="cell"
            @request="onRequest"
          >
            <template #body-cell-actions="props">
              <q-td class="text-center">
                <div class="q-gutter-x-sm flex justify-center">
                  <q-checkbox
                    v-model="props.row.selected"
                    @update:model-value="toggleUser(props.row)"
                  />
                </div>
              </q-td>
            </template>
          </q-table>
        </q-card-section>
        <q-card-actions align="right" class="q-px-md">
          <div class="q-gutter-sm">
            <q-btn
              icon="close"
              label="ยกเลิก"
              flat
              color="grey-7"
              @click="onClickCancel"
              style="border: 1px solid rgba(0, 0, 0, 0.3)"
            />
            <q-btn color="positive" icon="check" label="ยืนยัน" @click="onClickSave" />
          </div>
        </q-card-actions>
      </q-form>
    </q-card>
  </q-dialog>
</template>

<script setup lang="ts">
import { QForm, useDialogPluginComponent, useQuasar, type QTableProps } from 'quasar';
import { useRoleService } from 'src/services/ums/roleService';
import { updateUserFacultiesRole, useUserService } from 'src/services/ums/userService';
import { smallPaginationValue } from 'src/configs/pagination';
import type { Role, User } from 'src/types/models';
import { computed, onMounted, ref, watch } from 'vue';
import { AssignRoleToUserColumns } from 'src/data/table_columns';
import SearchBar from 'src/components/SearchBar.vue';
import type { FacultyUser } from 'src/types/data';

interface FacultyOption {
  label: string;
  value: number;
}

const pagination = ref({ ...smallPaginationValue });
const searchText = ref<string>('');
const props = defineProps<{
  user?: User;
}>();

const allfaculties = ref<FacultyUser[]>([]);
const selectedFaculties = ref<FacultyUser>();
const selectedFacultyOption = ref<FacultyOption | null>(null);
const selectedFacultiesId = computed(() => selectedFaculties.value?.id);

const facultiesOptions = ref<FacultyOption[]>([]);
const { dialogRef, onDialogOK, onDialogCancel } = useDialogPluginComponent();
const $q = useQuasar();
const formRef = ref<typeof QForm>();
const computedTitle = computed(() =>
  props.user ? `จัดการบุคลากร: ${props.user.firstName} ${props.user.lastName}` : 'จัดการบุคลากร',
);
const formData = ref({
  ...props.user,
  roles: props.user?.roles ?? [],
});

const roleOptions = ref<Role[]>([]);
const rows = ref<Role[]>([]);
const selectedRoles = ref<Role[]>(props.user?.roles ?? []);
const selectedRoleIds = computed(() => selectedRoles.value.map((role) => role.id));

watch(
  roleOptions,
  (newRoles) => {
    rows.value = newRoles.map((role) => ({
      ...role,
      selected: selectedRoleIds.value.includes(role.id),
    }));
  },
  { immediate: true },
);

watch(selectedRoleIds, (ids) => {
  rows.value = rows.value.map((row) => ({
    ...row,
    selected: ids.includes(row.id),
  }));
});

watch(
  selectedRoles,
  (newRoles) => {
    formData.value.roles = newRoles;
  },
  { immediate: true },
);

watch(
  selectedFacultyOption,
  (newOption) => {
    if (newOption) {
      selectedFaculties.value = allfaculties.value.find((fac) => fac.id === newOption.value);
      // Update selected roles based on the selected faculty's roles
      if (selectedFaculties.value && selectedFaculties.value.roles) {
        selectedRoles.value = [...selectedFaculties.value.roles];
      } else {
        selectedRoles.value = [];
      }
    } else {
      selectedFaculties.value = undefined;
      selectedRoles.value = [];
    }
  },
  { immediate: true },
);

const onClickSave = async () => {
  if (!formRef.value) {
    console.error('Form reference is not set');
    return;
  }

  if (!props.user?.id) {
    console.error('User ID is not set');
    return;
  }

  if (!selectedFacultiesId.value) {
    $q.notify({
      type: 'negative',
      message: 'กรุณาเลือกส่วนงานก่อนทำการบันทึก',
    });
    return;
  }

  const isValid = await formRef.value.validate();
  if (isValid) {
    try {
      const roles = selectedRoles.value;
      const roleIds = roles.map((role) => role.id);
      console.log('Selected role IDs:', roleIds);
      console.log('Selected Faculty ID:', selectedFacultiesId.value);

      await updateUserFacultiesRole(
        Number(props.user.id),
        roleIds,
        selectedFacultiesId.value as number,
      );

      formData.value.roles = roles;

      $q.notify({
        type: 'positive',
        message: 'บันทึกการกำหนดบทบาทสำเร็จ',
      });

      onDialogOK({
        ...formData.value,
        id: props.user.id,
        roles: roles,
      });
    } catch (error) {
      console.error('Error updating user roles:', error);
      $q.notify({
        type: 'negative',
        message: 'เกิดข้อผิดพลาดในการบันทึกบทบาท',
      });
    }
  }
};

const filterFalculties = (val: string, update: (cb: () => void) => void) => {
  update(() => {
    const needle = val.toLowerCase();
    facultiesOptions.value = allfaculties.value
      .map((fac) => ({ label: fac.facultyName, value: fac.id }))
      .filter((opt) => opt.label.toLowerCase().includes(needle));
  });
};

const onClickCancel = () => {
  onDialogCancel();
};

const toggleUser = (user: Record<string, unknown>) => {
  const roleId = user.id as number;
  const isSelected = selectedRoles.value.some((role) => role.id === roleId);
  if (isSelected) {
    selectedRoles.value = selectedRoles.value.filter((role) => role.id !== roleId);
  } else {
    const role = rows.value.find((r) => r.id === roleId);
    if (role) {
      selectedRoles.value.push(role);
    }
  }
};

const handleSearch = (term: string) => {
  searchText.value = term;
  pagination.value.page = 1;
  fetchRoles();
};

const fetchRoles = async () => {
  const resRole = await useRoleService().getRoles(pagination.value, searchText.value);
  roleOptions.value = resRole.data;
  pagination.value.rowsNumber = resRole.total;
};

const fetchFaculties = async () => {
  try {
    const userId = props.user?.id ? Number(props.user.id) : 0;
    const res = await useUserService().getFalcultieUser(userId, pagination.value, searchText.value);
    allfaculties.value = res.data;

    facultiesOptions.value = allfaculties.value.map((fac) => ({
      label: fac.facultyName,
      value: fac.id,
    }));

    // Check if props.user and props.user.faculties are defined
    if (props.user && props.user.faculties && props.user.faculties.length > 0) {
      const userFacultyId = props.user?.faculties?.[0]?.id;
      const userFaculty = allfaculties.value.find((fac) => fac.facultyId === Number(userFacultyId));
      if (userFaculty) {
        selectedFaculties.value = userFaculty;
        selectedFacultyOption.value = {
          label: userFaculty.facultyName,
          value: userFaculty.id,
        };
        // Set initial roles based on the selected faculty
        if (userFaculty.roles) {
          selectedRoles.value = [...userFaculty.roles];
        }
      }
    } else if (allfaculties.value.length > 0) {
      // Optional: Fallback to first faculty if no user faculties are available
      const defaultFaculty = allfaculties.value[0]!;
      selectedFaculties.value = defaultFaculty;
      selectedFacultyOption.value = {
        label: defaultFaculty.facultyName,
        value: defaultFaculty.id,
      };
      // Set initial roles based on the default faculty
      if (defaultFaculty.roles) {
        selectedRoles.value = [...defaultFaculty.roles];
      }
    }
  } catch (err) {
    console.error(err);
    $q.notify({ type: 'negative', message: 'โหลดส่วนงานไม่สำเร็จ' });
  }
};

const onRequest: QTableProps['onRequest'] = ({ pagination: _pag }) => {
  pagination.value = { ...pagination.value, ..._pag };
  fetchRoles();
};

// Roles are now automatically checked based on the selected FacultyUser
onMounted(async () => {
  await Promise.all([fetchRoles(), fetchFaculties()]);
  if (props.user) {
    const resUser = await useUserService().getUserById(Number(props.user.id)); // Safe because props.user is checked
    formData.value = { ...resUser, roles: resUser.roles ?? [] };
  }
});
</script>

<style scoped>
.q-btn {
  min-width: 100px;
}
</style>
