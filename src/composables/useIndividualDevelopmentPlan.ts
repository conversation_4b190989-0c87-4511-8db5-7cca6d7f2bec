import { ref, computed } from 'vue';
import { useQuasar, type QTableProps } from 'quasar';
import type { IndividualDevelopmentPlan, IndividualDevelopmentPlanForm } from 'src/types/models';
import { individualDevelopmentPlanApi, type PaginationParams } from 'src/apis/individualDevelopmentPlanApi';

export function useIndividualDevelopmentPlan() {
  const $q = useQuasar();

  // State
  const loading = ref(false);
  const rows = ref<IndividualDevelopmentPlan[]>([]);
  const searchKeyword = ref('');
  const selectedItem = ref<IndividualDevelopmentPlan | null>(null);

  // Pagination
  const pagination = ref<{
    sortBy: string;
    descending: boolean;
    page: number;
    rowsPerPage: number;
    rowsNumber: number;
  }>({
    sortBy: 'id',
    descending: false,
    page: 1,
    rowsPerPage: 10,
    rowsNumber: 0,
  });

  // Computed
  const hasData = computed(() => rows.value.length > 0);
  const isEmpty = computed(() => !loading.value && rows.value.length === 0);

  // Methods
  const fetchPlans = async (requestProp?: { pagination: QTableProps['pagination'] }) => {
    loading.value = true;
    try {
      const paginationToUse = requestProp?.pagination || pagination.value;
      
      const paginationParams: PaginationParams = {
        page: paginationToUse?.page || 1,
        rowsPerPage: paginationToUse?.rowsPerPage || 10,
        sortBy: paginationToUse?.sortBy || 'id',
        descending: paginationToUse?.descending || false,
      };

      const response = await individualDevelopmentPlanApi.getAll(paginationParams, searchKeyword.value);
      
      if (response.success && response.data) {
        rows.value = response.data.data;
        pagination.value.rowsNumber = response.data.total;
        
        // Update pagination with type safety
        if (requestProp?.pagination) {
          const { sortBy, descending, page, rowsPerPage } = requestProp.pagination;
          pagination.value = {
            sortBy: sortBy || 'id',
            descending: descending ?? false,
            page: page || 1,
            rowsPerPage: rowsPerPage || 10,
            rowsNumber: response.data.total,
          };
        }
      } else {
        throw new Error(response.error || 'Failed to fetch plans');
      }
    } catch (error) {
      console.error('Error fetching plans:', error);
      $q.notify({
        type: 'negative',
        message: 'เกิดข้อผิดพลาดในการโหลดข้อมูล',
      });
    } finally {
      loading.value = false;
    }
  };

  const getPlanById = async (id: number): Promise<IndividualDevelopmentPlan | null> => {
    loading.value = true;
    try {
      const response = await individualDevelopmentPlanApi.getById(id);
      
      if (response.success && response.data) {
        return response.data;
      } else {
        throw new Error(response.error || 'Plan not found');
      }
    } catch (error) {
      console.error('Error fetching plan:', error);
      $q.notify({
        type: 'negative',
        message: 'เกิดข้อผิดพลาดในการโหลดข้อมูล',
      });
      return null;
    } finally {
      loading.value = false;
    }
  };

  const createPlan = async (planForm: IndividualDevelopmentPlanForm): Promise<boolean> => {
    loading.value = true;
    try {
      const response = await individualDevelopmentPlanApi.create(planForm);
      
      if (response.success) {
        $q.notify({
          type: 'positive',
          message: response.message || 'เพิ่มแผนพัฒนารายบุคคลเรียบร้อยแล้ว',
        });
        // Refresh the list
        await fetchPlans();
        return true;
      } else {
        throw new Error(response.error || 'Failed to create plan');
      }
    } catch (error) {
      console.error('Error creating plan:', error);
      $q.notify({
        type: 'negative',
        message: 'เกิดข้อผิดพลาดในการบันทึกข้อมูล',
      });
      return false;
    } finally {
      loading.value = false;
    }
  };

  const updatePlan = async (id: number, planForm: IndividualDevelopmentPlanForm): Promise<boolean> => {
    loading.value = true;
    try {
      const response = await individualDevelopmentPlanApi.update(id, planForm);
      
      if (response.success) {
        $q.notify({
          type: 'positive',
          message: response.message || 'แก้ไขแผนพัฒนารายบุคคลเรียบร้อยแล้ว',
        });
        // Refresh the list
        await fetchPlans();
        return true;
      } else {
        throw new Error(response.error || 'Failed to update plan');
      }
    } catch (error) {
      console.error('Error updating plan:', error);
      $q.notify({
        type: 'negative',
        message: 'เกิดข้อผิดพลาดในการบันทึกข้อมูล',
      });
      return false;
    } finally {
      loading.value = false;
    }
  };

  const deletePlan = async (id: number): Promise<boolean> => {
    loading.value = true;
    try {
      const response = await individualDevelopmentPlanApi.delete(id);
      
      if (response.success) {
        $q.notify({
          type: 'positive',
          message: response.message || 'ลบแผนพัฒนารายบุคคลเรียบร้อยแล้ว',
        });
        // Refresh the list
        await fetchPlans();
        return true;
      } else {
        throw new Error(response.error || 'Failed to delete plan');
      }
    } catch (error) {
      console.error('Error deleting plan:', error);
      $q.notify({
        type: 'negative',
        message: 'เกิดข้อผิดพลาดในการลบข้อมูล',
      });
      return false;
    } finally {
      loading.value = false;
    }
  };

  const onRequest = (props: { pagination: QTableProps['pagination'] }) => {
    fetchPlans(props);
  };

  const onSearchUpdate = (searchValue: string) => {
    searchKeyword.value = searchValue;
    pagination.value.page = 1; // Reset to first page on search
    fetchPlans();
  };

  const setSelectedItem = (item: IndividualDevelopmentPlan | null) => {
    selectedItem.value = item;
  };

  const resetPagination = () => {
    pagination.value.page = 1;
  };

  return {
    // State
    loading,
    rows,
    searchKeyword,
    selectedItem,
    pagination,
    
    // Computed
    hasData,
    isEmpty,
    
    // Methods
    fetchPlans,
    getPlanById,
    createPlan,
    updatePlan,
    deletePlan,
    onRequest,
    onSearchUpdate,
    setSelectedItem,
    resetPagination,
  };
}
