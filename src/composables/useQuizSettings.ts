import { ref, computed, watch, type Ref } from 'vue';
import type { Assessment } from 'src/types/models';
import {
  convertSecondsToHourMinute,
  convertToSeconds,
  convertToDecimal,
  convertToPercentage,
} from 'src/utils/timeUtils';

export interface QuizSettingsData {
  selectedDate: Ref<string>;
  selectedDateEnd: Ref<string>;
  hour: Ref<number | null>;
  minute: Ref<number | null>;
  attemptLimit: Ref<number>;
  passRatio: Ref<number>;
  isPrototype: Ref<boolean>;
}

export function useQuizSettings(assessment: Ref<Assessment | null>) {
  // Reactive refs for form fields
  const selectedDate = ref<string>('');
  const selectedDateEnd = ref<string>('');
  const hour = ref<number | null>(null);
  const minute = ref<number | null>(null);
  const attemptLimit = ref<number>(0);
  const passRatio = ref<number>(0);
  const isPrototype = ref<boolean>(false);

  // Computed property that builds the assessment object from form fields
  const assessmentToSave = computed(() => {
    if (!assessment.value) return null;

    const result = { ...assessment.value };

    // Handle dates - only include if they have valid values, remove null/empty values
    if (selectedDate.value && selectedDate.value.trim() !== '') {
      try {
        // Send as ISO string - NestJS should handle the conversion
        result.startAt = new Date(selectedDate.value).toISOString();
      } catch {
        console.warn('Invalid startAt date:', selectedDate.value);
        delete result.startAt;
      }
    } else {
      // Remove null/empty values completely from payload
      delete result.startAt;
    }

    if (selectedDateEnd.value && selectedDateEnd.value.trim() !== '') {
      try {
        // Send as ISO string - NestJS should handle the conversion
        result.endAt = new Date(selectedDateEnd.value).toISOString();
      } catch {
        console.warn('Invalid endAt date:', selectedDateEnd.value);
        delete result.endAt;
      }
    } else {
      // Remove null/empty values completely from payload
      delete result.endAt;
    }

    // Handle timeout
    if (hour.value !== null && minute.value !== null && hour.value >= 0 && minute.value >= 0) {
      result.timeout = convertToSeconds(hour.value, minute.value);
    } else {
      result.timeout = 0;
    }

    // Handle submit limit
    result.submitLimit = Number(attemptLimit.value) || -1;

    // Handle pass ratio
    if (passRatio.value !== undefined && passRatio.value !== null) {
      const validatedPercentage = Math.min(Math.max(Number(passRatio.value), 0), 100);
      result.passRatio = convertToDecimal(validatedPercentage);
    } else {
      result.passRatio = 0.5;
    }

    result.isPrototype = Boolean(isPrototype.value);

    return result;
  });

  // Function to load data from assessment into form fields
  const loadFromAssessment = (assessmentData: Assessment) => {
    selectedDate.value = assessmentData.startAt || '';
    selectedDateEnd.value = assessmentData.endAt || '';

    if (assessmentData.timeout) {
      const { hours, minutes } = convertSecondsToHourMinute(assessmentData.timeout);
      hour.value = hours ?? null;
      minute.value = minutes ?? null;
    } else {
      hour.value = null;
      minute.value = null;
    }

    attemptLimit.value = assessmentData.submitLimit ?? -1;
    passRatio.value = assessmentData.passRatio ? convertToPercentage(assessmentData.passRatio) : 0;
    isPrototype.value = assessmentData.isPrototype ?? false;
  };

  // Watch for changes in individual fields and update the assessment
  watch([selectedDate, selectedDateEnd, hour, minute, attemptLimit, passRatio, isPrototype], () => {
    if (assessment.value && assessmentToSave.value) {
      // Update the assessment object
      Object.assign(assessment.value, assessmentToSave.value);
    }
  });

  return {
    // Form fields
    selectedDate,
    selectedDateEnd,
    hour,
    minute,
    attemptLimit,
    passRatio,
    isPrototype,

    // Computed assessment
    assessmentToSave,

    // Utility functions
    loadFromAssessment,
  };
}
