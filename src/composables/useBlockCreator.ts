import { ref } from 'vue';
import type { ItemBlock, Assessment } from 'src/types/models';
import { AssessmentService } from 'src/services/asm/assessmentService';
import { getCurrentSection } from 'src/utils/block_helper';
import { useGlobalStore } from 'src/stores/global';
import { insertBlockWithSequenceManagement } from 'src/utils/blockOperations';
import { findMaxSectionNumber } from 'src/utils/sectionManager';

/**
 * Block creation composable
 */
export function useBlockCreator() {
  const isBlockCreationInProgress = ref(false);

  /**
   * Create new block after index
   */
  async function createNewBlockAfterIndex(
    blockList: ItemBlock[],
    targetIndex: number,
    assessmentType: 'quiz' | 'evaluate',
    assessmentId: number,
    activeAssessment: Assessment | null
  ): Promise<{ success: boolean; newBlock?: ItemBlock; updatedBlocks?: ItemBlock[] }> {
    if (isBlockCreationInProgress.value) {
      return { success: false };
    }

    try {
      isBlockCreationInProgress.value = true;
      const globalStore = useGlobalStore();
      globalStore.startSaveOperation('Creating new question...');

      if (!assessmentId) {
        globalStore.completeSaveOperation(false, 'Assessment ID not found');
        return { success: false };
      }

      const globalIsRequired = activeAssessment?.globalIsRequired ?? false;
      const currentSection = getCurrentSection(blockList, targetIndex);
      const currentBlock = blockList[targetIndex];
      const newSequence = currentBlock ? currentBlock.sequence + 1 : targetIndex + 1;

      const newBlockData = {
        assessmentId,
        sequence: newSequence,
        section: currentSection,
        type: 'RADIO' as const,
        isRequired: globalIsRequired,
      };

      const assessmentService = new AssessmentService(assessmentType);
      const createdBlock = await assessmentService.createBlock(newBlockData);

      if (createdBlock) {
        const updatedBlocks = insertBlockWithSequenceManagement(blockList, createdBlock, targetIndex);
        globalStore.completeSaveOperation(true, 'Question created successfully');
        return { success: true, newBlock: createdBlock, updatedBlocks };
      } else {
        globalStore.completeSaveOperation(false, 'Failed to create question');
        return { success: false };
      }
    } catch (error) {
      console.error('❌ Error creating new block:', error);
      const globalStore = useGlobalStore();
      globalStore.completeSaveOperation(false, 'Error creating question');
      return { success: false };
    } finally {
      isBlockCreationInProgress.value = false;
    }
  }

  /**
   * Create new header after index
   */
  async function createNewHeaderAfterIndex(
    blockList: ItemBlock[],
    targetIndex: number,
    assessmentType: 'quiz' | 'evaluate',
    assessmentId: number,
    assignSection = true
  ): Promise<{ success: boolean; newBlock?: ItemBlock; updatedBlocks?: ItemBlock[] }> {
    if (isBlockCreationInProgress.value) {
      return { success: false };
    }

    try {
      isBlockCreationInProgress.value = true;
      const globalStore = useGlobalStore();
      const operationMessage = assignSection
        ? 'Creating new section header...'
        : 'Creating new text header...';
      globalStore.startSaveOperation(operationMessage);

      if (!assessmentId) {
        globalStore.completeSaveOperation(false, 'Assessment ID not found');
        return { success: false };
      }

      const sectionToAssign = assignSection ? getCurrentSection(blockList, targetIndex) : 0;
      const currentBlock = blockList[targetIndex];
      const newSequence = currentBlock ? currentBlock.sequence + 1 : targetIndex + 1;

      const newHeaderData = {
        assessmentId,
        sequence: newSequence,
        section: sectionToAssign,
        type: 'HEADER' as const,
        isRequired: false,
      };

      const assessmentService = new AssessmentService(assessmentType);
      const createdHeader = await assessmentService.createBlock(newHeaderData);

      if (createdHeader) {
        const updatedBlocks = insertBlockWithSequenceManagement(blockList, createdHeader, targetIndex);
        const successMessage = assignSection
          ? 'Section header created successfully'
          : 'Text header created successfully';
        globalStore.completeSaveOperation(true, successMessage);
        return { success: true, newBlock: createdHeader, updatedBlocks };
      } else {
        const errorMessage = assignSection
          ? 'Failed to create section header'
          : 'Failed to create text header';
        globalStore.completeSaveOperation(false, errorMessage);
        return { success: false };
      }
    } catch (error) {
      console.error('❌ Error creating header block:', error);
      const globalStore = useGlobalStore();
      const errorMessage = assignSection
        ? 'Error creating section header'
        : 'Error creating text header';
      globalStore.completeSaveOperation(false, errorMessage);
      return { success: false };
    } finally {
      isBlockCreationInProgress.value = false;
    }
  }

  /**
   * Create new section
   */
  async function createNewSection(
    blockList: ItemBlock[],
    assessmentType: 'quiz' | 'evaluate',
    assessmentId: number
  ): Promise<{ success: boolean; newBlocks?: ItemBlock[]; updatedBlocks?: ItemBlock[] }> {
    if (isBlockCreationInProgress.value) {
      return { success: false };
    }

    try {
      isBlockCreationInProgress.value = true;
      const globalStore = useGlobalStore();
      globalStore.startSaveOperation('Creating new section...');

      if (!assessmentId) {
        console.error('❌ Assessment ID is required for section creation');
        globalStore.completeSaveOperation(false, 'Assessment ID is required for section creation');
        return { success: false };
      }

      const newSectionNumber = findMaxSectionNumber(blockList) + 1;
      const newHeaderData = {
        assessmentId,
        sequence: blockList.length + 1,
        section: newSectionNumber,
        type: 'HEADER' as const,
        isRequired: false,
      };

      const assessmentService = new AssessmentService(assessmentType);
      const createdHeader = await assessmentService.createBlock(newHeaderData);

      if (createdHeader) {
        const newBlocks = [createdHeader];
        const updatedBlocks = [...blockList, createdHeader];

        // Create default ItemBlock for new section
        const defaultItemBlockData = {
          assessmentId: createdHeader.assessmentId,
          sequence: createdHeader.sequence + 1,
          section: createdHeader.section,
          type: 'RADIO' as const,
          isRequired: false,
        };

        const createdItemBlock = await assessmentService.createBlock(defaultItemBlockData);
        if (createdItemBlock) {
          newBlocks.push(createdItemBlock);
          updatedBlocks.push(createdItemBlock);
        }

        // Create NEXTSECTION block
        const nextSectionBlockData = {
          assessmentId: createdHeader.assessmentId,
          sequence: createdHeader.sequence + 2,
          section: createdHeader.section,
          type: 'NEXTSECTION' as const,
          isRequired: false,
        };

        const createdNextSectionBlock = await assessmentService.createBlock(nextSectionBlockData);
        if (createdNextSectionBlock) {
          newBlocks.push(createdNextSectionBlock);
          updatedBlocks.push(createdNextSectionBlock);
        }

        globalStore.completeSaveOperation(true, 'Section created successfully');
        return { success: true, newBlocks, updatedBlocks };
      } else {
        globalStore.completeSaveOperation(false, 'Failed to create section');
        return { success: false };
      }
    } catch (error) {
      console.error('❌ Error creating section:', error);
      const globalStore = useGlobalStore();
      globalStore.completeSaveOperation(false, 'Error creating section');
      return { success: false };
    } finally {
      isBlockCreationInProgress.value = false;
    }
  }

  return {
    isBlockCreationInProgress,
    createNewBlockAfterIndex,
    createNewHeaderAfterIndex,
    createNewSection,
  };
}
