import { ref, watch, type Ref } from 'vue';
import { debounce } from 'quasar';
import type { Assessment } from 'src/types/models';
import { useGlobalStore } from 'src/stores/global';

export interface AutoSaveOptions {
  debounceMs?: number;
  onSave: (data: Assessment) => Promise<void>;
  onError?: (error: unknown) => void;
}

export function useAutoSave(data: Ref<Assessment | null>, options: AutoSaveOptions) {
  const { debounceMs = 500, onSave, onError } = options;
  const globalStore = useGlobalStore();
  const isSaving = ref(false);
  const lastSavedData = ref<string>('');

  const saveData = debounce(async () => {
    if (!data.value || isSaving.value) return;

    // Check if data actually changed
    const currentDataString = JSON.stringify(data.value);
    if (currentDataString === lastSavedData.value) return;

    try {
      isSaving.value = true;
      globalStore.startSaveOperation('Saving...');

      // Log the data being sent for debugging
      console.log('🔍 Auto-save sending data:', JSON.stringify(data.value, null, 2));
      
      await onSave(data.value);

      lastSavedData.value = currentDataString;
      globalStore.completeSaveOperation(true, 'Saved successfully');
    } catch (error) {
      console.error('❌ Auto-save error:', error);

      // Log detailed error information
      if (error && typeof error === 'object' && 'response' in error) {
        const axiosError = error as { response?: { data?: unknown; status?: number } };
        console.error('❌ Server response:', axiosError.response?.data);
        console.error('❌ Status code:', axiosError.response?.status);
      }

      globalStore.completeSaveOperation(false, 'Save failed');

      if (onError) {
        onError(error);
      }
    } finally {
      isSaving.value = false;
    }
  }, debounceMs);

  // Watch for changes in the data
  watch(
    data,
    () => {
      if (data.value) {
        saveData();
      }
    },
    { deep: true },
  );

  // Manual save function
  const save = async () => {
    if (data.value && !isSaving.value) {
      saveData();
    }
  };

  // Force save without debounce
  const forceSave = async () => {
    if (!data.value || isSaving.value) return;

    try {
      isSaving.value = true;
      globalStore.startSaveOperation('Saving...');

      await onSave(data.value);

      lastSavedData.value = JSON.stringify(data.value);
      globalStore.completeSaveOperation(true, 'Saved successfully');
    } catch (error) {
      console.error('Error saving data:', error);
      globalStore.completeSaveOperation(false, 'Save failed');

      if (onError) {
        onError(error);
      }
    } finally {
      isSaving.value = false;
    }
  };

  return {
    isSaving,
    save,
    forceSave,
  };
}
