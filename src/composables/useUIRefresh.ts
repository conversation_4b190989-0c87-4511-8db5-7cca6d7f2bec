import { ref, nextTick } from 'vue';
import type { ItemBlock, Assessment } from 'src/types/models';

/**
 * UI refresh composable for block management
 */

// Constants
const UI_REFRESH_ITERATIONS = 5;
const DELAYED_REFRESH_DELAYS = [200, 100, 100];

export function useUIRefresh() {
  const uiRefreshTrigger = ref(0);

  /**
   * Execute basic UI refresh
   */
  async function executeBasicUIRefresh(): Promise<void> {
    uiRefreshTrigger.value++;
    await nextTick();
    uiRefreshTrigger.value++;
    await nextTick();
  }

  /**
   * Execute complex UI refresh with multiple iterations
   */
  async function executeComplexUIRefresh(
    activeAssessment?: Assessment | null,
    blockList?: ItemBlock[]
  ): Promise<void> {
    await executeBasicUIRefresh();

    if (activeAssessment && blockList) {
      activeAssessment.itemBlocks = [...blockList];
      await nextTick();
    }

    // Multiple iterations for complex DOM
    for (let i = 0; i < UI_REFRESH_ITERATIONS; i++) {
      uiRefreshTrigger.value++;
      await nextTick();
    }

    // Delayed refresh
    for (const delay of DELAYED_REFRESH_DELAYS) {
      await executeDelayedRefresh(delay);
      await nextTick();
    }
  }

  /**
   * Execute delayed refresh
   */
  async function executeDelayedRefresh(delay: number): Promise<void> {
    return new Promise<void>((resolve) => {
      setTimeout(() => {
        uiRefreshTrigger.value++;
        resolve();
      }, delay);
    });
  }

  /**
   * Synchronize blocks with assessment
   */
  async function synchronizeBlocksWithAssessment(
    activeAssessment: Assessment | null,
    blockList: ItemBlock[]
  ): Promise<ItemBlock[]> {
    if (activeAssessment && activeAssessment.itemBlocks) {
      const synchronized = [...activeAssessment.itemBlocks];
      await executeBasicUIRefresh();
      return synchronized;
    }
    return blockList;
  }

  return {
    uiRefreshTrigger,
    executeBasicUIRefresh,
    executeComplexUIRefresh,
    executeDelayedRefresh,
    synchronizeBlocksWithAssessment,
  };
}
