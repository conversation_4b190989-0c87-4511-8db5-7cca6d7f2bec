import { ref, computed, type Ref } from 'vue';
import type { Assessment } from 'src/types/models';

/**
 * Composable for managing evaluate settings form fields
 * Centralizes form logic and provides reactive state management
 */
export function useEvaluateSettings(currentAssessment: Ref<Assessment | null>) {
  // Form field refs
  const startDate = ref('');
  const endDate = ref('');
  const responseEdit = ref(false);
  const subLimit = ref<number | null>(null);
  const globalIsRequired = ref(false); // Keep for frontend-only logic
  const isPrototype = ref(false);

  // Computed property to build assessment object for saving
  const assessmentToSave = computed(() => {
    if (!currentAssessment.value) return null;

    const assessment = { ...currentAssessment.value };

    // Handle dates - remove if empty, otherwise keep the value
    if (startDate.value) {
      assessment.startAt = startDate.value;
    } else {
      delete assessment.startAt;
    }

    if (endDate.value) {
      assessment.endAt = endDate.value;
    } else {
      delete assessment.endAt;
    }

    // Set other properties with proper type conversion
    assessment.responseEdit = Boolean(responseEdit.value);
    assessment.submitLimit = Number(subLimit.value);
    assessment.globalIsRequired = Boolean(globalIsRequired.value); // Keep for frontend
    assessment.isPrototype = Boolean(isPrototype.value);

    return assessment;
  });

  // Computed for submission limit toggle
  const limitOneSubmission = computed({
    get: () => subLimit.value === 1,
    set: (value: boolean) => {
      subLimit.value = value ? 1 : -1;
    },
  });

  /**
   * Load form fields from assessment data
   */
  const loadFromAssessment = (assessment: Assessment) => {
    try {
      startDate.value = assessment.startAt || '';
      endDate.value = assessment.endAt || '';
      responseEdit.value = assessment.responseEdit || false;
      subLimit.value = assessment.submitLimit ?? null;
      globalIsRequired.value = assessment.globalIsRequired || false;
      isPrototype.value = assessment.isPrototype || false;
    } catch (error) {
      console.error('Error loading evaluate settings from assessment:', error);
      // Set default values on error
      startDate.value = '';
      endDate.value = '';
      responseEdit.value = false;
      subLimit.value = null;
      globalIsRequired.value = false;
      isPrototype.value = false;
    }
  };

  /**
   * Helper function to calculate the actual global isRequired state based on itemBlocks
   */
  const calculateActualGlobalIsRequired = (): boolean => {
    if (!currentAssessment.value?.itemBlocks) {
      return false;
    }

    const eligibleBlocks = currentAssessment.value.itemBlocks.filter(
      (block) => block.type !== 'HEADER' && block.type !== 'IMAGE',
    );

    if (eligibleBlocks.length === 0) {
      return false;
    }

    return eligibleBlocks.every((block) => block.isRequired === true);
  };

  return {
    // Form fields
    startDate,
    endDate,
    responseEdit,
    subLimit,
    globalIsRequired,
    isPrototype,
    
    // Computed properties
    assessmentToSave,
    limitOneSubmission,
    
    // Methods
    loadFromAssessment,
    calculateActualGlobalIsRequired,
  };
}
