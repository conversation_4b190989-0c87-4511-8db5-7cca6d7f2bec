import {
  Injectable,
  CanActivate,
  ExecutionContext,
  ForbiddenException,
} from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { PERMISSIONS_KEY } from '../decorators/permissions.decorator';
import { User } from '../../resources/users/entities/user.entity';

@Injectable()
export class PermissionsGuard implements CanActivate {
  constructor(private readonly reflector: Reflector) {}

  canActivate(context: ExecutionContext): boolean {
    const requiredPermissions = this.reflector.getAllAndOverride<string[]>(
      PERMISSIONS_KEY,
      [context.getHandler(), context.getClass()],
    );

    if (!requiredPermissions) {
      return true;
    }

    const request = context.switchToHttp().getRequest();
    const user: User = request.user;

    if (!user) {
      throw new ForbiddenException('User not authenticated');
    }

    // Check if user has super_operator permission (grants access to everything)
    // Check faculty role-based permissions through the new structure
    const hasSuperOperator = (user as any).faculties?.some((faculty: any) =>
      faculty.roles?.some((role: any) =>
        role.permissions?.some((permission: any) => permission.perName === 'super_operator')
      )
    );

    if (hasSuperOperator) {
      return true;
    }

    // Check if user has any of the required permissions
    // Get all permissions from all faculties and roles
    const allUserPermissions = (user as any).faculties?.flatMap((faculty: any) =>
      faculty.roles?.flatMap((role: any) =>
        role.permissions?.map((permission: any) => permission.perName) || []
      ) || []
    ) || [];

    const hasPermission = requiredPermissions.some(permission =>
      allUserPermissions.includes(permission)
    );

    if (!hasPermission) {
      throw new ForbiddenException(
        `Access denied. Required permissions: ${requiredPermissions.join(', ')}`
      );
    }

    return true;
  }
}
