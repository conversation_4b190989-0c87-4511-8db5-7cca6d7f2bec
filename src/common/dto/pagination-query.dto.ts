import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsNumber, IsOptional, IsString, IsIn, Min } from 'class-validator';
import { Type } from 'class-transformer';

export class PaginationQueryDto {
  @ApiPropertyOptional({ 
    default: 10, 
    description: 'Number of items per page',
    minimum: 1
  })
  @IsNumber()
  @Min(1)
  @Type(() => Number)
  @IsOptional()
  limit: number = 10;

  @ApiPropertyOptional({ 
    default: 1, 
    description: 'Page number',
    minimum: 1
  })
  @IsNumber()
  @Min(1)
  @Type(() => Number)
  @IsOptional()
  page: number = 1;

  @ApiPropertyOptional({
    description: 'Field to sort by',
    default: 'id'
  })
  @IsString()
  @IsOptional()
  sortBy: string = 'id';

  @ApiPropertyOptional({
    description: 'Sort order',
    enum: ['ASC', 'DESC'],
    default: 'ASC'
  })
  @IsString()
  @IsIn(['ASC', 'DESC'])
  @IsOptional()
  order: 'ASC' | 'DESC' = 'ASC';

  @ApiPropertyOptional({
    description: 'Filter by career type',
  })
  @IsOptional()
  @IsString()
  career_type?: string;

  @ApiPropertyOptional({
    description: 'Search term',
  })
  @IsOptional()
  @IsString()
  search?: string;
}
