# PositionsApi

All URIs are relative to *http://localhost*

|Method | HTTP request | Description|
|------------- | ------------- | -------------|
|[**positionControllerCreate**](#positioncontrollercreate) | **POST** /positions | Create a new position|
|[**positionControllerFindAll**](#positioncontrollerfindall) | **GET** /positions | Get all positions|
|[**positionControllerFindOne**](#positioncontrollerfindone) | **GET** /positions/{id} | Get a position by ID|
|[**positionControllerRemove**](#positioncontrollerremove) | **DELETE** /positions/{id} | Delete a position|
|[**positionControllerUpdate**](#positioncontrollerupdate) | **PATCH** /positions/{id} | Update a position|

# **positionControllerCreate**
> positionControllerCreate(createPositionDto)


### Example

```typescript
import {
    PositionsApi,
    Configuration,
    CreatePositionDto
} from './api';

const configuration = new Configuration();
const apiInstance = new PositionsApi(configuration);

let createPositionDto: CreatePositionDto; //

const { status, data } = await apiInstance.positionControllerCreate(
    createPositionDto
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **createPositionDto** | **CreatePositionDto**|  | |


### Return type

void (empty response body)

### Authorization

[bearer](../README.md#bearer)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: Not defined


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**201** |  |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **positionControllerFindAll**
> positionControllerFindAll()


### Example

```typescript
import {
    PositionsApi,
    Configuration
} from './api';

const configuration = new Configuration();
const apiInstance = new PositionsApi(configuration);

let limit: any; //Number of items per page (default to undefined)
let page: any; //Page number to retrieve (default to undefined)
let sortBy: any; //Field to sort by (e.g., id, name, email) (optional) (default to undefined)
let order: any; //Sort order (ASC or DESC) (optional) (default to undefined)
let search: any; //Search term to filter results (optional) (default to undefined)

const { status, data } = await apiInstance.positionControllerFindAll(
    limit,
    page,
    sortBy,
    order,
    search
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **limit** | **any** | Number of items per page | defaults to undefined|
| **page** | **any** | Page number to retrieve | defaults to undefined|
| **sortBy** | **any** | Field to sort by (e.g., id, name, email) | (optional) defaults to undefined|
| **order** | **any** | Sort order (ASC or DESC) | (optional) defaults to undefined|
| **search** | **any** | Search term to filter results | (optional) defaults to undefined|


### Return type

void (empty response body)

### Authorization

[bearer](../README.md#bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**200** | Return all positions |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **positionControllerFindOne**
> positionControllerFindOne()


### Example

```typescript
import {
    PositionsApi,
    Configuration
} from './api';

const configuration = new Configuration();
const apiInstance = new PositionsApi(configuration);

let id: string; //Position ID (default to undefined)

const { status, data } = await apiInstance.positionControllerFindOne(
    id
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **id** | [**string**] | Position ID | defaults to undefined|


### Return type

void (empty response body)

### Authorization

[bearer](../README.md#bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**200** |  |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **positionControllerRemove**
> positionControllerRemove()


### Example

```typescript
import {
    PositionsApi,
    Configuration
} from './api';

const configuration = new Configuration();
const apiInstance = new PositionsApi(configuration);

let id: string; //Position ID (default to undefined)

const { status, data } = await apiInstance.positionControllerRemove(
    id
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **id** | [**string**] | Position ID | defaults to undefined|


### Return type

void (empty response body)

### Authorization

[bearer](../README.md#bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**200** |  |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **positionControllerUpdate**
> positionControllerUpdate(updatePositionDto)


### Example

```typescript
import {
    PositionsApi,
    Configuration,
    UpdatePositionDto
} from './api';

const configuration = new Configuration();
const apiInstance = new PositionsApi(configuration);

let id: string; //Position ID (default to undefined)
let updatePositionDto: UpdatePositionDto; //

const { status, data } = await apiInstance.positionControllerUpdate(
    id,
    updatePositionDto
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **updatePositionDto** | **UpdatePositionDto**|  | |
| **id** | [**string**] | Position ID | defaults to undefined|


### Return type

void (empty response body)

### Authorization

[bearer](../README.md#bearer)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: Not defined


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**200** |  |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

