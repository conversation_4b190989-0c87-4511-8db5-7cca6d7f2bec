# PermissionsApi

All URIs are relative to *http://localhost*

|Method | HTTP request | Description|
|------------- | ------------- | -------------|
|[**permissionsControllerCreate**](#permissionscontrollercreate) | **POST** /permissions | |
|[**permissionsControllerFindAll**](#permissionscontrollerfindall) | **GET** /permissions | |
|[**permissionsControllerFindAllByStatus**](#permissionscontrollerfindallbystatus) | **GET** /permissions/status | |
|[**permissionsControllerFindOne**](#permissionscontrollerfindone) | **GET** /permissions/{id} | |
|[**permissionsControllerRemove**](#permissionscontrollerremove) | **DELETE** /permissions/{id} | |
|[**permissionsControllerSetStatus**](#permissionscontrollersetstatus) | **PATCH** /permissions/{id}/set-status | |
|[**permissionsControllerUpdate**](#permissionscontrollerupdate) | **PATCH** /permissions/{id} | |

# **permissionsControllerCreate**
> permissionsControllerCreate()


### Example

```typescript
import {
    PermissionsApi,
    Configuration
} from './api';

const configuration = new Configuration();
const apiInstance = new PermissionsApi(configuration);

let name: string; //ชื่อสิทธิ์ (default to undefined)
let nameEn: string; //รายละเอียดสิทธิ์ภาษาอังกฤษ (default to undefined)
let descTh: string; //รายละเอียดสิทธิ์ภาษาไทย (default to undefined)
let status: boolean; //สถานะการใช้งาน (optional) (default to undefined)
let isDefault: boolean; //เป็นสิทธิ์เริ่มต้นหรือไม่ (optional) (default to undefined)

const { status, data } = await apiInstance.permissionsControllerCreate(
    name,
    nameEn,
    descTh,
    status,
    isDefault
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **name** | [**string**] | ชื่อสิทธิ์ | defaults to undefined|
| **nameEn** | [**string**] | รายละเอียดสิทธิ์ภาษาอังกฤษ | defaults to undefined|
| **descTh** | [**string**] | รายละเอียดสิทธิ์ภาษาไทย | defaults to undefined|
| **status** | [**boolean**] | สถานะการใช้งาน | (optional) defaults to undefined|
| **isDefault** | [**boolean**] | เป็นสิทธิ์เริ่มต้นหรือไม่ | (optional) defaults to undefined|


### Return type

void (empty response body)

### Authorization

[bearer](../README.md#bearer)

### HTTP request headers

 - **Content-Type**: multipart/form-data
 - **Accept**: Not defined


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**201** |  |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **permissionsControllerFindAll**
> permissionsControllerFindAll()


### Example

```typescript
import {
    PermissionsApi,
    Configuration
} from './api';

const configuration = new Configuration();
const apiInstance = new PermissionsApi(configuration);

let limit: any; //Number of items per page (default to undefined)
let page: any; //Page number to retrieve (default to undefined)
let sortBy: any; //Field to sort by (e.g., id, name, email) (optional) (default to undefined)
let order: any; //Sort order (ASC or DESC) (optional) (default to undefined)
let search: any; //Search term to filter results (optional) (default to undefined)

const { status, data } = await apiInstance.permissionsControllerFindAll(
    limit,
    page,
    sortBy,
    order,
    search
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **limit** | **any** | Number of items per page | defaults to undefined|
| **page** | **any** | Page number to retrieve | defaults to undefined|
| **sortBy** | **any** | Field to sort by (e.g., id, name, email) | (optional) defaults to undefined|
| **order** | **any** | Sort order (ASC or DESC) | (optional) defaults to undefined|
| **search** | **any** | Search term to filter results | (optional) defaults to undefined|


### Return type

void (empty response body)

### Authorization

[bearer](../README.md#bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**200** |  |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **permissionsControllerFindAllByStatus**
> permissionsControllerFindAllByStatus()


### Example

```typescript
import {
    PermissionsApi,
    Configuration
} from './api';

const configuration = new Configuration();
const apiInstance = new PermissionsApi(configuration);

let perStatus: boolean; // (default to undefined)

const { status, data } = await apiInstance.permissionsControllerFindAllByStatus(
    perStatus
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **perStatus** | [**boolean**] |  | defaults to undefined|


### Return type

void (empty response body)

### Authorization

[bearer](../README.md#bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**200** |  |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **permissionsControllerFindOne**
> permissionsControllerFindOne()


### Example

```typescript
import {
    PermissionsApi,
    Configuration
} from './api';

const configuration = new Configuration();
const apiInstance = new PermissionsApi(configuration);

let id: number; // (default to undefined)

const { status, data } = await apiInstance.permissionsControllerFindOne(
    id
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **id** | [**number**] |  | defaults to undefined|


### Return type

void (empty response body)

### Authorization

[bearer](../README.md#bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**200** |  |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **permissionsControllerRemove**
> permissionsControllerRemove()


### Example

```typescript
import {
    PermissionsApi,
    Configuration
} from './api';

const configuration = new Configuration();
const apiInstance = new PermissionsApi(configuration);

let id: number; // (default to undefined)

const { status, data } = await apiInstance.permissionsControllerRemove(
    id
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **id** | [**number**] |  | defaults to undefined|


### Return type

void (empty response body)

### Authorization

[bearer](../README.md#bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**200** |  |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **permissionsControllerSetStatus**
> permissionsControllerSetStatus()


### Example

```typescript
import {
    PermissionsApi,
    Configuration
} from './api';

const configuration = new Configuration();
const apiInstance = new PermissionsApi(configuration);

let id: number; // (default to undefined)
let status: boolean; //สถานะการใช้งานสิทธิ์ (default to undefined)

const { status, data } = await apiInstance.permissionsControllerSetStatus(
    id,
    status
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **id** | [**number**] |  | defaults to undefined|
| **status** | [**boolean**] | สถานะการใช้งานสิทธิ์ | defaults to undefined|


### Return type

void (empty response body)

### Authorization

[bearer](../README.md#bearer)

### HTTP request headers

 - **Content-Type**: multipart/form-data
 - **Accept**: Not defined


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**200** |  |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **permissionsControllerUpdate**
> permissionsControllerUpdate()


### Example

```typescript
import {
    PermissionsApi,
    Configuration
} from './api';

const configuration = new Configuration();
const apiInstance = new PermissionsApi(configuration);

let id: number; // (default to undefined)

const { status, data } = await apiInstance.permissionsControllerUpdate(
    id
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **id** | [**number**] |  | defaults to undefined|


### Return type

void (empty response body)

### Authorization

[bearer](../README.md#bearer)

### HTTP request headers

 - **Content-Type**: multipart/form-data
 - **Accept**: Not defined


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**200** |  |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

