# CompetenciesApi

All URIs are relative to *http://localhost*

|Method | HTTP request | Description|
|------------- | ------------- | -------------|
|[**competenciesControllerCreate**](#competenciescontrollercreate) | **POST** /Competencies | Create a competency|
|[**competenciesControllerFindAll**](#competenciescontrollerfindall) | **GET** /Competencies | Get all competencies (with pagination)|
|[**competenciesControllerFindByType**](#competenciescontrollerfindbytype) | **GET** /Competencies/type/{career_type} | Get competency by Type|
|[**competenciesControllerFindOne**](#competenciescontrollerfindone) | **GET** /Competencies/{id} | Get competency by ID|
|[**competenciesControllerRemove**](#competenciescontrollerremove) | **DELETE** /Competencies/{id} | Delete a competency|
|[**competenciesControllerUpdate**](#competenciescontrollerupdate) | **PATCH** /Competencies/{id} | Update a competency|

# **competenciesControllerCreate**
> competenciesControllerCreate(createCompetenciesDto)


### Example

```typescript
import {
    CompetenciesApi,
    Configuration,
    CreateCompetenciesDto
} from './api';

const configuration = new Configuration();
const apiInstance = new CompetenciesApi(configuration);

let createCompetenciesDto: CreateCompetenciesDto; //

const { status, data } = await apiInstance.competenciesControllerCreate(
    createCompetenciesDto
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **createCompetenciesDto** | **CreateCompetenciesDto**|  | |


### Return type

void (empty response body)

### Authorization

[bearer](../README.md#bearer)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: Not defined


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**201** |  |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **competenciesControllerFindAll**
> competenciesControllerFindAll()


### Example

```typescript
import {
    CompetenciesApi,
    Configuration
} from './api';

const configuration = new Configuration();
const apiInstance = new CompetenciesApi(configuration);

let limit: any; //Number of items per page (default to undefined)
let page: any; //Page number to retrieve (default to undefined)
let careerType: 'สมรรถนะหลัก' | 'สมรรถนะสายวิชาการ' | 'สมรรถนะสายสนับสนุนวิชการ' | 'สมรรถนะทางการบริหาร'; // (optional) (default to undefined)
let sortBy: any; //Field to sort by (e.g., id, name, email) (optional) (default to undefined)
let order: any; //Sort order (ASC or DESC) (optional) (default to undefined)
let search: any; //Search term to filter results (optional) (default to undefined)

const { status, data } = await apiInstance.competenciesControllerFindAll(
    limit,
    page,
    careerType,
    sortBy,
    order,
    search
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **limit** | **any** | Number of items per page | defaults to undefined|
| **page** | **any** | Page number to retrieve | defaults to undefined|
| **careerType** | [**&#39;สมรรถนะหลัก&#39; | &#39;สมรรถนะสายวิชาการ&#39; | &#39;สมรรถนะสายสนับสนุนวิชการ&#39; | &#39;สมรรถนะทางการบริหาร&#39;**]**Array<&#39;สมรรถนะหลัก&#39; &#124; &#39;สมรรถนะสายวิชาการ&#39; &#124; &#39;สมรรถนะสายสนับสนุนวิชการ&#39; &#124; &#39;สมรรถนะทางการบริหาร&#39;>** |  | (optional) defaults to undefined|
| **sortBy** | **any** | Field to sort by (e.g., id, name, email) | (optional) defaults to undefined|
| **order** | **any** | Sort order (ASC or DESC) | (optional) defaults to undefined|
| **search** | **any** | Search term to filter results | (optional) defaults to undefined|


### Return type

void (empty response body)

### Authorization

[bearer](../README.md#bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**200** |  |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **competenciesControllerFindByType**
> competenciesControllerFindByType()


### Example

```typescript
import {
    CompetenciesApi,
    Configuration
} from './api';

const configuration = new Configuration();
const apiInstance = new CompetenciesApi(configuration);

let careerType: string; // (default to undefined)
let id: any; //Competency ID (default to undefined)

const { status, data } = await apiInstance.competenciesControllerFindByType(
    careerType,
    id
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **careerType** | [**string**] |  | defaults to undefined|
| **id** | **any** | Competency ID | defaults to undefined|


### Return type

void (empty response body)

### Authorization

[bearer](../README.md#bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**200** |  |  -  |
|**404** | Competency not found |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **competenciesControllerFindOne**
> competenciesControllerFindOne()


### Example

```typescript
import {
    CompetenciesApi,
    Configuration
} from './api';

const configuration = new Configuration();
const apiInstance = new CompetenciesApi(configuration);

let id: string; //Competency ID (default to undefined)

const { status, data } = await apiInstance.competenciesControllerFindOne(
    id
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **id** | [**string**] | Competency ID | defaults to undefined|


### Return type

void (empty response body)

### Authorization

[bearer](../README.md#bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**200** |  |  -  |
|**404** | Competency not found |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **competenciesControllerRemove**
> competenciesControllerRemove()


### Example

```typescript
import {
    CompetenciesApi,
    Configuration
} from './api';

const configuration = new Configuration();
const apiInstance = new CompetenciesApi(configuration);

let id: string; //Competency ID (default to undefined)

const { status, data } = await apiInstance.competenciesControllerRemove(
    id
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **id** | [**string**] | Competency ID | defaults to undefined|


### Return type

void (empty response body)

### Authorization

[bearer](../README.md#bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**204** | Deleted successfully |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **competenciesControllerUpdate**
> competenciesControllerUpdate(body)


### Example

```typescript
import {
    CompetenciesApi,
    Configuration
} from './api';

const configuration = new Configuration();
const apiInstance = new CompetenciesApi(configuration);

let id: string; //Competency ID (default to undefined)
let body: object; //

const { status, data } = await apiInstance.competenciesControllerUpdate(
    id,
    body
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **body** | **object**|  | |
| **id** | [**string**] | Competency ID | defaults to undefined|


### Return type

void (empty response body)

### Authorization

[bearer](../README.md#bearer)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: Not defined


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**200** |  |  -  |
|**404** | Competency not found |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

