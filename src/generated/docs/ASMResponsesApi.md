# ASMResponsesApi

All URIs are relative to *http://localhost*

|Method | HTTP request | Description|
|------------- | ------------- | -------------|
|[**responsesControllerClear**](#responsescontrollerclear) | **DELETE** /responses/clear/{id} | ลบคำตอบ|
|[**responsesControllerCreate**](#responsescontrollercreate) | **POST** /responses | สร้างคำตอบใหม่|
|[**responsesControllerFindAll**](#responsescontrollerfindall) | **GET** /responses | ดึงข้อมูลคำตอบทั้งหมด|
|[**responsesControllerFindAllBySubmissionId**](#responsescontrollerfindallbysubmissionid) | **GET** /responses/by/submission/{submissionId} |  ข้อมูลคำตอบทั้งหมดของ submission ตาม ID|
|[**responsesControllerFindAnswer**](#responsescontrollerfindanswer) | **GET** /responses/{submissionId}/{questionId} | ดึงข้อมูลคำตอบตาม ID|
|[**responsesControllerFindCheckboxAnswers**](#responsescontrollerfindcheckboxanswers) | **GET** /responses/checkbox/{submissionId}/{questionId} | |
|[**responsesControllerFindOne**](#responsescontrollerfindone) | **GET** /responses/{id} | ดึงข้อมูลคำตอบตาม ID|
|[**responsesControllerFindRemoveCheckBoxAnswer**](#responsescontrollerfindremovecheckboxanswer) | **GET** /responses/{submissionId}/{questionId}/{selectedOptionId} | ดึงข้อมูลคำตอบตาม ID|
|[**responsesControllerRemove**](#responsescontrollerremove) | **DELETE** /responses/{id} | ลบคำตอบ|
|[**responsesControllerUpdate**](#responsescontrollerupdate) | **PATCH** /responses/{id} | แก้ไขคำตอบ|
|[**responsesControllerUserSaveQuizResponse**](#responsescontrollerusersavequizresponse) | **POST** /responses/quiz/save-response | |

# **responsesControllerClear**
> responsesControllerClear()


### Example

```typescript
import {
    ASMResponsesApi,
    Configuration
} from './api';

const configuration = new Configuration();
const apiInstance = new ASMResponsesApi(configuration);

let id: number; // (default to undefined)

const { status, data } = await apiInstance.responsesControllerClear(
    id
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **id** | [**number**] |  | defaults to undefined|


### Return type

void (empty response body)

### Authorization

[bearer](../README.md#bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**200** | ลบคำตอบสำเร็จ |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **responsesControllerCreate**
> any responsesControllerCreate(createResponseDto)


### Example

```typescript
import {
    ASMResponsesApi,
    Configuration,
    CreateResponseDto
} from './api';

const configuration = new Configuration();
const apiInstance = new ASMResponsesApi(configuration);

let createResponseDto: CreateResponseDto; //

const { status, data } = await apiInstance.responsesControllerCreate(
    createResponseDto
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **createResponseDto** | **CreateResponseDto**|  | |


### Return type

**any**

### Authorization

[bearer](../README.md#bearer)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**201** | สร้างคำตอบสำเร็จ |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **responsesControllerFindAll**
> responsesControllerFindAll()


### Example

```typescript
import {
    ASMResponsesApi,
    Configuration
} from './api';

const configuration = new Configuration();
const apiInstance = new ASMResponsesApi(configuration);

const { status, data } = await apiInstance.responsesControllerFindAll();
```

### Parameters
This endpoint does not have any parameters.


### Return type

void (empty response body)

### Authorization

[bearer](../README.md#bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**200** | ดึงข้อมูลสำเร็จ |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **responsesControllerFindAllBySubmissionId**
> responsesControllerFindAllBySubmissionId()


### Example

```typescript
import {
    ASMResponsesApi,
    Configuration
} from './api';

const configuration = new Configuration();
const apiInstance = new ASMResponsesApi(configuration);

let submissionId: string; // (default to undefined)

const { status, data } = await apiInstance.responsesControllerFindAllBySubmissionId(
    submissionId
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **submissionId** | [**string**] |  | defaults to undefined|


### Return type

void (empty response body)

### Authorization

[bearer](../README.md#bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**200** |  |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **responsesControllerFindAnswer**
> responsesControllerFindAnswer()


### Example

```typescript
import {
    ASMResponsesApi,
    Configuration
} from './api';

const configuration = new Configuration();
const apiInstance = new ASMResponsesApi(configuration);

let submissionId: number; // (default to undefined)
let questionId: number; // (default to undefined)

const { status, data } = await apiInstance.responsesControllerFindAnswer(
    submissionId,
    questionId
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **submissionId** | [**number**] |  | defaults to undefined|
| **questionId** | [**number**] |  | defaults to undefined|


### Return type

void (empty response body)

### Authorization

[bearer](../README.md#bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**200** | ดึงข้อมูลสำเร็จ |  -  |
|**404** | ไม่พบข้อมูลคำตอบ |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **responsesControllerFindCheckboxAnswers**
> responsesControllerFindCheckboxAnswers()


### Example

```typescript
import {
    ASMResponsesApi,
    Configuration
} from './api';

const configuration = new Configuration();
const apiInstance = new ASMResponsesApi(configuration);

let submissionId: number; // (default to undefined)
let questionId: number; // (default to undefined)

const { status, data } = await apiInstance.responsesControllerFindCheckboxAnswers(
    submissionId,
    questionId
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **submissionId** | [**number**] |  | defaults to undefined|
| **questionId** | [**number**] |  | defaults to undefined|


### Return type

void (empty response body)

### Authorization

[bearer](../README.md#bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**200** |  |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **responsesControllerFindOne**
> responsesControllerFindOne()


### Example

```typescript
import {
    ASMResponsesApi,
    Configuration
} from './api';

const configuration = new Configuration();
const apiInstance = new ASMResponsesApi(configuration);

let id: number; // (default to undefined)

const { status, data } = await apiInstance.responsesControllerFindOne(
    id
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **id** | [**number**] |  | defaults to undefined|


### Return type

void (empty response body)

### Authorization

[bearer](../README.md#bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**200** | ดึงข้อมูลสำเร็จ |  -  |
|**404** | ไม่พบข้อมูลคำตอบ |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **responsesControllerFindRemoveCheckBoxAnswer**
> responsesControllerFindRemoveCheckBoxAnswer()


### Example

```typescript
import {
    ASMResponsesApi,
    Configuration
} from './api';

const configuration = new Configuration();
const apiInstance = new ASMResponsesApi(configuration);

let submissionId: number; // (default to undefined)
let questionId: number; // (default to undefined)
let selectedOptionId: number; // (default to undefined)

const { status, data } = await apiInstance.responsesControllerFindRemoveCheckBoxAnswer(
    submissionId,
    questionId,
    selectedOptionId
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **submissionId** | [**number**] |  | defaults to undefined|
| **questionId** | [**number**] |  | defaults to undefined|
| **selectedOptionId** | [**number**] |  | defaults to undefined|


### Return type

void (empty response body)

### Authorization

[bearer](../README.md#bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**200** | ดึงข้อมูลสำเร็จ |  -  |
|**404** | ไม่พบข้อมูลคำตอบ |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **responsesControllerRemove**
> responsesControllerRemove()


### Example

```typescript
import {
    ASMResponsesApi,
    Configuration
} from './api';

const configuration = new Configuration();
const apiInstance = new ASMResponsesApi(configuration);

let id: number; // (default to undefined)

const { status, data } = await apiInstance.responsesControllerRemove(
    id
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **id** | [**number**] |  | defaults to undefined|


### Return type

void (empty response body)

### Authorization

[bearer](../README.md#bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**200** | ลบคำตอบสำเร็จ |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **responsesControllerUpdate**
> any responsesControllerUpdate(body)


### Example

```typescript
import {
    ASMResponsesApi,
    Configuration
} from './api';

const configuration = new Configuration();
const apiInstance = new ASMResponsesApi(configuration);

let id: number; // (default to undefined)
let body: object; //

const { status, data } = await apiInstance.responsesControllerUpdate(
    id,
    body
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **body** | **object**|  | |
| **id** | [**number**] |  | defaults to undefined|


### Return type

**any**

### Authorization

[bearer](../README.md#bearer)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**200** | แก้ไขคำตอบสำเร็จ |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **responsesControllerUserSaveQuizResponse**
> responsesControllerUserSaveQuizResponse(createResponseDto)


### Example

```typescript
import {
    ASMResponsesApi,
    Configuration,
    CreateResponseDto
} from './api';

const configuration = new Configuration();
const apiInstance = new ASMResponsesApi(configuration);

let createResponseDto: CreateResponseDto; //

const { status, data } = await apiInstance.responsesControllerUserSaveQuizResponse(
    createResponseDto
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **createResponseDto** | **CreateResponseDto**|  | |


### Return type

void (empty response body)

### Authorization

[bearer](../README.md#bearer)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: Not defined


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**201** |  |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

