# EvaluateSettingsApi

All URIs are relative to *http://localhost*

|Method | HTTP request | Description|
|------------- | ------------- | -------------|
|[**evaluateSettingsControllerGetEvaluateSettings**](#evaluatesettingscontrollergetevaluatesettings) | **GET** /assessments/{id}/evaluate-settings | Get evaluate settings|
|[**evaluateSettingsControllerUpdateEvaluateSettings**](#evaluatesettingscontrollerupdateevaluatesettings) | **PATCH** /assessments/{id}/evaluate-settings | Update evaluate settings|

# **evaluateSettingsControllerGetEvaluateSettings**
> object evaluateSettingsControllerGetEvaluateSettings()

ดึงการตั้งค่าของแบบประเมิน

### Example

```typescript
import {
    EvaluateSettingsApi,
    Configuration
} from './api';

const configuration = new Configuration();
const apiInstance = new EvaluateSettingsApi(configuration);

let id: number; //รหัสของแบบประเมิน (default to undefined)

const { status, data } = await apiInstance.evaluateSettingsControllerGetEvaluateSettings(
    id
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **id** | [**number**] | รหัสของแบบประเมิน | defaults to undefined|


### Return type

**object**

### Authorization

[bearer](../README.md#bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**200** | ข้อมูลการตั้งค่าแบบประเมิน |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **evaluateSettingsControllerUpdateEvaluateSettings**
> object evaluateSettingsControllerUpdateEvaluateSettings(updateEvaluateSettingsDto)

อัปเดตการตั้งค่าของแบบประเมิน (เฉพาะฟิลด์ที่เกี่ยวข้องกับการตั้งค่า)

### Example

```typescript
import {
    EvaluateSettingsApi,
    Configuration,
    UpdateEvaluateSettingsDto
} from './api';

const configuration = new Configuration();
const apiInstance = new EvaluateSettingsApi(configuration);

let id: number; //รหัสของแบบประเมิน (default to undefined)
let updateEvaluateSettingsDto: UpdateEvaluateSettingsDto; //

const { status, data } = await apiInstance.evaluateSettingsControllerUpdateEvaluateSettings(
    id,
    updateEvaluateSettingsDto
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **updateEvaluateSettingsDto** | **UpdateEvaluateSettingsDto**|  | |
| **id** | [**number**] | รหัสของแบบประเมิน | defaults to undefined|


### Return type

**object**

### Authorization

[bearer](../README.md#bearer)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**200** | การตั้งค่าแบบประเมินถูกอัปเดตเรียบร้อยแล้ว |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

