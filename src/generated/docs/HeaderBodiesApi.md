# HeaderBodiesApi

All URIs are relative to *http://localhost*

|Method | HTTP request | Description|
|------------- | ------------- | -------------|
|[**headerBodiesControllerCreate**](#headerbodiescontrollercreate) | **POST** /header-bodies | สร้าง Header Bodyใหม่|
|[**headerBodiesControllerFindAll**](#headerbodiescontrollerfindall) | **GET** /header-bodies | |
|[**headerBodiesControllerFindOne**](#headerbodiescontrollerfindone) | **GET** /header-bodies/{id} | |
|[**headerBodiesControllerRemove**](#headerbodiescontrollerremove) | **DELETE** /header-bodies/{id} | |
|[**headerBodiesControllerUpdate**](#headerbodiescontrollerupdate) | **PATCH** /header-bodies/{id} | อัปเดตHeader Body|

# **headerBodiesControllerCreate**
> headerBodiesControllerCreate()

สร้าง Header Body ใหม่สำหรับ (Evaluate)

### Example

```typescript
import {
    HeaderBodiesApi,
    Configuration
} from './api';

const configuration = new Configuration();
const apiInstance = new HeaderBodiesApi(configuration);

let itemBlockId: number; // (default to undefined)
let title: string; // (optional) (default to undefined)
let description: string; // (optional) (default to undefined)

const { status, data } = await apiInstance.headerBodiesControllerCreate(
    itemBlockId,
    title,
    description
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **itemBlockId** | [**number**] |  | defaults to undefined|
| **title** | [**string**] |  | (optional) defaults to undefined|
| **description** | [**string**] |  | (optional) defaults to undefined|


### Return type

void (empty response body)

### Authorization

[bearer](../README.md#bearer)

### HTTP request headers

 - **Content-Type**: multipart/form-data
 - **Accept**: Not defined


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**201** |  |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **headerBodiesControllerFindAll**
> headerBodiesControllerFindAll()


### Example

```typescript
import {
    HeaderBodiesApi,
    Configuration
} from './api';

const configuration = new Configuration();
const apiInstance = new HeaderBodiesApi(configuration);

const { status, data } = await apiInstance.headerBodiesControllerFindAll();
```

### Parameters
This endpoint does not have any parameters.


### Return type

void (empty response body)

### Authorization

[bearer](../README.md#bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**200** |  |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **headerBodiesControllerFindOne**
> headerBodiesControllerFindOne()


### Example

```typescript
import {
    HeaderBodiesApi,
    Configuration
} from './api';

const configuration = new Configuration();
const apiInstance = new HeaderBodiesApi(configuration);

let id: string; // (default to undefined)

const { status, data } = await apiInstance.headerBodiesControllerFindOne(
    id
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **id** | [**string**] |  | defaults to undefined|


### Return type

void (empty response body)

### Authorization

[bearer](../README.md#bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**200** |  |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **headerBodiesControllerRemove**
> headerBodiesControllerRemove()


### Example

```typescript
import {
    HeaderBodiesApi,
    Configuration
} from './api';

const configuration = new Configuration();
const apiInstance = new HeaderBodiesApi(configuration);

let id: string; // (default to undefined)

const { status, data } = await apiInstance.headerBodiesControllerRemove(
    id
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **id** | [**string**] |  | defaults to undefined|


### Return type

void (empty response body)

### Authorization

[bearer](../README.md#bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**200** |  |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **headerBodiesControllerUpdate**
> headerBodiesControllerUpdate()

อัปเดตHeader Body (Evaluate) ตาม template ที่กำหนด

### Example

```typescript
import {
    HeaderBodiesApi,
    Configuration
} from './api';

const configuration = new Configuration();
const apiInstance = new HeaderBodiesApi(configuration);

let id: string; // (default to undefined)
let title: string; //The title of the header section (optional) (default to undefined)
let description: string; //Detailed description or instructions for this section (optional) (default to undefined)
let itemBlockId: number; //ID of the item block this header belongs to (optional) (default to undefined)

const { status, data } = await apiInstance.headerBodiesControllerUpdate(
    id,
    title,
    description,
    itemBlockId
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **id** | [**string**] |  | defaults to undefined|
| **title** | [**string**] | The title of the header section | (optional) defaults to undefined|
| **description** | [**string**] | Detailed description or instructions for this section | (optional) defaults to undefined|
| **itemBlockId** | [**number**] | ID of the item block this header belongs to | (optional) defaults to undefined|


### Return type

void (empty response body)

### Authorization

[bearer](../README.md#bearer)

### HTTP request headers

 - **Content-Type**: multipart/form-data
 - **Accept**: Not defined


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**200** |  |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

