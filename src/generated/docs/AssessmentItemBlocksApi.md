# AssessmentItemBlocksApi

All URIs are relative to *http://localhost*

|Method | HTTP request | Description|
|------------- | ------------- | -------------|
|[**itemBlocksControllerCreate**](#itemblockscontrollercreate) | **POST** /item-blocks/block | |
|[**itemBlocksControllerDuplicateBlock**](#itemblockscontrollerduplicateblock) | **POST** /item-blocks/{sourceId}/duplicate | Duplicate an existing Item Block|
|[**itemBlocksControllerFindItemOne**](#itemblockscontrollerfinditemone) | **GET** /item-blocks/{id} | |
|[**itemBlocksControllerFindOne**](#itemblockscontrollerfindone) | **GET** /item-blocks/{assessmentId}/block | |
|[**itemBlocksControllerRemove**](#itemblockscontrollerremove) | **DELETE** /item-blocks/{id} | |
|[**itemBlocksControllerSequenceQuestion**](#itemblockscontrollersequencequestion) | **GET** /item-blocks/quiz/sequence/{submissionId}/{sequence} | |
|[**itemBlocksControllerUpdate**](#itemblockscontrollerupdate) | **PATCH** /item-blocks/{id} | อัปเดตItem Block|
|[**itemBlocksControllerUpdateSequences**](#itemblockscontrollerupdatesequences) | **PATCH** /item-blocks/update/sequences | |

# **itemBlocksControllerCreate**
> itemBlocksControllerCreate()


### Example

```typescript
import {
    AssessmentItemBlocksApi,
    Configuration
} from './api';

const configuration = new Configuration();
const apiInstance = new AssessmentItemBlocksApi(configuration);

const { status, data } = await apiInstance.itemBlocksControllerCreate();
```

### Parameters
This endpoint does not have any parameters.


### Return type

void (empty response body)

### Authorization

[bearer](../README.md#bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**201** |  |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **itemBlocksControllerDuplicateBlock**
> itemBlocksControllerDuplicateBlock()

Creates a complete copy of an existing Item Block with all its content (atomic operation)

### Example

```typescript
import {
    AssessmentItemBlocksApi,
    Configuration
} from './api';

const configuration = new Configuration();
const apiInstance = new AssessmentItemBlocksApi(configuration);

let sourceId: number; // (default to undefined)
let assessmentId: number; // (default to undefined)
let sequence: number; // (optional) (default to undefined)
let section: number; // (optional) (default to undefined)

const { status, data } = await apiInstance.itemBlocksControllerDuplicateBlock(
    sourceId,
    assessmentId,
    sequence,
    section
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **sourceId** | [**number**] |  | defaults to undefined|
| **assessmentId** | [**number**] |  | defaults to undefined|
| **sequence** | [**number**] |  | (optional) defaults to undefined|
| **section** | [**number**] |  | (optional) defaults to undefined|


### Return type

void (empty response body)

### Authorization

[bearer](../README.md#bearer)

### HTTP request headers

 - **Content-Type**: multipart/form-data
 - **Accept**: Not defined


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**201** |  |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **itemBlocksControllerFindItemOne**
> itemBlocksControllerFindItemOne()


### Example

```typescript
import {
    AssessmentItemBlocksApi,
    Configuration
} from './api';

const configuration = new Configuration();
const apiInstance = new AssessmentItemBlocksApi(configuration);

let id: number; // (default to undefined)

const { status, data } = await apiInstance.itemBlocksControllerFindItemOne(
    id
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **id** | [**number**] |  | defaults to undefined|


### Return type

void (empty response body)

### Authorization

[bearer](../README.md#bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**200** |  |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **itemBlocksControllerFindOne**
> itemBlocksControllerFindOne()


### Example

```typescript
import {
    AssessmentItemBlocksApi,
    Configuration
} from './api';

const configuration = new Configuration();
const apiInstance = new AssessmentItemBlocksApi(configuration);

let assessmentId: number; // (default to undefined)

const { status, data } = await apiInstance.itemBlocksControllerFindOne(
    assessmentId
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **assessmentId** | [**number**] |  | defaults to undefined|


### Return type

void (empty response body)

### Authorization

[bearer](../README.md#bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**200** |  |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **itemBlocksControllerRemove**
> itemBlocksControllerRemove()


### Example

```typescript
import {
    AssessmentItemBlocksApi,
    Configuration
} from './api';

const configuration = new Configuration();
const apiInstance = new AssessmentItemBlocksApi(configuration);

let id: number; // (default to undefined)

const { status, data } = await apiInstance.itemBlocksControllerRemove(
    id
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **id** | [**number**] |  | defaults to undefined|


### Return type

void (empty response body)

### Authorization

[bearer](../README.md#bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**200** |  |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **itemBlocksControllerSequenceQuestion**
> itemBlocksControllerSequenceQuestion()


### Example

```typescript
import {
    AssessmentItemBlocksApi,
    Configuration
} from './api';

const configuration = new Configuration();
const apiInstance = new AssessmentItemBlocksApi(configuration);

let submissionId: number; // (default to undefined)
let sequence: number; // (default to undefined)

const { status, data } = await apiInstance.itemBlocksControllerSequenceQuestion(
    submissionId,
    sequence
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **submissionId** | [**number**] |  | defaults to undefined|
| **sequence** | [**number**] |  | defaults to undefined|


### Return type

void (empty response body)

### Authorization

[bearer](../README.md#bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**200** |  |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **itemBlocksControllerUpdate**
> itemBlocksControllerUpdate()

อัปเดตItem Block (Evaluate) ตาม template ที่กำหนด

### Example

```typescript
import {
    AssessmentItemBlocksApi,
    Configuration
} from './api';

const configuration = new Configuration();
const apiInstance = new AssessmentItemBlocksApi(configuration);

let id: string; // (default to undefined)
let sequence: number; //The order/position of this item block in the assessment (optional) (default to undefined)
let section: number; //The section number this item block belongs to (optional) (default to undefined)
let type: ItemBlockType; //The type of item block (RADIO, CHECKBOX, TEXTFIELD, etc.) (optional) (default to undefined)
let isRequired: boolean; //Whether this item block is required to be answered (optional) (default to false)
let assessmentId: number; //ID of the assessment this item block belongs to (optional) (default to undefined)

const { status, data } = await apiInstance.itemBlocksControllerUpdate(
    id,
    sequence,
    section,
    type,
    isRequired,
    assessmentId
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **id** | [**string**] |  | defaults to undefined|
| **sequence** | [**number**] | The order/position of this item block in the assessment | (optional) defaults to undefined|
| **section** | [**number**] | The section number this item block belongs to | (optional) defaults to undefined|
| **type** | **ItemBlockType** | The type of item block (RADIO, CHECKBOX, TEXTFIELD, etc.) | (optional) defaults to undefined|
| **isRequired** | [**boolean**] | Whether this item block is required to be answered | (optional) defaults to false|
| **assessmentId** | [**number**] | ID of the assessment this item block belongs to | (optional) defaults to undefined|


### Return type

void (empty response body)

### Authorization

[bearer](../README.md#bearer)

### HTTP request headers

 - **Content-Type**: multipart/form-data
 - **Accept**: Not defined


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**200** |  |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **itemBlocksControllerUpdateSequences**
> itemBlocksControllerUpdateSequences(bulkUpdateItemBlockSequencesDto)


### Example

```typescript
import {
    AssessmentItemBlocksApi,
    Configuration,
    BulkUpdateItemBlockSequencesDto
} from './api';

const configuration = new Configuration();
const apiInstance = new AssessmentItemBlocksApi(configuration);

let bulkUpdateItemBlockSequencesDto: BulkUpdateItemBlockSequencesDto; //

const { status, data } = await apiInstance.itemBlocksControllerUpdateSequences(
    bulkUpdateItemBlockSequencesDto
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **bulkUpdateItemBlockSequencesDto** | **BulkUpdateItemBlockSequencesDto**|  | |


### Return type

void (empty response body)

### Authorization

[bearer](../README.md#bearer)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: Not defined


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**200** |  |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

