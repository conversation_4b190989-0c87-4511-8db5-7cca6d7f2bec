# DevelopmentPlanResponseDto


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**data** | **Array&lt;string&gt;** | List of items | [default to undefined]
**meta** | [**PaginationMetaDto**](PaginationMetaDto.md) | Pagination metadata | [default to undefined]

## Example

```typescript
import { DevelopmentPlanResponseDto } from './api';

const instance: DevelopmentPlanResponseDto = {
    data,
    meta,
};
```

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)
