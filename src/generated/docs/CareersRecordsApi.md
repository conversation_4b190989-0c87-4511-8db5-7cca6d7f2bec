# CareersRecordsApi

All URIs are relative to *http://localhost*

|Method | HTTP request | Description|
|------------- | ------------- | -------------|
|[**careersRecordsControllerCreate**](#careersrecordscontrollercreate) | **POST** /career-records | |
|[**careersRecordsControllerFindAll**](#careersrecordscontrollerfindall) | **GET** /career-records | |
|[**careersRecordsControllerFindOne**](#careersrecordscontrollerfindone) | **GET** /career-records/{id} | |
|[**careersRecordsControllerRemove**](#careersrecordscontrollerremove) | **DELETE** /career-records/{id} | |
|[**careersRecordsControllerUpdate**](#careersrecordscontrollerupdate) | **PUT** /career-records/{id} | |

# **careersRecordsControllerCreate**
> careersRecordsControllerCreate()


### Example

```typescript
import {
    CareersRecordsApi,
    Configuration
} from './api';

const configuration = new Configuration();
const apiInstance = new CareersRecordsApi(configuration);

const { status, data } = await apiInstance.careersRecordsControllerCreate();
```

### Parameters
This endpoint does not have any parameters.


### Return type

void (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**201** |  |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **careersRecordsControllerFindAll**
> careersRecordsControllerFindAll()


### Example

```typescript
import {
    CareersRecordsApi,
    Configuration
} from './api';

const configuration = new Configuration();
const apiInstance = new CareersRecordsApi(configuration);

const { status, data } = await apiInstance.careersRecordsControllerFindAll();
```

### Parameters
This endpoint does not have any parameters.


### Return type

void (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**200** |  |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **careersRecordsControllerFindOne**
> careersRecordsControllerFindOne()


### Example

```typescript
import {
    CareersRecordsApi,
    Configuration
} from './api';

const configuration = new Configuration();
const apiInstance = new CareersRecordsApi(configuration);

let id: string; // (default to undefined)

const { status, data } = await apiInstance.careersRecordsControllerFindOne(
    id
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **id** | [**string**] |  | defaults to undefined|


### Return type

void (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**200** |  |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **careersRecordsControllerRemove**
> careersRecordsControllerRemove()


### Example

```typescript
import {
    CareersRecordsApi,
    Configuration
} from './api';

const configuration = new Configuration();
const apiInstance = new CareersRecordsApi(configuration);

let id: string; // (default to undefined)

const { status, data } = await apiInstance.careersRecordsControllerRemove(
    id
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **id** | [**string**] |  | defaults to undefined|


### Return type

void (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**200** |  |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **careersRecordsControllerUpdate**
> careersRecordsControllerUpdate()


### Example

```typescript
import {
    CareersRecordsApi,
    Configuration
} from './api';

const configuration = new Configuration();
const apiInstance = new CareersRecordsApi(configuration);

let id: string; // (default to undefined)

const { status, data } = await apiInstance.careersRecordsControllerUpdate(
    id
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **id** | [**string**] |  | defaults to undefined|


### Return type

void (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**200** |  |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

