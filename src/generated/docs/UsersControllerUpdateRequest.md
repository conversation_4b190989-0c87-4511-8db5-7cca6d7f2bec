# UsersControllerUpdateRequest


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**name** | **string** |  | [optional] [default to undefined]
**email** | **string** |  | [optional] [default to undefined]
**currentPassword** | **string** |  | [optional] [default to undefined]
**newPassword** | **string** |  | [optional] [default to undefined]
**roleIds** | **Array&lt;number&gt;** |  | [optional] [default to undefined]

## Example

```typescript
import { UsersControllerUpdateRequest } from './api';

const instance: UsersControllerUpdateRequest = {
    name,
    email,
    currentPassword,
    newPassword,
    roleIds,
};
```

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)
