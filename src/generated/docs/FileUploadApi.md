# FileUploadApi

All URIs are relative to *http://localhost*

|Method | HTTP request | Description|
|------------- | ------------- | -------------|
|[**fileUploadControllerGetPublicFile**](#fileuploadcontrollergetpublicfile) | **POST** /file-upload/public-file-path | |

# **fileUploadControllerGetPublicFile**
> fileUploadControllerGetPublicFile(body)


### Example

```typescript
import {
    FileUploadApi,
    Configuration
} from './api';

const configuration = new Configuration();
const apiInstance = new FileUploadApi(configuration);

let body: object; //

const { status, data } = await apiInstance.fileUploadControllerGetPublicFile(
    body
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **body** | **object**|  | |


### Return type

void (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: Not defined


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**201** |  |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

