# QuestionsApi

All URIs are relative to *http://localhost*

|Method | HTTP request | Description|
|------------- | ------------- | -------------|
|[**questionsControllerCreate**](#questionscontrollercreate) | **POST** /questions | สร้างคำถามใหม่|
|[**questionsControllerFindAll**](#questionscontrollerfindall) | **GET** /questions | |
|[**questionsControllerFindOne**](#questionscontrollerfindone) | **GET** /questions/{id} | |
|[**questionsControllerRemove**](#questionscontrollerremove) | **DELETE** /questions/{id} | |
|[**questionsControllerUpdate**](#questionscontrollerupdate) | **PATCH** /questions/{id} | อัปเดตคำถาม|

# **questionsControllerCreate**
> questionsControllerCreate()

สร้างคำถาม (Evaluate) ใหม่ตาม template ที่กำหนด

### Example

```typescript
import {
    QuestionsApi,
    Configuration
} from './api';

const configuration = new Configuration();
const apiInstance = new QuestionsApi(configuration);

let questionText: string; //The text content of the question (default to undefined)
let sequence: number; //The order/position of this question in the assessment (default to undefined)
let imagePath: File; //Image file for the question (optional) (default to undefined)
let isHeader: boolean; //Whether this question serves as a header/section title (optional) (default to false)
let sizeLimit: number; //Maximum file size limit in bytes for file upload questions (optional) (default to undefined)
let acceptFile: string; //Accepted file types for file upload questions (comma-separated MIME types or extensions) (optional) (default to undefined)
let uploadLimit: number; //Maximum number of files that can be uploaded for this question (optional) (default to undefined)
let itemBlockId: number; //ID of the item block this question belongs to (optional) (default to undefined)
let score: number; //Score for the question (optional) (default to undefined)
let imageWidth: number; //Width of the image in pixels (optional) (default to undefined)
let imageHeight: number; //Height of the image in pixels (optional) (default to undefined)

const { status, data } = await apiInstance.questionsControllerCreate(
    questionText,
    sequence,
    imagePath,
    isHeader,
    sizeLimit,
    acceptFile,
    uploadLimit,
    itemBlockId,
    score,
    imageWidth,
    imageHeight
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **questionText** | [**string**] | The text content of the question | defaults to undefined|
| **sequence** | [**number**] | The order/position of this question in the assessment | defaults to undefined|
| **imagePath** | [**File**] | Image file for the question | (optional) defaults to undefined|
| **isHeader** | [**boolean**] | Whether this question serves as a header/section title | (optional) defaults to false|
| **sizeLimit** | [**number**] | Maximum file size limit in bytes for file upload questions | (optional) defaults to undefined|
| **acceptFile** | [**string**] | Accepted file types for file upload questions (comma-separated MIME types or extensions) | (optional) defaults to undefined|
| **uploadLimit** | [**number**] | Maximum number of files that can be uploaded for this question | (optional) defaults to undefined|
| **itemBlockId** | [**number**] | ID of the item block this question belongs to | (optional) defaults to undefined|
| **score** | [**number**] | Score for the question | (optional) defaults to undefined|
| **imageWidth** | [**number**] | Width of the image in pixels | (optional) defaults to undefined|
| **imageHeight** | [**number**] | Height of the image in pixels | (optional) defaults to undefined|


### Return type

void (empty response body)

### Authorization

[bearer](../README.md#bearer)

### HTTP request headers

 - **Content-Type**: multipart/form-data
 - **Accept**: Not defined


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**201** |  |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **questionsControllerFindAll**
> questionsControllerFindAll()


### Example

```typescript
import {
    QuestionsApi,
    Configuration
} from './api';

const configuration = new Configuration();
const apiInstance = new QuestionsApi(configuration);

const { status, data } = await apiInstance.questionsControllerFindAll();
```

### Parameters
This endpoint does not have any parameters.


### Return type

void (empty response body)

### Authorization

[bearer](../README.md#bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**200** |  |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **questionsControllerFindOne**
> questionsControllerFindOne()


### Example

```typescript
import {
    QuestionsApi,
    Configuration
} from './api';

const configuration = new Configuration();
const apiInstance = new QuestionsApi(configuration);

let id: string; // (default to undefined)

const { status, data } = await apiInstance.questionsControllerFindOne(
    id
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **id** | [**string**] |  | defaults to undefined|


### Return type

void (empty response body)

### Authorization

[bearer](../README.md#bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**200** |  |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **questionsControllerRemove**
> questionsControllerRemove()


### Example

```typescript
import {
    QuestionsApi,
    Configuration
} from './api';

const configuration = new Configuration();
const apiInstance = new QuestionsApi(configuration);

let id: string; // (default to undefined)

const { status, data } = await apiInstance.questionsControllerRemove(
    id
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **id** | [**string**] |  | defaults to undefined|


### Return type

void (empty response body)

### Authorization

[bearer](../README.md#bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**200** |  |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **questionsControllerUpdate**
> questionsControllerUpdate()

อัปเดตคำถาม (Evaluate) ตาม template ที่กำหนด

### Example

```typescript
import {
    QuestionsApi,
    Configuration
} from './api';

const configuration = new Configuration();
const apiInstance = new QuestionsApi(configuration);

let id: string; // (default to undefined)
let questionText: string; //The text content of the question (optional) (default to undefined)
let imagePath: File; //Image file for the question (optional) (default to undefined)
let isHeader: boolean; //Whether this question serves as a header/section title (optional) (default to false)
let sequence: number; //The order/position of this question in the assessment (optional) (default to undefined)
let sizeLimit: number; //Maximum file size limit in bytes for file upload questions (optional) (default to undefined)
let acceptFile: string; //Accepted file types for file upload questions (comma-separated MIME types or extensions) (optional) (default to undefined)
let uploadLimit: number; //Maximum number of files that can be uploaded for this question (optional) (default to undefined)
let itemBlockId: number; //ID of the item block this question belongs to (optional) (default to undefined)
let score: number; //Score for the question (optional) (default to undefined)
let imageWidth: number; //Width of the image in pixels (optional) (default to undefined)
let imageHeight: number; //Height of the image in pixels (optional) (default to undefined)

const { status, data } = await apiInstance.questionsControllerUpdate(
    id,
    questionText,
    imagePath,
    isHeader,
    sequence,
    sizeLimit,
    acceptFile,
    uploadLimit,
    itemBlockId,
    score,
    imageWidth,
    imageHeight
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **id** | [**string**] |  | defaults to undefined|
| **questionText** | [**string**] | The text content of the question | (optional) defaults to undefined|
| **imagePath** | [**File**] | Image file for the question | (optional) defaults to undefined|
| **isHeader** | [**boolean**] | Whether this question serves as a header/section title | (optional) defaults to false|
| **sequence** | [**number**] | The order/position of this question in the assessment | (optional) defaults to undefined|
| **sizeLimit** | [**number**] | Maximum file size limit in bytes for file upload questions | (optional) defaults to undefined|
| **acceptFile** | [**string**] | Accepted file types for file upload questions (comma-separated MIME types or extensions) | (optional) defaults to undefined|
| **uploadLimit** | [**number**] | Maximum number of files that can be uploaded for this question | (optional) defaults to undefined|
| **itemBlockId** | [**number**] | ID of the item block this question belongs to | (optional) defaults to undefined|
| **score** | [**number**] | Score for the question | (optional) defaults to undefined|
| **imageWidth** | [**number**] | Width of the image in pixels | (optional) defaults to undefined|
| **imageHeight** | [**number**] | Height of the image in pixels | (optional) defaults to undefined|


### Return type

void (empty response body)

### Authorization

[bearer](../README.md#bearer)

### HTTP request headers

 - **Content-Type**: multipart/form-data
 - **Accept**: Not defined


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**200** |  |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

