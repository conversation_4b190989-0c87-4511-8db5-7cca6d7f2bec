# UpdateDevelopmentPlanDto


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**name** | **string** |  | [optional] [default to undefined]
**description** | **string** |  | [optional] [default to undefined]
**isActive** | **boolean** |  | [optional] [default to undefined]
**facId** | **number** |  | [optional] [default to undefined]
**ageWorkCriteriaId** | **number** |  | [optional] [default to undefined]

## Example

```typescript
import { UpdateDevelopmentPlanDto } from './api';

const instance: UpdateDevelopmentPlanDto = {
    name,
    description,
    isActive,
    facId,
    ageWorkCriteriaId,
};
```

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)
