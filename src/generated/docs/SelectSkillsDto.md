# SelectSkillsDto


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**typeId** | **number** |  | [default to undefined]
**skillIds** | **Array&lt;string&gt;** |  | [default to undefined]

## Example

```typescript
import { SelectSkillsDto } from './api';

const instance: SelectSkillsDto = {
    typeId,
    skillIds,
};
```

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)
