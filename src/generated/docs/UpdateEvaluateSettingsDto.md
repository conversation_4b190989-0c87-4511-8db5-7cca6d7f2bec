# UpdateEvaluateSettingsDto


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**startAt** | **string** | วันที่เริ่มต้นการประเมิน | [optional] [default to undefined]
**endAt** | **string** | วันที่สิ้นสุดการประเมิน | [optional] [default to undefined]
**responseEdit** | **boolean** | อนุญาตให้แก้ไขคำตอบได้หรือไม่ | [optional] [default to undefined]
**submitLimit** | **number** | จำนวนครั้งที่จำกัดการส่งคำตอบ (-1 หมายถึงไม่จำกัด) | [optional] [default to undefined]
**isPrototype** | **boolean** | กำหนดให้แบบประเมินนี้เป็นต้นแบบสำหรับการคัดลอก | [optional] [default to undefined]

## Example

```typescript
import { UpdateEvaluateSettingsDto } from './api';

const instance: UpdateEvaluateSettingsDto = {
    startAt,
    endAt,
    responseEdit,
    submitLimit,
    isPrototype,
};
```

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)
