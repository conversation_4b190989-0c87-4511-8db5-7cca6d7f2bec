# CreateCompetenciesDto


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**career_type** | **string** |  | [default to undefined]
**name** | **string** |  | [default to undefined]
**description** | **string** |  | [default to undefined]
**skillIds** | **Array&lt;string&gt;** |  | [optional] [default to undefined]

## Example

```typescript
import { CreateCompetenciesDto } from './api';

const instance: CreateCompetenciesDto = {
    career_type,
    name,
    description,
    skillIds,
};
```

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)
