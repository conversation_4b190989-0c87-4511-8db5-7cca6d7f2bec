# DevelopmentPlansApi

All URIs are relative to *http://localhost*

|Method | HTTP request | Description|
|------------- | ------------- | -------------|
|[**developmentPlansControllerCreate**](#developmentplanscontrollercreate) | **POST** /development-plans | Create a new development plan|
|[**developmentPlansControllerFindAll**](#developmentplanscontrollerfindall) | **GET** /development-plans | Get all development plans with pagination|
|[**developmentPlansControllerFindOne**](#developmentplanscontrollerfindone) | **GET** /development-plans/{id} | Get development plan by ID|
|[**developmentPlansControllerGetPersonalPlans**](#developmentplanscontrollergetpersonalplans) | **GET** /development-plans/personal-plans | Get personal development plans with filters|
|[**developmentPlansControllerRemove**](#developmentplanscontrollerremove) | **DELETE** /development-plans/{id} | Delete a development plan|
|[**developmentPlansControllerUpdate**](#developmentplanscontrollerupdate) | **PATCH** /development-plans/{id} | Update a development plan|

# **developmentPlansControllerCreate**
> developmentPlansControllerCreate(createDevelopmentPlanDto)


### Example

```typescript
import {
    DevelopmentPlansApi,
    Configuration,
    CreateDevelopmentPlanDto
} from './api';

const configuration = new Configuration();
const apiInstance = new DevelopmentPlansApi(configuration);

let createDevelopmentPlanDto: CreateDevelopmentPlanDto; //

const { status, data } = await apiInstance.developmentPlansControllerCreate(
    createDevelopmentPlanDto
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **createDevelopmentPlanDto** | **CreateDevelopmentPlanDto**|  | |


### Return type

void (empty response body)

### Authorization

[bearer](../README.md#bearer)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: Not defined


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**201** | The development plan has been successfully created. |  -  |
|**400** | Bad request. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **developmentPlansControllerFindAll**
> DevelopmentPlanResponseDto developmentPlansControllerFindAll()


### Example

```typescript
import {
    DevelopmentPlansApi,
    Configuration
} from './api';

const configuration = new Configuration();
const apiInstance = new DevelopmentPlansApi(configuration);

let limit: number; //Number of items per page (optional) (default to 10)
let page: number; //Page number (optional) (default to 1)
let sortBy: string; //Field to sort by (optional) (default to 'id')
let order: 'ASC' | 'DESC'; //Sort order (optional) (default to 'ASC')
let careerType: string; //Filter by career type (optional) (default to undefined)
let search: string; //Search term for name or description (optional) (default to undefined)
let isCentral: string; //Filter by isCentral (optional) (default to undefined)
let parentId: number; //Filter by parent ID (optional) (default to undefined)

const { status, data } = await apiInstance.developmentPlansControllerFindAll(
    limit,
    page,
    sortBy,
    order,
    careerType,
    search,
    isCentral,
    parentId
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **limit** | [**number**] | Number of items per page | (optional) defaults to 10|
| **page** | [**number**] | Page number | (optional) defaults to 1|
| **sortBy** | [**string**] | Field to sort by | (optional) defaults to 'id'|
| **order** | [**&#39;ASC&#39; | &#39;DESC&#39;**]**Array<&#39;ASC&#39; &#124; &#39;DESC&#39;>** | Sort order | (optional) defaults to 'ASC'|
| **careerType** | [**string**] | Filter by career type | (optional) defaults to undefined|
| **search** | [**string**] | Search term for name or description | (optional) defaults to undefined|
| **isCentral** | [**string**] | Filter by isCentral | (optional) defaults to undefined|
| **parentId** | [**number**] | Filter by parent ID | (optional) defaults to undefined|


### Return type

**DevelopmentPlanResponseDto**

### Authorization

[bearer](../README.md#bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**200** | Returns paginated list of development plans |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **developmentPlansControllerFindOne**
> developmentPlansControllerFindOne()


### Example

```typescript
import {
    DevelopmentPlansApi,
    Configuration
} from './api';

const configuration = new Configuration();
const apiInstance = new DevelopmentPlansApi(configuration);

let id: string; // (default to undefined)
let isCentral: boolean; // (default to undefined)

const { status, data } = await apiInstance.developmentPlansControllerFindOne(
    id,
    isCentral
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **id** | [**string**] |  | defaults to undefined|
| **isCentral** | [**boolean**] |  | defaults to undefined|


### Return type

void (empty response body)

### Authorization

[bearer](../README.md#bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**200** | Returns the development plan |  -  |
|**404** | Development plan not found |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **developmentPlansControllerGetPersonalPlans**
> PersonalPlansResponseDto developmentPlansControllerGetPersonalPlans()

Retrieve personal development plans with pagination and filtering by career, faculty, and role

### Example

```typescript
import {
    DevelopmentPlansApi,
    Configuration
} from './api';

const configuration = new Configuration();
const apiInstance = new DevelopmentPlansApi(configuration);

let page: number; //Page number (optional) (default to 1)
let limit: number; //Number of items per page (optional) (default to 10)
let search: string; //Search term (optional) (default to undefined)
let sortBy: string; //Sort by field (optional) (default to 'id')
let order: 'ASC' | 'DESC'; //Sort order (optional) (default to 'ASC')
let careerFilter: number; //Filter by career ID (optional) (default to undefined)
let facultyFilter: number; //Filter by faculty ID (optional) (default to undefined)
let roleFilter: number; //Filter by role ID (optional) (default to undefined)

const { status, data } = await apiInstance.developmentPlansControllerGetPersonalPlans(
    page,
    limit,
    search,
    sortBy,
    order,
    careerFilter,
    facultyFilter,
    roleFilter
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **page** | [**number**] | Page number | (optional) defaults to 1|
| **limit** | [**number**] | Number of items per page | (optional) defaults to 10|
| **search** | [**string**] | Search term | (optional) defaults to undefined|
| **sortBy** | [**string**] | Sort by field | (optional) defaults to 'id'|
| **order** | [**&#39;ASC&#39; | &#39;DESC&#39;**]**Array<&#39;ASC&#39; &#124; &#39;DESC&#39;>** | Sort order | (optional) defaults to 'ASC'|
| **careerFilter** | [**number**] | Filter by career ID | (optional) defaults to undefined|
| **facultyFilter** | [**number**] | Filter by faculty ID | (optional) defaults to undefined|
| **roleFilter** | [**number**] | Filter by role ID | (optional) defaults to undefined|


### Return type

**PersonalPlansResponseDto**

### Authorization

[bearer](../README.md#bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**200** | Successfully retrieved personal development plans |  -  |
|**400** | Bad request |  -  |
|**401** | Unauthorized |  -  |
|**403** | Forbidden |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **developmentPlansControllerRemove**
> developmentPlansControllerRemove()


### Example

```typescript
import {
    DevelopmentPlansApi,
    Configuration
} from './api';

const configuration = new Configuration();
const apiInstance = new DevelopmentPlansApi(configuration);

let id: string; // (default to undefined)

const { status, data } = await apiInstance.developmentPlansControllerRemove(
    id
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **id** | [**string**] |  | defaults to undefined|


### Return type

void (empty response body)

### Authorization

[bearer](../README.md#bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**200** | The development plan has been successfully deleted. |  -  |
|**404** | Development plan not found |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **developmentPlansControllerUpdate**
> developmentPlansControllerUpdate(updateDevelopmentPlanDto)


### Example

```typescript
import {
    DevelopmentPlansApi,
    Configuration,
    UpdateDevelopmentPlanDto
} from './api';

const configuration = new Configuration();
const apiInstance = new DevelopmentPlansApi(configuration);

let id: string; // (default to undefined)
let updateDevelopmentPlanDto: UpdateDevelopmentPlanDto; //

const { status, data } = await apiInstance.developmentPlansControllerUpdate(
    id,
    updateDevelopmentPlanDto
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **updateDevelopmentPlanDto** | **UpdateDevelopmentPlanDto**|  | |
| **id** | [**string**] |  | defaults to undefined|


### Return type

void (empty response body)

### Authorization

[bearer](../README.md#bearer)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: Not defined


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**200** | The development plan has been successfully updated. |  -  |
|**404** | Development plan not found |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

