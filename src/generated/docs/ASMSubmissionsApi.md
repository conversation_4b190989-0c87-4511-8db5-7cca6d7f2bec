# ASMSubmissionsApi

All URIs are relative to *http://localhost*

|Method | HTTP request | Description|
|------------- | ------------- | -------------|
|[**submissionsControllerCreate**](#submissionscontrollercreate) | **POST** /submissions | สร้าง submission ใหม่|
|[**submissionsControllerFindAll**](#submissionscontrollerfindall) | **GET** /submissions | ดึงข้อมูล submissions ทั้งหมด|
|[**submissionsControllerFindDraft**](#submissionscontrollerfinddraft) | **GET** /submissions/{assessmentId}/{userId} | ดึงข้อมูล Draft submissions ทั้งหมด|
|[**submissionsControllerFindOne**](#submissionscontrollerfindone) | **GET** /submissions/{id} | ดึงข้อมูล submission ตาม ID|
|[**submissionsControllerFindSubmissionById**](#submissionscontrollerfindsubmissionbyid) | **GET** /submissions/find-submission/{linkUrl}/{userId} | |
|[**submissionsControllerGetQuizScore**](#submissionscontrollergetquizscore) | **GET** /submissions/quiz/score/{submissionId} | ดูคะแนนแบบทดสอบ|
|[**submissionsControllerRemove**](#submissionscontrollerremove) | **DELETE** /submissions/{id} | ลบ submission|
|[**submissionsControllerStartAssessment**](#submissionscontrollerstartassessment) | **POST** /submissions/start-assessment | เริ่มทำแบบทดสอบ|
|[**submissionsControllerSubmitAssessment**](#submissionscontrollersubmitassessment) | **PATCH** /submissions/submit-assessment/{submissionId} | ส่งแบบทดสอบ|
|[**submissionsControllerUpdate**](#submissionscontrollerupdate) | **PATCH** /submissions/{id} | อัปเดตข้อมูล submission|

# **submissionsControllerCreate**
> any submissionsControllerCreate(body)

     สร้าง submission ใหม่สำหรับการทำแบบทดสอบ     - ตรวจสอบว่า assessment ที่ระบุมีอยู่จริง     - ตรวจสอบ submitLimit ว่าผู้ใช้สามารถทำแบบทดสอบได้     - บันทึกเวลาเริ่มทำแบบทดสอบ     

### Example

```typescript
import {
    ASMSubmissionsApi,
    Configuration
} from './api';

const configuration = new Configuration();
const apiInstance = new ASMSubmissionsApi(configuration);

let body: object; //

const { status, data } = await apiInstance.submissionsControllerCreate(
    body
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **body** | **object**|  | |


### Return type

**any**

### Authorization

[bearer](../README.md#bearer)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**201** | สร้าง submission สำเร็จ |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **submissionsControllerFindAll**
> submissionsControllerFindAll()


### Example

```typescript
import {
    ASMSubmissionsApi,
    Configuration
} from './api';

const configuration = new Configuration();
const apiInstance = new ASMSubmissionsApi(configuration);

const { status, data } = await apiInstance.submissionsControllerFindAll();
```

### Parameters
This endpoint does not have any parameters.


### Return type

void (empty response body)

### Authorization

[bearer](../README.md#bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**200** | ดึงข้อมูลสำเร็จ |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **submissionsControllerFindDraft**
> submissionsControllerFindDraft()


### Example

```typescript
import {
    ASMSubmissionsApi,
    Configuration
} from './api';

const configuration = new Configuration();
const apiInstance = new ASMSubmissionsApi(configuration);

let assessmentId: number; // (default to undefined)
let userId: number; // (default to undefined)

const { status, data } = await apiInstance.submissionsControllerFindDraft(
    assessmentId,
    userId
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **assessmentId** | [**number**] |  | defaults to undefined|
| **userId** | [**number**] |  | defaults to undefined|


### Return type

void (empty response body)

### Authorization

[bearer](../README.md#bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**200** | ดึงข้อมูลสำเร็จ |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **submissionsControllerFindOne**
> submissionsControllerFindOne()


### Example

```typescript
import {
    ASMSubmissionsApi,
    Configuration
} from './api';

const configuration = new Configuration();
const apiInstance = new ASMSubmissionsApi(configuration);

let id: number; // (default to undefined)

const { status, data } = await apiInstance.submissionsControllerFindOne(
    id
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **id** | [**number**] |  | defaults to undefined|


### Return type

void (empty response body)

### Authorization

[bearer](../README.md#bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**200** | ดึงข้อมูลสำเร็จ |  -  |
|**404** | ไม่พบข้อมูล submission |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **submissionsControllerFindSubmissionById**
> submissionsControllerFindSubmissionById()


### Example

```typescript
import {
    ASMSubmissionsApi,
    Configuration
} from './api';

const configuration = new Configuration();
const apiInstance = new ASMSubmissionsApi(configuration);

let linkUrl: string; // (default to undefined)
let userId: number; // (default to undefined)

const { status, data } = await apiInstance.submissionsControllerFindSubmissionById(
    linkUrl,
    userId
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **linkUrl** | [**string**] |  | defaults to undefined|
| **userId** | [**number**] |  | defaults to undefined|


### Return type

void (empty response body)

### Authorization

[bearer](../README.md#bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**200** |  |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **submissionsControllerGetQuizScore**
> submissionsControllerGetQuizScore()


### Example

```typescript
import {
    ASMSubmissionsApi,
    Configuration
} from './api';

const configuration = new Configuration();
const apiInstance = new ASMSubmissionsApi(configuration);

let submissionId: number; // (default to undefined)

const { status, data } = await apiInstance.submissionsControllerGetQuizScore(
    submissionId
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **submissionId** | [**number**] |  | defaults to undefined|


### Return type

void (empty response body)

### Authorization

[bearer](../README.md#bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**200** |  |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **submissionsControllerRemove**
> submissionsControllerRemove()


### Example

```typescript
import {
    ASMSubmissionsApi,
    Configuration
} from './api';

const configuration = new Configuration();
const apiInstance = new ASMSubmissionsApi(configuration);

let id: number; // (default to undefined)

const { status, data } = await apiInstance.submissionsControllerRemove(
    id
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **id** | [**number**] |  | defaults to undefined|


### Return type

void (empty response body)

### Authorization

[bearer](../README.md#bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**200** | ลบข้อมูลสำเร็จ |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **submissionsControllerStartAssessment**
> submissionsControllerStartAssessment(startQuizDto)


### Example

```typescript
import {
    ASMSubmissionsApi,
    Configuration,
    StartQuizDto
} from './api';

const configuration = new Configuration();
const apiInstance = new ASMSubmissionsApi(configuration);

let startQuizDto: StartQuizDto; //

const { status, data } = await apiInstance.submissionsControllerStartAssessment(
    startQuizDto
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **startQuizDto** | **StartQuizDto**|  | |


### Return type

void (empty response body)

### Authorization

[bearer](../README.md#bearer)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: Not defined


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**201** | เริ่มทำแบบทดสอบสำเร็จ |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **submissionsControllerSubmitAssessment**
> submissionsControllerSubmitAssessment()


### Example

```typescript
import {
    ASMSubmissionsApi,
    Configuration
} from './api';

const configuration = new Configuration();
const apiInstance = new ASMSubmissionsApi(configuration);

let submissionId: number; // (default to undefined)

const { status, data } = await apiInstance.submissionsControllerSubmitAssessment(
    submissionId
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **submissionId** | [**number**] |  | defaults to undefined|


### Return type

void (empty response body)

### Authorization

[bearer](../README.md#bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**200** | ส่งแบบทดสอบสำเร็จ |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **submissionsControllerUpdate**
> submissionsControllerUpdate(body)


### Example

```typescript
import {
    ASMSubmissionsApi,
    Configuration
} from './api';

const configuration = new Configuration();
const apiInstance = new ASMSubmissionsApi(configuration);

let id: number; // (default to undefined)
let body: object; //

const { status, data } = await apiInstance.submissionsControllerUpdate(
    id,
    body
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **body** | **object**|  | |
| **id** | [**number**] |  | defaults to undefined|


### Return type

void (empty response body)

### Authorization

[bearer](../README.md#bearer)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: Not defined


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**200** | อัปเดตข้อมูลสำเร็จ |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

