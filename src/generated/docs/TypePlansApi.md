# TypePlansApi

All URIs are relative to *http://localhost*

|Method | HTTP request | Description|
|------------- | ------------- | -------------|
|[**typePlansControllerCreate**](#typeplanscontrollercreate) | **POST** /type-plans | |
|[**typePlansControllerFindAll**](#typeplanscontrollerfindall) | **GET** /type-plans | |
|[**typePlansControllerFindOne**](#typeplanscontrollerfindone) | **GET** /type-plans/{id} | |
|[**typePlansControllerRemoveSkillFromType**](#typeplanscontrollerremoveskillfromtype) | **DELETE** /type-plans/remove-skill | |
|[**typePlansControllerSelectSkillToType**](#typeplanscontrollerselectskilltotype) | **POST** /type-plans/select-skill | |

# **typePlansControllerCreate**
> typePlansControllerCreate(createTypePlanDto)


### Example

```typescript
import {
    TypePlansApi,
    Configuration,
    CreateTypePlanDto
} from './api';

const configuration = new Configuration();
const apiInstance = new TypePlansApi(configuration);

let createTypePlanDto: CreateTypePlanDto; //

const { status, data } = await apiInstance.typePlansControllerCreate(
    createTypePlanDto
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **createTypePlanDto** | **CreateTypePlanDto**|  | |


### Return type

void (empty response body)

### Authorization

[bearer](../README.md#bearer)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: Not defined


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**201** |  |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **typePlansControllerFindAll**
> typePlansControllerFindAll()


### Example

```typescript
import {
    TypePlansApi,
    Configuration
} from './api';

const configuration = new Configuration();
const apiInstance = new TypePlansApi(configuration);

const { status, data } = await apiInstance.typePlansControllerFindAll();
```

### Parameters
This endpoint does not have any parameters.


### Return type

void (empty response body)

### Authorization

[bearer](../README.md#bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**200** |  |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **typePlansControllerFindOne**
> typePlansControllerFindOne()


### Example

```typescript
import {
    TypePlansApi,
    Configuration
} from './api';

const configuration = new Configuration();
const apiInstance = new TypePlansApi(configuration);

let id: string; // (default to undefined)

const { status, data } = await apiInstance.typePlansControllerFindOne(
    id
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **id** | [**string**] |  | defaults to undefined|


### Return type

void (empty response body)

### Authorization

[bearer](../README.md#bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**200** |  |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **typePlansControllerRemoveSkillFromType**
> typePlansControllerRemoveSkillFromType(removeSkillDto)


### Example

```typescript
import {
    TypePlansApi,
    Configuration,
    RemoveSkillDto
} from './api';

const configuration = new Configuration();
const apiInstance = new TypePlansApi(configuration);

let removeSkillDto: RemoveSkillDto; //

const { status, data } = await apiInstance.typePlansControllerRemoveSkillFromType(
    removeSkillDto
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **removeSkillDto** | **RemoveSkillDto**|  | |


### Return type

void (empty response body)

### Authorization

[bearer](../README.md#bearer)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: Not defined


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**200** |  |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **typePlansControllerSelectSkillToType**
> typePlansControllerSelectSkillToType(selectSkillsDto)


### Example

```typescript
import {
    TypePlansApi,
    Configuration,
    SelectSkillsDto
} from './api';

const configuration = new Configuration();
const apiInstance = new TypePlansApi(configuration);

let selectSkillsDto: SelectSkillsDto; //

const { status, data } = await apiInstance.typePlansControllerSelectSkillToType(
    selectSkillsDto
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **selectSkillsDto** | **SelectSkillsDto**|  | |


### Return type

void (empty response body)

### Authorization

[bearer](../README.md#bearer)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: Not defined


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**201** |  |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

