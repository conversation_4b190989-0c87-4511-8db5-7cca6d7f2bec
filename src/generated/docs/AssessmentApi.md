# AssessmentApi

All URIs are relative to *http://localhost*

|Method | HTTP request | Description|
|------------- | ------------- | -------------|
|[**assessmentsControllerCopyAssessmentContent**](#assessmentscontrollercopyassessmentcontent) | **PATCH** /assessments/{sourceId}/copy-to/{targetId} | Copy content from one assessment to another|
|[**assessmentsControllerCreate**](#assessmentscontrollercreate) | **POST** /assessments | New Assessment (Quiz/Evaluate)|
|[**assessmentsControllerCreateCompleteAssessmentMockup**](#assessmentscontrollercreatecompleteassessmentmockup) | **POST** /assessments/mockup/complete | Create Complete Mockup Data (Assessment + Submissions)|
|[**assessmentsControllerCreateMockupAssessment**](#assessmentscontrollercreatemockupassessment) | **POST** /assessments/mockup | |
|[**assessmentsControllerCreateMockupSubmissions**](#assessmentscontrollercreatemockupsubmissions) | **POST** /assessments/mockup/submissions/{id} | Create Mockup Submissions for Assessment|
|[**assessmentsControllerCreateQuickMockup**](#assessmentscontrollercreatequickmockup) | **POST** /assessments/mockup/quick | Create Quick Mockup Assessment|
|[**assessmentsControllerExportAssessmentToExcel**](#assessmentscontrollerexportassessmenttoexcel) | **GET** /assessments/{id}/export/excel | Export assessment questions to Excel file|
|[**assessmentsControllerFindOne**](#assessmentscontrollerfindone) | **GET** /assessments/{id} | Get a single assessment by ID with paginated itemBlocks|
|[**assessmentsControllerGetAll**](#assessmentscontrollergetall) | **GET** /assessments | |
|[**assessmentsControllerGetAllEditor**](#assessmentscontrollergetalleditor) | **GET** /assessments/editor/view-assessment | |
|[**assessmentsControllerGetAllPrototypes**](#assessmentscontrollergetallprototypes) | **GET** /assessments/prototypes | Get all assessments that are prototypes (isPrototype&#x3D;true)|
|[**assessmentsControllerGetAllUser**](#assessmentscontrollergetalluser) | **GET** /assessments/standardUser/view-assessment | |
|[**assessmentsControllerGetByURL**](#assessmentscontrollergetbyurl) | **GET** /assessments/url/{url} | Get assessment by URL|
|[**assessmentsControllerGetChartData**](#assessmentscontrollergetchartdata) | **GET** /assessments/dashboard/evaluate/{assessmentId} | ดึงข่อมูลสำหรับโชว์ Graph|
|[**assessmentsControllerGetNumberOfResponses**](#assessmentscontrollergetnumberofresponses) | **GET** /assessments/header/{assessmentId} | ดึงข่อมูลจำนวนของResponses ทั้งหมด|
|[**assessmentsControllerGetOne**](#assessmentscontrollergetone) | **GET** /assessments/preview/{id}/{section} | Get assessment by section|
|[**assessmentsControllerGetQuizDashboardMeta**](#assessmentscontrollergetquizdashboardmeta) | **GET** /assessments/dashboard/quiz/{id} | ดึงข้อมูลแดชบอร์ดของแบบทดสอบ|
|[**assessmentsControllerGetQuizHeaderWithUserSubmissions**](#assessmentscontrollergetquizheaderwithusersubmissions) | **GET** /assessments/header-with-submissions/url/{linkUrl} | Get quiz header with user submission history|
|[**assessmentsControllerGetQuizParticipantDetails**](#assessmentscontrollergetquizparticipantdetails) | **GET** /assessments/dashboard/quiz/participant/{id} | ดึงข้อมูลรายละเอียดของผู้เข้าร่วมทำแบบทดสอบเฉพาะราย|
|[**assessmentsControllerGetQuizParticipantTextFieldGrading**](#assessmentscontrollergetquizparticipanttextfieldgrading) | **GET** /assessments/dashboard/quiz/participant/{id}/textfield-grading | ดึงข้อมูลรายละเอียดของผู้เข้าร่วมทำแบบทดสอบสำหรับการให้คะแนน TEXTFIELD|
|[**assessmentsControllerGetQuizParticipants**](#assessmentscontrollergetquizparticipants) | **GET** /assessments/dashboard/quiz/{id}/participants | ดึงข้อมูลผู้เข้าร่วมทำแบบทดสอบทั้งหมด|
|[**assessmentsControllerGetQuizQuestionResponses**](#assessmentscontrollergetquizquestionresponses) | **GET** /assessments/dashboard/quiz/{id}/questions | ดึงข้อมูลการตอบคำถามทั้งหมดของแบบทดสอบ|
|[**assessmentsControllerRemove**](#assessmentscontrollerremove) | **DELETE** /assessments/{id} | Delete an assessment|
|[**assessmentsControllerSaveTextFieldScore**](#assessmentscontrollersavetextfieldscore) | **POST** /assessments/dashboard/quiz/participant/{submissionId}/textfield-score | Save custom score for TEXTFIELD question|
|[**assessmentsControllerUpdate**](#assessmentscontrollerupdate) | **PATCH** /assessments/{id} | Update an existing assessment|

# **assessmentsControllerCopyAssessmentContent**
> assessmentsControllerCopyAssessmentContent()


### Example

```typescript
import {
    AssessmentApi,
    Configuration
} from './api';

const configuration = new Configuration();
const apiInstance = new AssessmentApi(configuration);

let sourceId: number; //Source assessment ID (default to undefined)
let targetId: number; //Target assessment ID (default to undefined)

const { status, data } = await apiInstance.assessmentsControllerCopyAssessmentContent(
    sourceId,
    targetId
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **sourceId** | [**number**] | Source assessment ID | defaults to undefined|
| **targetId** | [**number**] | Target assessment ID | defaults to undefined|


### Return type

void (empty response body)

### Authorization

[bearer](../README.md#bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**200** |  |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **assessmentsControllerCreate**
> assessmentsControllerCreate()


### Example

```typescript
import {
    AssessmentApi,
    Configuration
} from './api';

const configuration = new Configuration();
const apiInstance = new AssessmentApi(configuration);

let creatorUserId: number; //รหัสผู้สร้างแบบประเมิน (default to undefined)
let type: AssessmentType; //ประเภทของแบบประเมิน (QUIZ หรือ FORM) (default to undefined)
let programId: number; //รหัสโปรแกรม (default to undefined)

const { status, data } = await apiInstance.assessmentsControllerCreate(
    creatorUserId,
    type,
    programId
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **creatorUserId** | [**number**] | รหัสผู้สร้างแบบประเมิน | defaults to undefined|
| **type** | **AssessmentType** | ประเภทของแบบประเมิน (QUIZ หรือ FORM) | defaults to undefined|
| **programId** | [**number**] | รหัสโปรแกรม | defaults to undefined|


### Return type

void (empty response body)

### Authorization

[bearer](../README.md#bearer)

### HTTP request headers

 - **Content-Type**: multipart/form-data
 - **Accept**: Not defined


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**201** |  |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **assessmentsControllerCreateCompleteAssessmentMockup**
> assessmentsControllerCreateCompleteAssessmentMockup()

Creates a complete assessment with customizable questions and submissions for dashboard testing

### Example

```typescript
import {
    AssessmentApi,
    Configuration
} from './api';

const configuration = new Configuration();
const apiInstance = new AssessmentApi(configuration);

let totalQuestions: number; //Total number of questions (default: 100) (optional) (default to undefined)
let submissionCount: number; //Number of submissions (default: 50) (optional) (default to undefined)
let totalScore: number; //Total score (auto-calculated if not provided) (optional) (default to undefined)

const { status, data } = await apiInstance.assessmentsControllerCreateCompleteAssessmentMockup(
    totalQuestions,
    submissionCount,
    totalScore
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **totalQuestions** | [**number**] | Total number of questions (default: 100) | (optional) defaults to undefined|
| **submissionCount** | [**number**] | Number of submissions (default: 50) | (optional) defaults to undefined|
| **totalScore** | [**number**] | Total score (auto-calculated if not provided) | (optional) defaults to undefined|


### Return type

void (empty response body)

### Authorization

[bearer](../README.md#bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**201** |  |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **assessmentsControllerCreateMockupAssessment**
> assessmentsControllerCreateMockupAssessment()


### Example

```typescript
import {
    AssessmentApi,
    Configuration
} from './api';

const configuration = new Configuration();
const apiInstance = new AssessmentApi(configuration);

let totalQuestions: number; //Total number of questions (default: 100) (optional) (default to undefined)
let totalScore: number; //Total score (auto-calculated if not provided) (optional) (default to undefined)

const { status, data } = await apiInstance.assessmentsControllerCreateMockupAssessment(
    totalQuestions,
    totalScore
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **totalQuestions** | [**number**] | Total number of questions (default: 100) | (optional) defaults to undefined|
| **totalScore** | [**number**] | Total score (auto-calculated if not provided) | (optional) defaults to undefined|


### Return type

void (empty response body)

### Authorization

[bearer](../README.md#bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**201** |  |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **assessmentsControllerCreateMockupSubmissions**
> assessmentsControllerCreateMockupSubmissions()

Creates 50 mockup submissions with realistic response patterns for the specified assessment

### Example

```typescript
import {
    AssessmentApi,
    Configuration
} from './api';

const configuration = new Configuration();
const apiInstance = new AssessmentApi(configuration);

let id: number; //Assessment ID to create submissions for (default to undefined)

const { status, data } = await apiInstance.assessmentsControllerCreateMockupSubmissions(
    id
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **id** | [**number**] | Assessment ID to create submissions for | defaults to undefined|


### Return type

void (empty response body)

### Authorization

[bearer](../README.md#bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**201** |  |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **assessmentsControllerCreateQuickMockup**
> assessmentsControllerCreateQuickMockup()

Creates a quick assessment with fewer questions for testing

### Example

```typescript
import {
    AssessmentApi,
    Configuration
} from './api';

const configuration = new Configuration();
const apiInstance = new AssessmentApi(configuration);

let totalQuestions: number; //Total number of questions (default: 20) (optional) (default to undefined)
let submissionCount: number; //Number of submissions (default: 20) (optional) (default to undefined)

const { status, data } = await apiInstance.assessmentsControllerCreateQuickMockup(
    totalQuestions,
    submissionCount
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **totalQuestions** | [**number**] | Total number of questions (default: 20) | (optional) defaults to undefined|
| **submissionCount** | [**number**] | Number of submissions (default: 20) | (optional) defaults to undefined|


### Return type

void (empty response body)

### Authorization

[bearer](../README.md#bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**201** |  |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **assessmentsControllerExportAssessmentToExcel**
> assessmentsControllerExportAssessmentToExcel()


### Example

```typescript
import {
    AssessmentApi,
    Configuration
} from './api';

const configuration = new Configuration();
const apiInstance = new AssessmentApi(configuration);

let id: number; //Assessment ID to export (default to undefined)

const { status, data } = await apiInstance.assessmentsControllerExportAssessmentToExcel(
    id
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **id** | [**number**] | Assessment ID to export | defaults to undefined|


### Return type

void (empty response body)

### Authorization

[bearer](../README.md#bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**200** |  |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **assessmentsControllerFindOne**
> assessmentsControllerFindOne()


### Example

```typescript
import {
    AssessmentApi,
    Configuration
} from './api';

const configuration = new Configuration();
const apiInstance = new AssessmentApi(configuration);

let id: number; //The ID of the assessment (default to undefined)
let page: number; // (optional) (default to undefined)

const { status, data } = await apiInstance.assessmentsControllerFindOne(
    id,
    page
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **id** | [**number**] | The ID of the assessment | defaults to undefined|
| **page** | [**number**] |  | (optional) defaults to undefined|


### Return type

void (empty response body)

### Authorization

[bearer](../README.md#bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**200** |  |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **assessmentsControllerGetAll**
> assessmentsControllerGetAll()


### Example

```typescript
import {
    AssessmentApi,
    Configuration
} from './api';

const configuration = new Configuration();
const apiInstance = new AssessmentApi(configuration);

let type: 'EVALUATE' | 'QUIZ'; //ประเภทของแบบประเมิน (default to undefined)
let limit: any; //Number of items per page (default to undefined)
let page: any; //Page number to retrieve (default to undefined)
let search: string; //ค้นหาด้วยชื่อแบบประเมิน (optional) (default to undefined)
let sortBy: any; //Field to sort by (e.g., id, name, email) (optional) (default to undefined)
let order: any; //Sort order (ASC or DESC) (optional) (default to undefined)

const { status, data } = await apiInstance.assessmentsControllerGetAll(
    type,
    limit,
    page,
    search,
    sortBy,
    order
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **type** | [**&#39;EVALUATE&#39; | &#39;QUIZ&#39;**]**Array<&#39;EVALUATE&#39; &#124; &#39;QUIZ&#39;>** | ประเภทของแบบประเมิน | defaults to undefined|
| **limit** | **any** | Number of items per page | defaults to undefined|
| **page** | **any** | Page number to retrieve | defaults to undefined|
| **search** | [**string**] | ค้นหาด้วยชื่อแบบประเมิน | (optional) defaults to undefined|
| **sortBy** | **any** | Field to sort by (e.g., id, name, email) | (optional) defaults to undefined|
| **order** | **any** | Sort order (ASC or DESC) | (optional) defaults to undefined|


### Return type

void (empty response body)

### Authorization

[bearer](../README.md#bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**200** |  |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **assessmentsControllerGetAllEditor**
> assessmentsControllerGetAllEditor()


### Example

```typescript
import {
    AssessmentApi,
    Configuration
} from './api';

const configuration = new Configuration();
const apiInstance = new AssessmentApi(configuration);

let limit: any; //Number of items per page (default to undefined)
let page: any; //Page number to retrieve (default to undefined)
let type: 'EVALUATE' | 'QUIZ'; // (optional) (default to undefined)
let sortBy: any; //Field to sort by (e.g., id, name, email) (optional) (default to undefined)
let order: any; //Sort order (ASC or DESC) (optional) (default to undefined)
let search: any; //Search term to filter results (optional) (default to undefined)

const { status, data } = await apiInstance.assessmentsControllerGetAllEditor(
    limit,
    page,
    type,
    sortBy,
    order,
    search
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **limit** | **any** | Number of items per page | defaults to undefined|
| **page** | **any** | Page number to retrieve | defaults to undefined|
| **type** | [**&#39;EVALUATE&#39; | &#39;QUIZ&#39;**]**Array<&#39;EVALUATE&#39; &#124; &#39;QUIZ&#39;>** |  | (optional) defaults to undefined|
| **sortBy** | **any** | Field to sort by (e.g., id, name, email) | (optional) defaults to undefined|
| **order** | **any** | Sort order (ASC or DESC) | (optional) defaults to undefined|
| **search** | **any** | Search term to filter results | (optional) defaults to undefined|


### Return type

void (empty response body)

### Authorization

[bearer](../README.md#bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**200** |  |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **assessmentsControllerGetAllPrototypes**
> assessmentsControllerGetAllPrototypes()


### Example

```typescript
import {
    AssessmentApi,
    Configuration
} from './api';

const configuration = new Configuration();
const apiInstance = new AssessmentApi(configuration);

let type: 'EVALUATE' | 'QUIZ'; // (default to undefined)
let limit: any; //Number of items per page (default to undefined)
let page: any; //Page number to retrieve (default to undefined)
let sortBy: any; //Field to sort by (e.g., id, name, email) (optional) (default to undefined)
let order: any; //Sort order (ASC or DESC) (optional) (default to undefined)
let search: any; //Search term to filter results (optional) (default to undefined)

const { status, data } = await apiInstance.assessmentsControllerGetAllPrototypes(
    type,
    limit,
    page,
    sortBy,
    order,
    search
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **type** | [**&#39;EVALUATE&#39; | &#39;QUIZ&#39;**]**Array<&#39;EVALUATE&#39; &#124; &#39;QUIZ&#39;>** |  | defaults to undefined|
| **limit** | **any** | Number of items per page | defaults to undefined|
| **page** | **any** | Page number to retrieve | defaults to undefined|
| **sortBy** | **any** | Field to sort by (e.g., id, name, email) | (optional) defaults to undefined|
| **order** | **any** | Sort order (ASC or DESC) | (optional) defaults to undefined|
| **search** | **any** | Search term to filter results | (optional) defaults to undefined|


### Return type

void (empty response body)

### Authorization

[bearer](../README.md#bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**200** |  |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **assessmentsControllerGetAllUser**
> assessmentsControllerGetAllUser()


### Example

```typescript
import {
    AssessmentApi,
    Configuration
} from './api';

const configuration = new Configuration();
const apiInstance = new AssessmentApi(configuration);

let limit: any; //Number of items per page (default to undefined)
let page: any; //Page number to retrieve (default to undefined)
let type: 'EVALUATE' | 'QUIZ'; // (optional) (default to undefined)
let sortBy: any; //Field to sort by (e.g., id, name, email) (optional) (default to undefined)
let order: any; //Sort order (ASC or DESC) (optional) (default to undefined)
let search: any; //Search term to filter results (optional) (default to undefined)

const { status, data } = await apiInstance.assessmentsControllerGetAllUser(
    limit,
    page,
    type,
    sortBy,
    order,
    search
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **limit** | **any** | Number of items per page | defaults to undefined|
| **page** | **any** | Page number to retrieve | defaults to undefined|
| **type** | [**&#39;EVALUATE&#39; | &#39;QUIZ&#39;**]**Array<&#39;EVALUATE&#39; &#124; &#39;QUIZ&#39;>** |  | (optional) defaults to undefined|
| **sortBy** | **any** | Field to sort by (e.g., id, name, email) | (optional) defaults to undefined|
| **order** | **any** | Sort order (ASC or DESC) | (optional) defaults to undefined|
| **search** | **any** | Search term to filter results | (optional) defaults to undefined|


### Return type

void (empty response body)

### Authorization

[bearer](../README.md#bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**200** |  |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **assessmentsControllerGetByURL**
> assessmentsControllerGetByURL()


### Example

```typescript
import {
    AssessmentApi,
    Configuration
} from './api';

const configuration = new Configuration();
const apiInstance = new AssessmentApi(configuration);

let url: string; // (default to undefined)

const { status, data } = await apiInstance.assessmentsControllerGetByURL(
    url
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **url** | [**string**] |  | defaults to undefined|


### Return type

void (empty response body)

### Authorization

[bearer](../README.md#bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**200** |  |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **assessmentsControllerGetChartData**
> assessmentsControllerGetChartData()


### Example

```typescript
import {
    AssessmentApi,
    Configuration
} from './api';

const configuration = new Configuration();
const apiInstance = new AssessmentApi(configuration);

let assessmentId: number; // (default to undefined)

const { status, data } = await apiInstance.assessmentsControllerGetChartData(
    assessmentId
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **assessmentId** | [**number**] |  | defaults to undefined|


### Return type

void (empty response body)

### Authorization

[bearer](../README.md#bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**200** |  |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **assessmentsControllerGetNumberOfResponses**
> assessmentsControllerGetNumberOfResponses()


### Example

```typescript
import {
    AssessmentApi,
    Configuration
} from './api';

const configuration = new Configuration();
const apiInstance = new AssessmentApi(configuration);

let assessmentId: number; // (default to undefined)

const { status, data } = await apiInstance.assessmentsControllerGetNumberOfResponses(
    assessmentId
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **assessmentId** | [**number**] |  | defaults to undefined|


### Return type

void (empty response body)

### Authorization

[bearer](../README.md#bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**200** |  |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **assessmentsControllerGetOne**
> assessmentsControllerGetOne()


### Example

```typescript
import {
    AssessmentApi,
    Configuration
} from './api';

const configuration = new Configuration();
const apiInstance = new AssessmentApi(configuration);

let id: number; // (default to undefined)
let section: number; // (default to undefined)

const { status, data } = await apiInstance.assessmentsControllerGetOne(
    id,
    section
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **id** | [**number**] |  | defaults to undefined|
| **section** | [**number**] |  | defaults to undefined|


### Return type

void (empty response body)

### Authorization

[bearer](../README.md#bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**200** |  |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **assessmentsControllerGetQuizDashboardMeta**
> assessmentsControllerGetQuizDashboardMeta()


### Example

```typescript
import {
    AssessmentApi,
    Configuration
} from './api';

const configuration = new Configuration();
const apiInstance = new AssessmentApi(configuration);

let id: number; //รหัสของแบบทดสอบ (default to undefined)

const { status, data } = await apiInstance.assessmentsControllerGetQuizDashboardMeta(
    id
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **id** | [**number**] | รหัสของแบบทดสอบ | defaults to undefined|


### Return type

void (empty response body)

### Authorization

[bearer](../README.md#bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**200** |  |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **assessmentsControllerGetQuizHeaderWithUserSubmissions**
> assessmentsControllerGetQuizHeaderWithUserSubmissions()


### Example

```typescript
import {
    AssessmentApi,
    Configuration
} from './api';

const configuration = new Configuration();
const apiInstance = new AssessmentApi(configuration);

let linkUrl: string; // (default to undefined)
let userId: number; // (default to undefined)

const { status, data } = await apiInstance.assessmentsControllerGetQuizHeaderWithUserSubmissions(
    linkUrl,
    userId
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **linkUrl** | [**string**] |  | defaults to undefined|
| **userId** | [**number**] |  | defaults to undefined|


### Return type

void (empty response body)

### Authorization

[bearer](../README.md#bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**200** |  |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **assessmentsControllerGetQuizParticipantDetails**
> assessmentsControllerGetQuizParticipantDetails()


### Example

```typescript
import {
    AssessmentApi,
    Configuration
} from './api';

const configuration = new Configuration();
const apiInstance = new AssessmentApi(configuration);

let id: number; //รหัสของผู้เข้าร่วม (default to undefined)
let limit: any; //Number of items per page (default to undefined)
let page: any; //Page number to retrieve (default to undefined)
let sortBy: any; //Field to sort by (e.g., id, name, email) (optional) (default to undefined)
let order: any; //Sort order (ASC or DESC) (optional) (default to undefined)
let search: any; //Search term to filter results (optional) (default to undefined)

const { status, data } = await apiInstance.assessmentsControllerGetQuizParticipantDetails(
    id,
    limit,
    page,
    sortBy,
    order,
    search
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **id** | [**number**] | รหัสของผู้เข้าร่วม | defaults to undefined|
| **limit** | **any** | Number of items per page | defaults to undefined|
| **page** | **any** | Page number to retrieve | defaults to undefined|
| **sortBy** | **any** | Field to sort by (e.g., id, name, email) | (optional) defaults to undefined|
| **order** | **any** | Sort order (ASC or DESC) | (optional) defaults to undefined|
| **search** | **any** | Search term to filter results | (optional) defaults to undefined|


### Return type

void (empty response body)

### Authorization

[bearer](../README.md#bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**200** |  |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **assessmentsControllerGetQuizParticipantTextFieldGrading**
> assessmentsControllerGetQuizParticipantTextFieldGrading()


### Example

```typescript
import {
    AssessmentApi,
    Configuration
} from './api';

const configuration = new Configuration();
const apiInstance = new AssessmentApi(configuration);

let id: number; //รหัสของผู้เข้าร่วม (default to undefined)
let limit: any; //Number of items per page (default to undefined)
let page: any; //Page number to retrieve (default to undefined)
let sortBy: any; //Field to sort by (e.g., id, name, email) (optional) (default to undefined)
let order: any; //Sort order (ASC or DESC) (optional) (default to undefined)
let search: any; //Search term to filter results (optional) (default to undefined)

const { status, data } = await apiInstance.assessmentsControllerGetQuizParticipantTextFieldGrading(
    id,
    limit,
    page,
    sortBy,
    order,
    search
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **id** | [**number**] | รหัสของผู้เข้าร่วม | defaults to undefined|
| **limit** | **any** | Number of items per page | defaults to undefined|
| **page** | **any** | Page number to retrieve | defaults to undefined|
| **sortBy** | **any** | Field to sort by (e.g., id, name, email) | (optional) defaults to undefined|
| **order** | **any** | Sort order (ASC or DESC) | (optional) defaults to undefined|
| **search** | **any** | Search term to filter results | (optional) defaults to undefined|


### Return type

void (empty response body)

### Authorization

[bearer](../README.md#bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**200** |  |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **assessmentsControllerGetQuizParticipants**
> assessmentsControllerGetQuizParticipants()


### Example

```typescript
import {
    AssessmentApi,
    Configuration
} from './api';

const configuration = new Configuration();
const apiInstance = new AssessmentApi(configuration);

let id: number; //รหัสของแบบทดสอบ (default to undefined)

const { status, data } = await apiInstance.assessmentsControllerGetQuizParticipants(
    id
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **id** | [**number**] | รหัสของแบบทดสอบ | defaults to undefined|


### Return type

void (empty response body)

### Authorization

[bearer](../README.md#bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**200** |  |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **assessmentsControllerGetQuizQuestionResponses**
> assessmentsControllerGetQuizQuestionResponses()


### Example

```typescript
import {
    AssessmentApi,
    Configuration
} from './api';

const configuration = new Configuration();
const apiInstance = new AssessmentApi(configuration);

let id: number; //รหัสของแบบทดสอบ (default to undefined)
let limit: any; //Number of items per page (default to undefined)
let page: any; //Page number to retrieve (default to undefined)
let sortBy: any; //Field to sort by (e.g., id, name, email) (optional) (default to undefined)
let order: any; //Sort order (ASC or DESC) (optional) (default to undefined)
let search: any; //Search term to filter results (optional) (default to undefined)

const { status, data } = await apiInstance.assessmentsControllerGetQuizQuestionResponses(
    id,
    limit,
    page,
    sortBy,
    order,
    search
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **id** | [**number**] | รหัสของแบบทดสอบ | defaults to undefined|
| **limit** | **any** | Number of items per page | defaults to undefined|
| **page** | **any** | Page number to retrieve | defaults to undefined|
| **sortBy** | **any** | Field to sort by (e.g., id, name, email) | (optional) defaults to undefined|
| **order** | **any** | Sort order (ASC or DESC) | (optional) defaults to undefined|
| **search** | **any** | Search term to filter results | (optional) defaults to undefined|


### Return type

void (empty response body)

### Authorization

[bearer](../README.md#bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**200** |  |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **assessmentsControllerRemove**
> assessmentsControllerRemove()


### Example

```typescript
import {
    AssessmentApi,
    Configuration
} from './api';

const configuration = new Configuration();
const apiInstance = new AssessmentApi(configuration);

let id: string; // (default to undefined)

const { status, data } = await apiInstance.assessmentsControllerRemove(
    id
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **id** | [**string**] |  | defaults to undefined|


### Return type

void (empty response body)

### Authorization

[bearer](../README.md#bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**200** |  |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **assessmentsControllerSaveTextFieldScore**
> AssessmentsControllerSaveTextFieldScore200Response assessmentsControllerSaveTextFieldScore(body)


### Example

```typescript
import {
    AssessmentApi,
    Configuration
} from './api';

const configuration = new Configuration();
const apiInstance = new AssessmentApi(configuration);

let submissionId: number; //Submission ID (default to undefined)
let body: object; //

const { status, data } = await apiInstance.assessmentsControllerSaveTextFieldScore(
    submissionId,
    body
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **body** | **object**|  | |
| **submissionId** | [**number**] | Submission ID | defaults to undefined|


### Return type

**AssessmentsControllerSaveTextFieldScore200Response**

### Authorization

[bearer](../README.md#bearer)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**200** | Score saved successfully |  -  |
|**404** | Submission or question not found |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **assessmentsControllerUpdate**
> assessmentsControllerUpdate()


### Example

```typescript
import {
    AssessmentApi,
    Configuration
} from './api';

const configuration = new Configuration();
const apiInstance = new AssessmentApi(configuration);

let id: number; // (default to undefined)
let creatorUserId: number; //รหัสผู้สร้างแบบประเมิน (optional) (default to undefined)
let type: AssessmentType; //ประเภทของแบบประเมิน (QUIZ หรือ FORM) (optional) (default to undefined)
let programId: number; //รหัสโปรแกรม (optional) (default to undefined)
let name: string; //ชื่อแบบประเมิน (optional) (default to undefined)
let submitLimit: number; //จำนวนครั้งที่อนุญาตให้ส่งแบบประเมิน (optional) (default to undefined)
let timeout: number; //เวลาที่อนุญาต (วินาที) (optional) (default to undefined)
let startAt: string; //วันที่เริ่มต้น (optional) (default to undefined)
let endAt: string; //วันที่สิ้นสุด (optional) (default to undefined)
let status: boolean; //สถานะการเปิดใช้งาน (optional) (default to undefined)
let totalScore: number; //คะแนนรวม (สำหรับ Quiz เท่านั้น) (optional) (default to undefined)
let passRatio: number; //อัตราส่วนการผ่าน (เช่น 0.5, 1.5) (optional) (default to undefined)
let isPrototype: boolean; //เป็นต้นแบบหรือไม่ (optional) (default to undefined)
let responseEdit: boolean; //อนุญาตให้แก้ไขคำตอบหรือไม่ (optional) (default to undefined)
let linkURL: string; //URL ลิงก์ของแบบประเมิน (optional) (default to undefined)

const { status, data } = await apiInstance.assessmentsControllerUpdate(
    id,
    creatorUserId,
    type,
    programId,
    name,
    submitLimit,
    timeout,
    startAt,
    endAt,
    status,
    totalScore,
    passRatio,
    isPrototype,
    responseEdit,
    linkURL
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **id** | [**number**] |  | defaults to undefined|
| **creatorUserId** | [**number**] | รหัสผู้สร้างแบบประเมิน | (optional) defaults to undefined|
| **type** | **AssessmentType** | ประเภทของแบบประเมิน (QUIZ หรือ FORM) | (optional) defaults to undefined|
| **programId** | [**number**] | รหัสโปรแกรม | (optional) defaults to undefined|
| **name** | [**string**] | ชื่อแบบประเมิน | (optional) defaults to undefined|
| **submitLimit** | [**number**] | จำนวนครั้งที่อนุญาตให้ส่งแบบประเมิน | (optional) defaults to undefined|
| **timeout** | [**number**] | เวลาที่อนุญาต (วินาที) | (optional) defaults to undefined|
| **startAt** | [**string**] | วันที่เริ่มต้น | (optional) defaults to undefined|
| **endAt** | [**string**] | วันที่สิ้นสุด | (optional) defaults to undefined|
| **status** | [**boolean**] | สถานะการเปิดใช้งาน | (optional) defaults to undefined|
| **totalScore** | [**number**] | คะแนนรวม (สำหรับ Quiz เท่านั้น) | (optional) defaults to undefined|
| **passRatio** | [**number**] | อัตราส่วนการผ่าน (เช่น 0.5, 1.5) | (optional) defaults to undefined|
| **isPrototype** | [**boolean**] | เป็นต้นแบบหรือไม่ | (optional) defaults to undefined|
| **responseEdit** | [**boolean**] | อนุญาตให้แก้ไขคำตอบหรือไม่ | (optional) defaults to undefined|
| **linkURL** | [**string**] | URL ลิงก์ของแบบประเมิน | (optional) defaults to undefined|


### Return type

void (empty response body)

### Authorization

[bearer](../README.md#bearer)

### HTTP request headers

 - **Content-Type**: multipart/form-data
 - **Accept**: Not defined


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**200** |  |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

