# CreateSkillDto


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**name** | **string** |  | [default to undefined]
**description** | **string** |  | [optional] [default to undefined]
**career_type** | **string** |  | [optional] [default to undefined]
**programId** | **number** |  | [default to undefined]
**tracking** | **boolean** |  | [optional] [default to undefined]
**evaluatorId** | **number** |  | [optional] [default to undefined]
**dep_id** | **number** |  | [optional] [default to undefined]
**competencyIds** | **Array&lt;string&gt;** |  | [optional] [default to undefined]

## Example

```typescript
import { CreateSkillDto } from './api';

const instance: CreateSkillDto = {
    name,
    description,
    career_type,
    programId,
    tracking,
    evaluatorId,
    dep_id,
    competencyIds,
};
```

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)
