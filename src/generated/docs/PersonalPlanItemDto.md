# PersonalPlanItemDto


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**id** | **number** | User ID | [default to undefined]
**name** | **string** | User full name | [default to undefined]
**careerName** | **string** | Career name | [default to undefined]

## Example

```typescript
import { PersonalPlanItemDto } from './api';

const instance: PersonalPlanItemDto = {
    id,
    name,
    careerName,
};
```

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)
