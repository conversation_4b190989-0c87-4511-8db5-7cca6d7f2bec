# AssessmentsControllerSaveTextFieldScore200Response


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**success** | **boolean** |  | [optional] [default to undefined]
**message** | **string** |  | [optional] [default to undefined]

## Example

```typescript
import { AssessmentsControllerSaveTextFieldScore200Response } from './api';

const instance: AssessmentsControllerSaveTextFieldScore200Response = {
    success,
    message,
};
```

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)
