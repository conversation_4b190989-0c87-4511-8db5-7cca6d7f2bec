# CreateProgramDto


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**name** | **string** | ชื่อโครงงาน | [default to undefined]
**description** | **string** | รายละเอียดโครงงาน | [default to undefined]
**creatorId** | **number** | รหัสผู้สร้างโครงงาน | [default to undefined]

## Example

```typescript
import { CreateProgramDto } from './api';

const instance: CreateProgramDto = {
    name,
    description,
    creatorId,
};
```

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)
