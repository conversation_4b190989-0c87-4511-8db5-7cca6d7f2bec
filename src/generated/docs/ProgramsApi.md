# ProgramsApi

All URIs are relative to *http://localhost*

|Method | HTTP request | Description|
|------------- | ------------- | -------------|
|[**programsControllerCreate**](#programscontrollercreate) | **POST** /programs | Create a new program|
|[**programsControllerFindAll**](#programscontrollerfindall) | **GET** /programs | Get all programs|
|[**programsControllerFindOne**](#programscontrollerfindone) | **GET** /programs/{id} | Get a program by ID|
|[**programsControllerRemove**](#programscontrollerremove) | **DELETE** /programs/{id} | Delete a program|
|[**programsControllerUpdate**](#programscontrollerupdate) | **PATCH** /programs/{id} | Update a program|

# **programsControllerCreate**
> programsControllerCreate(createProgramDto)


### Example

```typescript
import {
    ProgramsApi,
    Configuration,
    CreateProgramDto
} from './api';

const configuration = new Configuration();
const apiInstance = new ProgramsApi(configuration);

let createProgramDto: CreateProgramDto; //

const { status, data } = await apiInstance.programsControllerCreate(
    createProgramDto
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **createProgramDto** | **CreateProgramDto**|  | |


### Return type

void (empty response body)

### Authorization

[bearer](../README.md#bearer)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: Not defined


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**201** | Program successfully created |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **programsControllerFindAll**
> programsControllerFindAll()


### Example

```typescript
import {
    ProgramsApi,
    Configuration
} from './api';

const configuration = new Configuration();
const apiInstance = new ProgramsApi(configuration);

let limit: any; //Number of items per page (default to undefined)
let page: any; //Page number to retrieve (default to undefined)
let sortBy: any; //Field to sort by (e.g., id, name, email) (optional) (default to undefined)
let order: any; //Sort order (ASC or DESC) (optional) (default to undefined)
let search: any; //Search term to filter results (optional) (default to undefined)

const { status, data } = await apiInstance.programsControllerFindAll(
    limit,
    page,
    sortBy,
    order,
    search
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **limit** | **any** | Number of items per page | defaults to undefined|
| **page** | **any** | Page number to retrieve | defaults to undefined|
| **sortBy** | **any** | Field to sort by (e.g., id, name, email) | (optional) defaults to undefined|
| **order** | **any** | Sort order (ASC or DESC) | (optional) defaults to undefined|
| **search** | **any** | Search term to filter results | (optional) defaults to undefined|


### Return type

void (empty response body)

### Authorization

[bearer](../README.md#bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**200** | Return all programs |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **programsControllerFindOne**
> programsControllerFindOne()


### Example

```typescript
import {
    ProgramsApi,
    Configuration
} from './api';

const configuration = new Configuration();
const apiInstance = new ProgramsApi(configuration);

let id: string; //Program ID (default to undefined)

const { status, data } = await apiInstance.programsControllerFindOne(
    id
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **id** | [**string**] | Program ID | defaults to undefined|


### Return type

void (empty response body)

### Authorization

[bearer](../README.md#bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**200** | Return the program |  -  |
|**404** | Program not found |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **programsControllerRemove**
> programsControllerRemove()


### Example

```typescript
import {
    ProgramsApi,
    Configuration
} from './api';

const configuration = new Configuration();
const apiInstance = new ProgramsApi(configuration);

let id: string; //Program ID (default to undefined)

const { status, data } = await apiInstance.programsControllerRemove(
    id
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **id** | [**string**] | Program ID | defaults to undefined|


### Return type

void (empty response body)

### Authorization

[bearer](../README.md#bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**204** | Program successfully deleted |  -  |
|**404** | Program not found |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **programsControllerUpdate**
> programsControllerUpdate(updateProgramDto)


### Example

```typescript
import {
    ProgramsApi,
    Configuration,
    UpdateProgramDto
} from './api';

const configuration = new Configuration();
const apiInstance = new ProgramsApi(configuration);

let id: string; //Program ID (default to undefined)
let updateProgramDto: UpdateProgramDto; //

const { status, data } = await apiInstance.programsControllerUpdate(
    id,
    updateProgramDto
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **updateProgramDto** | **UpdateProgramDto**|  | |
| **id** | [**string**] | Program ID | defaults to undefined|


### Return type

void (empty response body)

### Authorization

[bearer](../README.md#bearer)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: Not defined


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**200** | Program successfully updated |  -  |
|**404** | Program not found |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

