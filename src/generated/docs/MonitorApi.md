# MonitorApi

All URIs are relative to *http://localhost*

|Method | HTTP request | Description|
|------------- | ------------- | -------------|
|[**monitorControllerCreate**](#monitorcontrollercreate) | **POST** /monitor | |
|[**monitorControllerFindAll**](#monitorcontrollerfindall) | **GET** /monitor | |
|[**monitorControllerFindOne**](#monitorcontrollerfindone) | **GET** /monitor/{id} | |
|[**monitorControllerRemove**](#monitorcontrollerremove) | **DELETE** /monitor/{id} | |
|[**monitorControllerUpdate**](#monitorcontrollerupdate) | **PATCH** /monitor/{id} | |

# **monitorControllerCreate**
> monitorControllerCreate(body)


### Example

```typescript
import {
    MonitorApi,
    Configuration
} from './api';

const configuration = new Configuration();
const apiInstance = new Monitor<PERSON>pi(configuration);

let body: object; //

const { status, data } = await apiInstance.monitorControllerCreate(
    body
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **body** | **object**|  | |


### Return type

void (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: Not defined


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**201** |  |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **monitorControllerFindAll**
> monitorControllerFindAll()


### Example

```typescript
import {
    MonitorApi,
    Configuration
} from './api';

const configuration = new Configuration();
const apiInstance = new MonitorApi(configuration);

const { status, data } = await apiInstance.monitorControllerFindAll();
```

### Parameters
This endpoint does not have any parameters.


### Return type

void (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**200** |  |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **monitorControllerFindOne**
> monitorControllerFindOne()


### Example

```typescript
import {
    MonitorApi,
    Configuration
} from './api';

const configuration = new Configuration();
const apiInstance = new MonitorApi(configuration);

let id: string; // (default to undefined)

const { status, data } = await apiInstance.monitorControllerFindOne(
    id
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **id** | [**string**] |  | defaults to undefined|


### Return type

void (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**200** |  |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **monitorControllerRemove**
> monitorControllerRemove()


### Example

```typescript
import {
    MonitorApi,
    Configuration
} from './api';

const configuration = new Configuration();
const apiInstance = new MonitorApi(configuration);

let id: string; // (default to undefined)

const { status, data } = await apiInstance.monitorControllerRemove(
    id
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **id** | [**string**] |  | defaults to undefined|


### Return type

void (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**200** |  |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **monitorControllerUpdate**
> monitorControllerUpdate(body)


### Example

```typescript
import {
    MonitorApi,
    Configuration
} from './api';

const configuration = new Configuration();
const apiInstance = new MonitorApi(configuration);

let id: string; // (default to undefined)
let body: object; //

const { status, data } = await apiInstance.monitorControllerUpdate(
    id,
    body
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **body** | **object**|  | |
| **id** | [**string**] |  | defaults to undefined|


### Return type

void (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: Not defined


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**200** |  |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

