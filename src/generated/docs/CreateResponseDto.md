# CreateResponseDto


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**submissionId** | **number** |  | [default to undefined]
**questionId** | **number** |  | [default to undefined]
**responseId** | **number** |  | [default to undefined]
**selectedOptionId** | **number** |  | [default to undefined]
**answerText** | **string** |  | [optional] [default to undefined]
**selectedOptionIds** | **Array&lt;number&gt;** | Array of selected option IDs for CHECKBOX type questions | [optional] [default to undefined]

## Example

```typescript
import { CreateResponseDto } from './api';

const instance: CreateResponseDto = {
    submissionId,
    questionId,
    responseId,
    selectedOptionId,
    answerText,
    selectedOptionIds,
};
```

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)
