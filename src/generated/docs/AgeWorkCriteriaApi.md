# AgeWorkCriteriaApi

All URIs are relative to *http://localhost*

|Method | HTTP request | Description|
|------------- | ------------- | -------------|
|[**ageWorkCriteriaControllerCreate**](#ageworkcriteriacontrollercreate) | **POST** /age-work-criteria | Create a new age work criteria|
|[**ageWorkCriteriaControllerFindAgeWorksByCriteria**](#ageworkcriteriacontrollerfindageworksbycriteria) | **GET** /age-work-criteria/{id}/age-works | Get all age works by criteria ID|
|[**ageWorkCriteriaControllerFindAll**](#ageworkcriteriacontrollerfindall) | **GET** /age-work-criteria | Get all age work criteria|
|[**ageWorkCriteriaControllerFindOne**](#ageworkcriteriacontrollerfindone) | **GET** /age-work-criteria/{id} | Get an age work criteria by ID|
|[**ageWorkCriteriaControllerRemove**](#ageworkcriteriacontrollerremove) | **DELETE** /age-work-criteria/{id} | Delete an age work criteria|
|[**ageWorkCriteriaControllerUpdate**](#ageworkcriteriacontrollerupdate) | **PATCH** /age-work-criteria/{id} | Update an age work criteria|

# **ageWorkCriteriaControllerCreate**
> ageWorkCriteriaControllerCreate(createAgeWorkCriteriaDto)


### Example

```typescript
import {
    AgeWorkCriteriaApi,
    Configuration,
    CreateAgeWorkCriteriaDto
} from './api';

const configuration = new Configuration();
const apiInstance = new AgeWorkCriteriaApi(configuration);

let createAgeWorkCriteriaDto: CreateAgeWorkCriteriaDto; //

const { status, data } = await apiInstance.ageWorkCriteriaControllerCreate(
    createAgeWorkCriteriaDto
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **createAgeWorkCriteriaDto** | **CreateAgeWorkCriteriaDto**|  | |


### Return type

void (empty response body)

### Authorization

[bearer](../README.md#bearer)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: Not defined


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**201** | Age work criteria successfully created |  -  |
|**400** | Bad request |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **ageWorkCriteriaControllerFindAgeWorksByCriteria**
> ageWorkCriteriaControllerFindAgeWorksByCriteria()


### Example

```typescript
import {
    AgeWorkCriteriaApi,
    Configuration
} from './api';

const configuration = new Configuration();
const apiInstance = new AgeWorkCriteriaApi(configuration);

let id: number; //Age work criteria ID (default to undefined)
let limit: any; //Number of items per page (default to undefined)
let page: any; //Page number to retrieve (default to undefined)
let sortBy: any; //Field to sort by (e.g., id, name, email) (optional) (default to undefined)
let order: any; //Sort order (ASC or DESC) (optional) (default to undefined)
let search: any; //Search term to filter results (optional) (default to undefined)

const { status, data } = await apiInstance.ageWorkCriteriaControllerFindAgeWorksByCriteria(
    id,
    limit,
    page,
    sortBy,
    order,
    search
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **id** | [**number**] | Age work criteria ID | defaults to undefined|
| **limit** | **any** | Number of items per page | defaults to undefined|
| **page** | **any** | Page number to retrieve | defaults to undefined|
| **sortBy** | **any** | Field to sort by (e.g., id, name, email) | (optional) defaults to undefined|
| **order** | **any** | Sort order (ASC or DESC) | (optional) defaults to undefined|
| **search** | **any** | Search term to filter results | (optional) defaults to undefined|


### Return type

void (empty response body)

### Authorization

[bearer](../README.md#bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**200** | Return all age works for the criteria |  -  |
|**404** | Age work criteria not found |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **ageWorkCriteriaControllerFindAll**
> ageWorkCriteriaControllerFindAll()


### Example

```typescript
import {
    AgeWorkCriteriaApi,
    Configuration
} from './api';

const configuration = new Configuration();
const apiInstance = new AgeWorkCriteriaApi(configuration);

let limit: any; //Number of items per page (default to undefined)
let page: any; //Page number to retrieve (default to undefined)
let sortBy: any; //Field to sort by (e.g., id, name, email) (optional) (default to undefined)
let order: any; //Sort order (ASC or DESC) (optional) (default to undefined)
let search: any; //Search term to filter results (optional) (default to undefined)

const { status, data } = await apiInstance.ageWorkCriteriaControllerFindAll(
    limit,
    page,
    sortBy,
    order,
    search
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **limit** | **any** | Number of items per page | defaults to undefined|
| **page** | **any** | Page number to retrieve | defaults to undefined|
| **sortBy** | **any** | Field to sort by (e.g., id, name, email) | (optional) defaults to undefined|
| **order** | **any** | Sort order (ASC or DESC) | (optional) defaults to undefined|
| **search** | **any** | Search term to filter results | (optional) defaults to undefined|


### Return type

void (empty response body)

### Authorization

[bearer](../README.md#bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**200** | Return all age work criteria |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **ageWorkCriteriaControllerFindOne**
> ageWorkCriteriaControllerFindOne()


### Example

```typescript
import {
    AgeWorkCriteriaApi,
    Configuration
} from './api';

const configuration = new Configuration();
const apiInstance = new AgeWorkCriteriaApi(configuration);

let id: number; //Age work criteria ID (default to undefined)

const { status, data } = await apiInstance.ageWorkCriteriaControllerFindOne(
    id
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **id** | [**number**] | Age work criteria ID | defaults to undefined|


### Return type

void (empty response body)

### Authorization

[bearer](../README.md#bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**200** | Return the age work criteria |  -  |
|**404** | Age work criteria not found |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **ageWorkCriteriaControllerRemove**
> ageWorkCriteriaControllerRemove()


### Example

```typescript
import {
    AgeWorkCriteriaApi,
    Configuration
} from './api';

const configuration = new Configuration();
const apiInstance = new AgeWorkCriteriaApi(configuration);

let id: number; //Age work criteria ID (default to undefined)

const { status, data } = await apiInstance.ageWorkCriteriaControllerRemove(
    id
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **id** | [**number**] | Age work criteria ID | defaults to undefined|


### Return type

void (empty response body)

### Authorization

[bearer](../README.md#bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**200** | Age work criteria successfully deleted |  -  |
|**404** | Age work criteria not found |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **ageWorkCriteriaControllerUpdate**
> ageWorkCriteriaControllerUpdate(updateAgeWorkCriteriaDto)


### Example

```typescript
import {
    AgeWorkCriteriaApi,
    Configuration,
    UpdateAgeWorkCriteriaDto
} from './api';

const configuration = new Configuration();
const apiInstance = new AgeWorkCriteriaApi(configuration);

let id: number; //Age work criteria ID (default to undefined)
let updateAgeWorkCriteriaDto: UpdateAgeWorkCriteriaDto; //

const { status, data } = await apiInstance.ageWorkCriteriaControllerUpdate(
    id,
    updateAgeWorkCriteriaDto
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **updateAgeWorkCriteriaDto** | **UpdateAgeWorkCriteriaDto**|  | |
| **id** | [**number**] | Age work criteria ID | defaults to undefined|


### Return type

void (empty response body)

### Authorization

[bearer](../README.md#bearer)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: Not defined


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**200** | Age work criteria successfully updated |  -  |
|**404** | Age work criteria not found |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

