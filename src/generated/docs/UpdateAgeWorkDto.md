# UpdateAgeWorkDto


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**name** | **string** | Name of the age work range | [optional] [default to undefined]
**startYear** | **number** | Starting year of work experience | [optional] [default to undefined]
**endYear** | **number** | Ending year of work experience | [optional] [default to undefined]
**ageWorkCriteriaId** | **number** | ID of the age work criteria plan this belongs to | [optional] [default to undefined]

## Example

```typescript
import { UpdateAgeWorkDto } from './api';

const instance: UpdateAgeWorkDto = {
    name,
    startYear,
    endYear,
    ageWorkCriteriaId,
};
```

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)
