# CreateDevelopmentPlanDto


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**name** | **string** |  | [default to undefined]
**description** | **string** |  | [default to undefined]
**isActive** | **boolean** |  | [optional] [default to undefined]
**facId** | **number** |  | [optional] [default to undefined]
**ageWorkCriteriaId** | **number** |  | [default to undefined]

## Example

```typescript
import { CreateDevelopmentPlanDto } from './api';

const instance: CreateDevelopmentPlanDto = {
    name,
    description,
    isActive,
    facId,
    ageWorkCriteriaId,
};
```

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)
