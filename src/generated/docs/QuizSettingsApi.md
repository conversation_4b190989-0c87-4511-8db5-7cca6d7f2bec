# QuizSettingsApi

All URIs are relative to *http://localhost*

|Method | HTTP request | Description|
|------------- | ------------- | -------------|
|[**quizSettingsControllerGetQuizSettings**](#quizsettingscontrollergetquizsettings) | **GET** /assessments/{id}/settings | Get quiz settings|
|[**quizSettingsControllerUpdateQuizSettings**](#quizsettingscontrollerupdatequizsettings) | **PATCH** /assessments/{id}/settings | Update quiz settings|

# **quizSettingsControllerGetQuizSettings**
> object quizSettingsControllerGetQuizSettings()

ดึงการตั้งค่าของแบบทดสอบ

### Example

```typescript
import {
    QuizSettingsApi,
    Configuration
} from './api';

const configuration = new Configuration();
const apiInstance = new QuizSettingsApi(configuration);

let id: number; //รหัสของแบบทดสอบ (default to undefined)

const { status, data } = await apiInstance.quizSettingsControllerGetQuizSettings(
    id
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **id** | [**number**] | รหัสของแบบทดสอบ | defaults to undefined|


### Return type

**object**

### Authorization

[bearer](../README.md#bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**200** | ข้อมูลการตั้งค่าแบบทดสอบ |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **quizSettingsControllerUpdateQuizSettings**
> object quizSettingsControllerUpdateQuizSettings(updateQuizSettingsDto)

อัปเดตการตั้งค่าของแบบทดสอบ (เฉพาะฟิลด์ที่เกี่ยวข้องกับการตั้งค่า)

### Example

```typescript
import {
    QuizSettingsApi,
    Configuration,
    UpdateQuizSettingsDto
} from './api';

const configuration = new Configuration();
const apiInstance = new QuizSettingsApi(configuration);

let id: number; //รหัสของแบบทดสอบ (default to undefined)
let updateQuizSettingsDto: UpdateQuizSettingsDto; //

const { status, data } = await apiInstance.quizSettingsControllerUpdateQuizSettings(
    id,
    updateQuizSettingsDto
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **updateQuizSettingsDto** | **UpdateQuizSettingsDto**|  | |
| **id** | [**number**] | รหัสของแบบทดสอบ | defaults to undefined|


### Return type

**object**

### Authorization

[bearer](../README.md#bearer)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**200** | การตั้งค่าแบบทดสอบถูกอัปเดตเรียบร้อยแล้ว |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

