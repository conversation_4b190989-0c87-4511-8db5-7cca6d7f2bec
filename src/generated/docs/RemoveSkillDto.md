# RemoveSkillDto


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**typeId** | **number** |  | [default to undefined]
**skillId** | **number** |  | [default to undefined]

## Example

```typescript
import { RemoveSkillDto } from './api';

const instance: RemoveSkillDto = {
    typeId,
    skillId,
};
```

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)
