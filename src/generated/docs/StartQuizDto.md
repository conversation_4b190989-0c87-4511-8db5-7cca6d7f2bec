# StartQuizDto


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**userId** | **number** | ID of the user starting the assessment | [default to undefined]
**linkUrl** | **string** | ID of the assessment being started | [default to undefined]

## Example

```typescript
import { StartQuizDto } from './api';

const instance: StartQuizDto = {
    userId,
    linkUrl,
};
```

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)
