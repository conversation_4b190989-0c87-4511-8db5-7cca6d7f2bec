# CreateAgeWorkDto


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**name** | **string** | Name of the age work range | [default to undefined]
**startYear** | **number** | Starting year of work experience | [default to undefined]
**endYear** | **number** | Ending year of work experience | [default to undefined]
**ageWorkCriteriaId** | **number** | ID of the age work criteria plan this belongs to | [default to undefined]

## Example

```typescript
import { CreateAgeWorkDto } from './api';

const instance: CreateAgeWorkDto = {
    name,
    startYear,
    endYear,
    ageWorkCriteriaId,
};
```

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)
