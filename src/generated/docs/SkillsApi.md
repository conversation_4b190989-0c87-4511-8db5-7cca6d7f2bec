# SkillsApi

All URIs are relative to *http://localhost*

|Method | HTTP request | Description|
|------------- | ------------- | -------------|
|[**skillsControllerCreate**](#skillscontrollercreate) | **POST** /skills | |
|[**skillsControllerFindAll**](#skillscontrollerfindall) | **GET** /skills | Get all skills (with pagination)|
|[**skillsControllerFindOne**](#skillscontrollerfindone) | **GET** /skills/{id} | Get skill by ID|
|[**skillsControllerRemove**](#skillscontrollerremove) | **DELETE** /skills/{id} | Delete a skill|
|[**skillsControllerUpdate**](#skillscontrollerupdate) | **PATCH** /skills/{id} | Update a skill|

# **skillsControllerCreate**
> skillsControllerCreate(createSkillDto)


### Example

```typescript
import {
    SkillsApi,
    Configuration,
    CreateSkillDto
} from './api';

const configuration = new Configuration();
const apiInstance = new SkillsApi(configuration);

let createSkillDto: CreateSkillDto; //

const { status, data } = await apiInstance.skillsControllerCreate(
    createSkillDto
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **createSkillDto** | **CreateSkillDto**|  | |


### Return type

void (empty response body)

### Authorization

[bearer](../README.md#bearer)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: Not defined


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**201** |  |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **skillsControllerFindAll**
> skillsControllerFindAll()


### Example

```typescript
import {
    SkillsApi,
    Configuration
} from './api';

const configuration = new Configuration();
const apiInstance = new SkillsApi(configuration);

let limit: number; //Number of items per page (default to 10)
let page: number; //Page number to retrieve (default to 1)
let sortBy: string; //Field to sort by (e.g., id, name, email) (optional) (default to 'id')
let order: 'ASC' | 'DESC'; //Sort order (ASC or DESC) (optional) (default to 'ASC')
let careerType: string; //Filter by career type (optional) (default to undefined)
let search: string; //Search term to filter results (optional) (default to undefined)
let : 'ทั่วไปบุคลากร' | 'ทั่วไปผู้บริหาร' | 'เฉพาะด้านบริหาร' | 'เฉพาะด้านวิชาการ' | 'เฉพาะสายสนับสนุน' | 'ตำแหน่ง'; // (optional) (default to undefined)

const { status, data } = await apiInstance.skillsControllerFindAll(
    limit,
    page,
    sortBy,
    order,
    careerType,
    search,
    
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **limit** | [**number**] | Number of items per page | defaults to 10|
| **page** | [**number**] | Page number to retrieve | defaults to 1|
| **sortBy** | [**string**] | Field to sort by (e.g., id, name, email) | (optional) defaults to 'id'|
| **order** | [**&#39;ASC&#39; | &#39;DESC&#39;**]**Array<&#39;ASC&#39; &#124; &#39;DESC&#39;>** | Sort order (ASC or DESC) | (optional) defaults to 'ASC'|
| **careerType** | [**string**] | Filter by career type | (optional) defaults to undefined|
| **search** | [**string**] | Search term to filter results | (optional) defaults to undefined|
| **** | [**&#39;ทั่วไปบุคลากร&#39; | &#39;ทั่วไปผู้บริหาร&#39; | &#39;เฉพาะด้านบริหาร&#39; | &#39;เฉพาะด้านวิชาการ&#39; | &#39;เฉพาะสายสนับสนุน&#39; | &#39;ตำแหน่ง&#39;**]**Array<&#39;ทั่วไปบุคลากร&#39; &#124; &#39;ทั่วไปผู้บริหาร&#39; &#124; &#39;เฉพาะด้านบริหาร&#39; &#124; &#39;เฉพาะด้านวิชาการ&#39; &#124; &#39;เฉพาะสายสนับสนุน&#39; &#124; &#39;ตำแหน่ง&#39;>** |  | (optional) defaults to undefined|


### Return type

void (empty response body)

### Authorization

[bearer](../README.md#bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**200** |  |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **skillsControllerFindOne**
> skillsControllerFindOne()


### Example

```typescript
import {
    SkillsApi,
    Configuration
} from './api';

const configuration = new Configuration();
const apiInstance = new SkillsApi(configuration);

let id: string; //Skill ID (default to undefined)

const { status, data } = await apiInstance.skillsControllerFindOne(
    id
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **id** | [**string**] | Skill ID | defaults to undefined|


### Return type

void (empty response body)

### Authorization

[bearer](../README.md#bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**200** |  |  -  |
|**404** | Skill not found |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **skillsControllerRemove**
> skillsControllerRemove()


### Example

```typescript
import {
    SkillsApi,
    Configuration
} from './api';

const configuration = new Configuration();
const apiInstance = new SkillsApi(configuration);

let id: string; //Skill ID (default to undefined)

const { status, data } = await apiInstance.skillsControllerRemove(
    id
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **id** | [**string**] | Skill ID | defaults to undefined|


### Return type

void (empty response body)

### Authorization

[bearer](../README.md#bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**204** | Deleted successfully |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **skillsControllerUpdate**
> skillsControllerUpdate(body)


### Example

```typescript
import {
    SkillsApi,
    Configuration
} from './api';

const configuration = new Configuration();
const apiInstance = new SkillsApi(configuration);

let id: string; //Skill ID (default to undefined)
let body: object; //

const { status, data } = await apiInstance.skillsControllerUpdate(
    id,
    body
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **body** | **object**|  | |
| **id** | [**string**] | Skill ID | defaults to undefined|


### Return type

void (empty response body)

### Authorization

[bearer](../README.md#bearer)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: Not defined


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**200** |  |  -  |
|**404** | Skill not found |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

