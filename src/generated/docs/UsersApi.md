# UsersApi

All URIs are relative to *http://localhost*

|Method | HTTP request | Description|
|------------- | ------------- | -------------|
|[**usersControllerCreate**](#userscontrollercreate) | **POST** /users | Create a new user|
|[**usersControllerFindAll**](#userscontrollerfindall) | **GET** /users | Get all users|
|[**usersControllerFindOne**](#userscontrollerfindone) | **GET** /users/{id} | Get a user by ID|
|[**usersControllerGetFacultiesUser**](#userscontrollergetfacultiesuser) | **GET** /users/{id}/facultiesUser | Get all faculties of User|
|[**usersControllerGetFacultiesWithUserStatus**](#userscontrollergetfacultieswithuserstatus) | **GET** /users/{id}/faculties | Get all faculties with user status for dialog|
|[**usersControllerRemove**](#userscontrollerremove) | **DELETE** /users/{id} | Delete a user|
|[**usersControllerUpdate**](#userscontrollerupdate) | **PATCH** /users/{id} | Update user information, password, or roles|
|[**usersControllerUpdateUserFaculties**](#userscontrollerupdateuserfaculties) | **PATCH** /users/{id}/faculties | Update user faculties based on checkbox selection|
|[**usersControllerUpdateUserFacultiesRoles**](#userscontrollerupdateuserfacultiesroles) | **PATCH** /users/{id}/roles | Update user roles based on checkbox selection|

# **usersControllerCreate**
> usersControllerCreate(body)


### Example

```typescript
import {
    UsersApi,
    Configuration
} from './api';

const configuration = new Configuration();
const apiInstance = new UsersApi(configuration);

let body: object; //

const { status, data } = await apiInstance.usersControllerCreate(
    body
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **body** | **object**|  | |


### Return type

void (empty response body)

### Authorization

[bearer](../README.md#bearer)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: Not defined


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**201** | User successfully created |  -  |
|**400** | Bad request |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **usersControllerFindAll**
> usersControllerFindAll()


### Example

```typescript
import {
    UsersApi,
    Configuration
} from './api';

const configuration = new Configuration();
const apiInstance = new UsersApi(configuration);

let limit: any; //Number of items per page (default to undefined)
let page: any; //Page number to retrieve (default to undefined)
let facultyFilter: string; //Filter users by faculty ID (optional) (default to undefined)
let roleFilter: string; //Filter users by role ID (optional) (default to undefined)
let sortBy: any; //Field to sort by (e.g., id, name, email) (optional) (default to undefined)
let order: any; //Sort order (ASC or DESC) (optional) (default to undefined)
let search: any; //Search term to filter results (optional) (default to undefined)

const { status, data } = await apiInstance.usersControllerFindAll(
    limit,
    page,
    facultyFilter,
    roleFilter,
    sortBy,
    order,
    search
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **limit** | **any** | Number of items per page | defaults to undefined|
| **page** | **any** | Page number to retrieve | defaults to undefined|
| **facultyFilter** | [**string**] | Filter users by faculty ID | (optional) defaults to undefined|
| **roleFilter** | [**string**] | Filter users by role ID | (optional) defaults to undefined|
| **sortBy** | **any** | Field to sort by (e.g., id, name, email) | (optional) defaults to undefined|
| **order** | **any** | Sort order (ASC or DESC) | (optional) defaults to undefined|
| **search** | **any** | Search term to filter results | (optional) defaults to undefined|


### Return type

void (empty response body)

### Authorization

[bearer](../README.md#bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**200** | Return all users |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **usersControllerFindOne**
> usersControllerFindOne()


### Example

```typescript
import {
    UsersApi,
    Configuration
} from './api';

const configuration = new Configuration();
const apiInstance = new UsersApi(configuration);

let id: string; //User ID (default to undefined)

const { status, data } = await apiInstance.usersControllerFindOne(
    id
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **id** | [**string**] | User ID | defaults to undefined|


### Return type

void (empty response body)

### Authorization

[bearer](../README.md#bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**200** | Return the user |  -  |
|**404** | User not found |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **usersControllerGetFacultiesUser**
> usersControllerGetFacultiesUser()


### Example

```typescript
import {
    UsersApi,
    Configuration
} from './api';

const configuration = new Configuration();
const apiInstance = new UsersApi(configuration);

let id: string; //User ID (default to undefined)
let limit: any; //Number of items per page (default to undefined)
let page: any; //Page number to retrieve (default to undefined)
let sortBy: any; //Field to sort by (e.g., id, name, email) (optional) (default to undefined)
let order: any; //Sort order (ASC or DESC) (optional) (default to undefined)
let search: any; //Search term to filter results (optional) (default to undefined)

const { status, data } = await apiInstance.usersControllerGetFacultiesUser(
    id,
    limit,
    page,
    sortBy,
    order,
    search
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **id** | [**string**] | User ID | defaults to undefined|
| **limit** | **any** | Number of items per page | defaults to undefined|
| **page** | **any** | Page number to retrieve | defaults to undefined|
| **sortBy** | **any** | Field to sort by (e.g., id, name, email) | (optional) defaults to undefined|
| **order** | **any** | Sort order (ASC or DESC) | (optional) defaults to undefined|
| **search** | **any** | Search term to filter results | (optional) defaults to undefined|


### Return type

void (empty response body)

### Authorization

[bearer](../README.md#bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**200** | Return all faculties with user status |  -  |
|**404** | User not found |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **usersControllerGetFacultiesWithUserStatus**
> usersControllerGetFacultiesWithUserStatus()


### Example

```typescript
import {
    UsersApi,
    Configuration
} from './api';

const configuration = new Configuration();
const apiInstance = new UsersApi(configuration);

let id: string; //User ID (default to undefined)
let limit: any; //Number of items per page (default to undefined)
let page: any; //Page number to retrieve (default to undefined)
let sortBy: any; //Field to sort by (e.g., id, name, email) (optional) (default to undefined)
let order: any; //Sort order (ASC or DESC) (optional) (default to undefined)
let search: any; //Search term to filter results (optional) (default to undefined)

const { status, data } = await apiInstance.usersControllerGetFacultiesWithUserStatus(
    id,
    limit,
    page,
    sortBy,
    order,
    search
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **id** | [**string**] | User ID | defaults to undefined|
| **limit** | **any** | Number of items per page | defaults to undefined|
| **page** | **any** | Page number to retrieve | defaults to undefined|
| **sortBy** | **any** | Field to sort by (e.g., id, name, email) | (optional) defaults to undefined|
| **order** | **any** | Sort order (ASC or DESC) | (optional) defaults to undefined|
| **search** | **any** | Search term to filter results | (optional) defaults to undefined|


### Return type

void (empty response body)

### Authorization

[bearer](../README.md#bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**200** | Return all faculties with user status |  -  |
|**404** | User not found |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **usersControllerRemove**
> usersControllerRemove()


### Example

```typescript
import {
    UsersApi,
    Configuration
} from './api';

const configuration = new Configuration();
const apiInstance = new UsersApi(configuration);

let id: string; //User ID (default to undefined)

const { status, data } = await apiInstance.usersControllerRemove(
    id
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **id** | [**string**] | User ID | defaults to undefined|


### Return type

void (empty response body)

### Authorization

[bearer](../README.md#bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**204** | User successfully deleted |  -  |
|**404** | User not found |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **usersControllerUpdate**
> usersControllerUpdate(usersControllerUpdateRequest)


### Example

```typescript
import {
    UsersApi,
    Configuration,
    UsersControllerUpdateRequest
} from './api';

const configuration = new Configuration();
const apiInstance = new UsersApi(configuration);

let id: string; //User ID (default to undefined)
let usersControllerUpdateRequest: UsersControllerUpdateRequest; //

const { status, data } = await apiInstance.usersControllerUpdate(
    id,
    usersControllerUpdateRequest
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **usersControllerUpdateRequest** | **UsersControllerUpdateRequest**|  | |
| **id** | [**string**] | User ID | defaults to undefined|


### Return type

void (empty response body)

### Authorization

[bearer](../README.md#bearer)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: Not defined


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**200** | User successfully updated |  -  |
|**404** | User not found |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **usersControllerUpdateUserFaculties**
> usersControllerUpdateUserFaculties(body)


### Example

```typescript
import {
    UsersApi,
    Configuration
} from './api';

const configuration = new Configuration();
const apiInstance = new UsersApi(configuration);

let id: string; //User ID (default to undefined)
let body: object; //

const { status, data } = await apiInstance.usersControllerUpdateUserFaculties(
    id,
    body
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **body** | **object**|  | |
| **id** | [**string**] | User ID | defaults to undefined|


### Return type

void (empty response body)

### Authorization

[bearer](../README.md#bearer)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: Not defined


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**200** | User faculties successfully updated |  -  |
|**400** | Bad request |  -  |
|**404** | User not found |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **usersControllerUpdateUserFacultiesRoles**
> usersControllerUpdateUserFacultiesRoles(body)


### Example

```typescript
import {
    UsersApi,
    Configuration
} from './api';

const configuration = new Configuration();
const apiInstance = new UsersApi(configuration);

let id: string; //User ID (default to undefined)
let body: object; //

const { status, data } = await apiInstance.usersControllerUpdateUserFacultiesRoles(
    id,
    body
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **body** | **object**|  | |
| **id** | [**string**] | User ID | defaults to undefined|


### Return type

void (empty response body)

### Authorization

[bearer](../README.md#bearer)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: Not defined


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**200** | User faculties roles successfully updated |  -  |
|**400** | Bad request |  -  |
|**404** | User not found |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

