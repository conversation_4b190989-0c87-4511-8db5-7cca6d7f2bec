# UpdateQuizSettingsDto


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**startAt** | **string** | วันที่เริ่มต้น | [optional] [default to undefined]
**endAt** | **string** | วันที่สิ้นสุด | [optional] [default to undefined]
**timeout** | **number** | เวลาที่อนุญาต (วินาที) | [optional] [default to undefined]
**submitLimit** | **number** | จำนวนครั้งที่อนุญาตให้ส่งแบบประเมิน | [optional] [default to undefined]
**passRatio** | **number** | อัตราส่วนการผ่าน (เช่น 0.5, 0.8) | [optional] [default to undefined]
**isPrototype** | **boolean** | เป็นต้นแบบหรือไม่ | [optional] [default to undefined]

## Example

```typescript
import { UpdateQuizSettingsDto } from './api';

const instance: UpdateQuizSettingsDto = {
    startAt,
    endAt,
    timeout,
    submitLimit,
    passRatio,
    isPrototype,
};
```

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)
