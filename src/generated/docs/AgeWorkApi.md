# AgeWorkApi

All URIs are relative to *http://localhost*

|Method | HTTP request | Description|
|------------- | ------------- | -------------|
|[**ageWorkControllerCreate**](#ageworkcontrollercreate) | **POST** /age-works | Create a new age work|
|[**ageWorkControllerFindOne**](#ageworkcontrollerfindone) | **GET** /age-works/{id} | Get an age work by ID|
|[**ageWorkControllerRemove**](#ageworkcontrollerremove) | **DELETE** /age-works/{id} | Delete an age work|
|[**ageWorkControllerUpdate**](#ageworkcontrollerupdate) | **PATCH** /age-works/{id} | Update an age work|

# **ageWorkControllerCreate**
> ageWorkControllerCreate(createAgeWorkDto)


### Example

```typescript
import {
    AgeWorkApi,
    Configuration,
    CreateAgeWorkDto
} from './api';

const configuration = new Configuration();
const apiInstance = new AgeWorkApi(configuration);

let createAgeWorkDto: CreateAgeWorkDto; //

const { status, data } = await apiInstance.ageWorkControllerCreate(
    createAgeWorkDto
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **createAgeWorkDto** | **CreateAgeWorkDto**|  | |


### Return type

void (empty response body)

### Authorization

[bearer](../README.md#bearer)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: Not defined


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**201** | Age work successfully created |  -  |
|**400** | Bad request |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **ageWorkControllerFindOne**
> ageWorkControllerFindOne()


### Example

```typescript
import {
    AgeWorkApi,
    Configuration
} from './api';

const configuration = new Configuration();
const apiInstance = new AgeWorkApi(configuration);

let id: number; //Age work ID (default to undefined)

const { status, data } = await apiInstance.ageWorkControllerFindOne(
    id
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **id** | [**number**] | Age work ID | defaults to undefined|


### Return type

void (empty response body)

### Authorization

[bearer](../README.md#bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**200** | Return the age work |  -  |
|**404** | Age work not found |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **ageWorkControllerRemove**
> ageWorkControllerRemove()


### Example

```typescript
import {
    AgeWorkApi,
    Configuration
} from './api';

const configuration = new Configuration();
const apiInstance = new AgeWorkApi(configuration);

let id: number; //Age work ID (default to undefined)

const { status, data } = await apiInstance.ageWorkControllerRemove(
    id
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **id** | [**number**] | Age work ID | defaults to undefined|


### Return type

void (empty response body)

### Authorization

[bearer](../README.md#bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**200** | Age work successfully deleted |  -  |
|**404** | Age work not found |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **ageWorkControllerUpdate**
> ageWorkControllerUpdate(updateAgeWorkDto)


### Example

```typescript
import {
    AgeWorkApi,
    Configuration,
    UpdateAgeWorkDto
} from './api';

const configuration = new Configuration();
const apiInstance = new AgeWorkApi(configuration);

let id: number; //Age work ID (default to undefined)
let updateAgeWorkDto: UpdateAgeWorkDto; //

const { status, data } = await apiInstance.ageWorkControllerUpdate(
    id,
    updateAgeWorkDto
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **updateAgeWorkDto** | **UpdateAgeWorkDto**|  | |
| **id** | [**number**] | Age work ID | defaults to undefined|


### Return type

void (empty response body)

### Authorization

[bearer](../README.md#bearer)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: Not defined


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**200** | Age work successfully updated |  -  |
|**404** | Age work not found |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

