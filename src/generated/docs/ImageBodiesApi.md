# ImageBodiesApi

All URIs are relative to *http://localhost*

|Method | HTTP request | Description|
|------------- | ------------- | -------------|
|[**imageBodiesControllerCreate**](#imagebodiescontrollercreate) | **POST** /image-bodies | สร้าง Image Bodyใหม่|
|[**imageBodiesControllerFindAll**](#imagebodiescontrollerfindall) | **GET** /image-bodies | |
|[**imageBodiesControllerFindOne**](#imagebodiescontrollerfindone) | **GET** /image-bodies/{id} | |
|[**imageBodiesControllerRemove**](#imagebodiescontrollerremove) | **DELETE** /image-bodies/{id} | |
|[**imageBodiesControllerUpdate**](#imagebodiescontrollerupdate) | **PATCH** /image-bodies/{id} | อัปเดตImage Body|

# **imageBodiesControllerCreate**
> imageBodiesControllerCreate()

สร้าง Image Body ใหม่สำหรับ (Evaluate)

### Example

```typescript
import {
    ImageBodiesApi,
    Configuration
} from './api';

const configuration = new Configuration();
const apiInstance = new ImageBodiesApi(configuration);

let itemBlockId: number; // (default to undefined)
let imageText: string; // (optional) (default to undefined)
let imageWidth: number; // (optional) (default to undefined)
let imageHeight: number; // (optional) (default to undefined)
let imagePath: File; // (optional) (default to undefined)

const { status, data } = await apiInstance.imageBodiesControllerCreate(
    itemBlockId,
    imageText,
    imageWidth,
    imageHeight,
    imagePath
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **itemBlockId** | [**number**] |  | defaults to undefined|
| **imageText** | [**string**] |  | (optional) defaults to undefined|
| **imageWidth** | [**number**] |  | (optional) defaults to undefined|
| **imageHeight** | [**number**] |  | (optional) defaults to undefined|
| **imagePath** | [**File**] |  | (optional) defaults to undefined|


### Return type

void (empty response body)

### Authorization

[bearer](../README.md#bearer)

### HTTP request headers

 - **Content-Type**: multipart/form-data
 - **Accept**: Not defined


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**201** |  |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **imageBodiesControllerFindAll**
> imageBodiesControllerFindAll()


### Example

```typescript
import {
    ImageBodiesApi,
    Configuration
} from './api';

const configuration = new Configuration();
const apiInstance = new ImageBodiesApi(configuration);

const { status, data } = await apiInstance.imageBodiesControllerFindAll();
```

### Parameters
This endpoint does not have any parameters.


### Return type

void (empty response body)

### Authorization

[bearer](../README.md#bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**200** |  |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **imageBodiesControllerFindOne**
> imageBodiesControllerFindOne()


### Example

```typescript
import {
    ImageBodiesApi,
    Configuration
} from './api';

const configuration = new Configuration();
const apiInstance = new ImageBodiesApi(configuration);

let id: string; // (default to undefined)

const { status, data } = await apiInstance.imageBodiesControllerFindOne(
    id
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **id** | [**string**] |  | defaults to undefined|


### Return type

void (empty response body)

### Authorization

[bearer](../README.md#bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**200** |  |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **imageBodiesControllerRemove**
> imageBodiesControllerRemove()


### Example

```typescript
import {
    ImageBodiesApi,
    Configuration
} from './api';

const configuration = new Configuration();
const apiInstance = new ImageBodiesApi(configuration);

let id: number; // (default to undefined)

const { status, data } = await apiInstance.imageBodiesControllerRemove(
    id
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **id** | [**number**] |  | defaults to undefined|


### Return type

void (empty response body)

### Authorization

[bearer](../README.md#bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**200** |  |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **imageBodiesControllerUpdate**
> imageBodiesControllerUpdate()

อัปเดตImage Body (Evaluate) ตาม template ที่กำหนด

### Example

```typescript
import {
    ImageBodiesApi,
    Configuration
} from './api';

const configuration = new Configuration();
const apiInstance = new ImageBodiesApi(configuration);

let id: string; // (default to undefined)
let imageText: string; //Caption or text associated with the image (optional) (default to undefined)
let imagePath: string; //Path to the image file (optional) (default to undefined)
let itemBlockId: number; //ID of the item block this image belongs to (optional) (default to undefined)
let imageWidth: number; //Width of the image in pixels (optional) (default to undefined)
let imageHeight: number; //Height of the image in pixels (optional) (default to undefined)

const { status, data } = await apiInstance.imageBodiesControllerUpdate(
    id,
    imageText,
    imagePath,
    itemBlockId,
    imageWidth,
    imageHeight
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **id** | [**string**] |  | defaults to undefined|
| **imageText** | [**string**] | Caption or text associated with the image | (optional) defaults to undefined|
| **imagePath** | [**string**] | Path to the image file | (optional) defaults to undefined|
| **itemBlockId** | [**number**] | ID of the item block this image belongs to | (optional) defaults to undefined|
| **imageWidth** | [**number**] | Width of the image in pixels | (optional) defaults to undefined|
| **imageHeight** | [**number**] | Height of the image in pixels | (optional) defaults to undefined|


### Return type

void (empty response body)

### Authorization

[bearer](../README.md#bearer)

### HTTP request headers

 - **Content-Type**: multipart/form-data
 - **Accept**: Not defined


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**200** |  |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

