# UploadFileDto


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**path** | **string** | File path where the file is stored | [default to undefined]
**fileName** | **string** | Original file name | [default to undefined]
**fileType** | **string** | File MIME type | [default to undefined]
**qrVerify** | **string** | QR code verification string (optional) | [optional] [default to undefined]

## Example

```typescript
import { UploadFileDto } from './api';

const instance: UploadFileDto = {
    path,
    fileName,
    fileType,
    qrVerify,
};
```

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)
