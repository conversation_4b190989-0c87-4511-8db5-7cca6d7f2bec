# AuthenticationApi

All URIs are relative to *http://localhost*

|Method | HTTP request | Description|
|------------- | ------------- | -------------|
|[**authControllerSignIn**](#authcontrollersignin) | **POST** /auth/loginBuu | Legacy encrypted login (paused for now)|
|[**authControllerSimpleSignIn**](#authcontrollersimplesignin) | **POST** /auth/login | Simple login with username and password|

# **authControllerSignIn**
> authControllerSignIn(authControllerSignInRequest)


### Example

```typescript
import {
    AuthenticationApi,
    Configuration,
    AuthControllerSignInRequest
} from './api';

const configuration = new Configuration();
const apiInstance = new AuthenticationApi(configuration);

let authControllerSignInRequest: AuthControllerSignInRequest; //

const { status, data } = await apiInstance.authControllerSignIn(
    authControllerSignInRequest
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **authControllerSignInRequest** | **AuthControllerSignInRequest**|  | |


### Return type

void (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: Not defined


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**200** |  |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **authControllerSimpleSignIn**
> authControllerSimpleSignIn(authControllerSimpleSignInRequest)


### Example

```typescript
import {
    AuthenticationApi,
    Configuration,
    AuthControllerSimpleSignInRequest
} from './api';

const configuration = new Configuration();
const apiInstance = new AuthenticationApi(configuration);

let authControllerSimpleSignInRequest: AuthControllerSimpleSignInRequest; //

const { status, data } = await apiInstance.authControllerSimpleSignIn(
    authControllerSimpleSignInRequest
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **authControllerSimpleSignInRequest** | **AuthControllerSimpleSignInRequest**|  | |


### Return type

void (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: Not defined


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**200** |  |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

