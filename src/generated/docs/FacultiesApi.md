# FacultiesApi

All URIs are relative to *http://localhost*

|Method | HTTP request | Description|
|------------- | ------------- | -------------|
|[**facultiesControllerCreate**](#facultiescontrollercreate) | **POST** /faculties | |
|[**facultiesControllerFindAll**](#facultiescontrollerfindall) | **GET** /faculties | Get all faculties with pagination and relations|
|[**facultiesControllerFindOne**](#facultiescontrollerfindone) | **GET** /faculties/{id} | |
|[**facultiesControllerGetFacultiesRoles**](#facultiescontrollergetfacultiesroles) | **GET** /faculties/{facultyId}/{roleId}/roles | Get faculties with their users and roles|
|[**facultiesControllerGetUsersFaculty**](#facultiescontrollergetusersfaculty) | **GET** /faculties/{facultyId}/users | Get all users in a specific faculty with their roles|
|[**facultiesControllerRemove**](#facultiescontrollerremove) | **DELETE** /faculties/{id} | |
|[**facultiesControllerUpdate**](#facultiescontrollerupdate) | **PATCH** /faculties/{id} | |

# **facultiesControllerCreate**
> facultiesControllerCreate(createFacultyDto)


### Example

```typescript
import {
    FacultiesApi,
    Configuration,
    CreateFacultyDto
} from './api';

const configuration = new Configuration();
const apiInstance = new FacultiesApi(configuration);

let createFacultyDto: CreateFacultyDto; //

const { status, data } = await apiInstance.facultiesControllerCreate(
    createFacultyDto
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **createFacultyDto** | **CreateFacultyDto**|  | |


### Return type

void (empty response body)

### Authorization

[bearer](../README.md#bearer)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: Not defined


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**201** |  |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **facultiesControllerFindAll**
> facultiesControllerFindAll()


### Example

```typescript
import {
    FacultiesApi,
    Configuration
} from './api';

const configuration = new Configuration();
const apiInstance = new FacultiesApi(configuration);

let limit: any; //Number of items per page (default to undefined)
let page: any; //Page number to retrieve (default to undefined)
let sortBy: any; //Field to sort by (e.g., id, name, email) (optional) (default to undefined)
let order: any; //Sort order (ASC or DESC) (optional) (default to undefined)
let search: any; //Search term to filter results (optional) (default to undefined)

const { status, data } = await apiInstance.facultiesControllerFindAll(
    limit,
    page,
    sortBy,
    order,
    search
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **limit** | **any** | Number of items per page | defaults to undefined|
| **page** | **any** | Page number to retrieve | defaults to undefined|
| **sortBy** | **any** | Field to sort by (e.g., id, name, email) | (optional) defaults to undefined|
| **order** | **any** | Sort order (ASC or DESC) | (optional) defaults to undefined|
| **search** | **any** | Search term to filter results | (optional) defaults to undefined|


### Return type

void (empty response body)

### Authorization

[bearer](../README.md#bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**200** | Returns a paginated list of faculties with their users and roles |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **facultiesControllerFindOne**
> facultiesControllerFindOne()


### Example

```typescript
import {
    FacultiesApi,
    Configuration
} from './api';

const configuration = new Configuration();
const apiInstance = new FacultiesApi(configuration);

let id: string; // (default to undefined)

const { status, data } = await apiInstance.facultiesControllerFindOne(
    id
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **id** | [**string**] |  | defaults to undefined|


### Return type

void (empty response body)

### Authorization

[bearer](../README.md#bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**200** |  |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **facultiesControllerGetFacultiesRoles**
> facultiesControllerGetFacultiesRoles()


### Example

```typescript
import {
    FacultiesApi,
    Configuration
} from './api';

const configuration = new Configuration();
const apiInstance = new FacultiesApi(configuration);

let facultyId: string; // (default to undefined)
let roleId: string; // (default to undefined)

const { status, data } = await apiInstance.facultiesControllerGetFacultiesRoles(
    facultyId,
    roleId
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **facultyId** | [**string**] |  | defaults to undefined|
| **roleId** | [**string**] |  | defaults to undefined|


### Return type

void (empty response body)

### Authorization

[bearer](../README.md#bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**200** | Returns a paginated list of faculties with their users and role assignments |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **facultiesControllerGetUsersFaculty**
> facultiesControllerGetUsersFaculty()


### Example

```typescript
import {
    FacultiesApi,
    Configuration
} from './api';

const configuration = new Configuration();
const apiInstance = new FacultiesApi(configuration);

let facultyId: string; // (default to undefined)
let limit: any; //Number of items per page (default to undefined)
let page: any; //Page number to retrieve (default to undefined)
let sortBy: any; //Field to sort by (e.g., id, name, email) (optional) (default to undefined)
let order: any; //Sort order (ASC or DESC) (optional) (default to undefined)
let search: any; //Search term to filter results (optional) (default to undefined)

const { status, data } = await apiInstance.facultiesControllerGetUsersFaculty(
    facultyId,
    limit,
    page,
    sortBy,
    order,
    search
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **facultyId** | [**string**] |  | defaults to undefined|
| **limit** | **any** | Number of items per page | defaults to undefined|
| **page** | **any** | Page number to retrieve | defaults to undefined|
| **sortBy** | **any** | Field to sort by (e.g., id, name, email) | (optional) defaults to undefined|
| **order** | **any** | Sort order (ASC or DESC) | (optional) defaults to undefined|
| **search** | **any** | Search term to filter results | (optional) defaults to undefined|


### Return type

void (empty response body)

### Authorization

[bearer](../README.md#bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**200** | Returns a paginated list of users in the specified faculty with their roles |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **facultiesControllerRemove**
> facultiesControllerRemove()


### Example

```typescript
import {
    FacultiesApi,
    Configuration
} from './api';

const configuration = new Configuration();
const apiInstance = new FacultiesApi(configuration);

let id: string; // (default to undefined)

const { status, data } = await apiInstance.facultiesControllerRemove(
    id
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **id** | [**string**] |  | defaults to undefined|


### Return type

void (empty response body)

### Authorization

[bearer](../README.md#bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**200** |  |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **facultiesControllerUpdate**
> facultiesControllerUpdate(updateFacultyDto)


### Example

```typescript
import {
    FacultiesApi,
    Configuration,
    UpdateFacultyDto
} from './api';

const configuration = new Configuration();
const apiInstance = new FacultiesApi(configuration);

let id: string; // (default to undefined)
let updateFacultyDto: UpdateFacultyDto; //

const { status, data } = await apiInstance.facultiesControllerUpdate(
    id,
    updateFacultyDto
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **updateFacultyDto** | **UpdateFacultyDto**|  | |
| **id** | [**string**] |  | defaults to undefined|


### Return type

void (empty response body)

### Authorization

[bearer](../README.md#bearer)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: Not defined


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**200** |  |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

