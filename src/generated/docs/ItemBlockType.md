# ItemBlockType

The type of item block (RADIO, CHECKBOX, TEXTFIELD, etc.)

## Enum

* `Radio` (value: `'RADIO'`)

* `Checkbox` (value: `'CHECKBOX'`)

* `Textfield` (value: `'TEXTFIELD'`)

* `Grid` (value: `'GRID'`)

* `Header` (value: `'HEADER'`)

* `Image` (value: `'IMAGE'`)

* `Upload` (value: `'UPLOAD'`)

* `Nextsection` (value: `'NEXTSECTION'`)

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)
