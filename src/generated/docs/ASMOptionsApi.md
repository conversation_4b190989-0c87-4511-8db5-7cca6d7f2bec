# ASMOptionsApi

All URIs are relative to *http://localhost*

|Method | HTTP request | Description|
|------------- | ------------- | -------------|
|[**optionsControllerCreate**](#optionscontrollercreate) | **POST** /options | สร้างตัวเลือกใหม่|
|[**optionsControllerCreateForQuestion**](#optionscontrollercreateforquestion) | **POST** /options/{questionId} | สร้างตัวเลือกใหม่สำหรับคำถาม|
|[**optionsControllerFindAll**](#optionscontrollerfindall) | **GET** /options | ดึงข้อมูลตัวเลือกทั้งหมด|
|[**optionsControllerFindImagePath**](#optionscontrollerfindimagepath) | **GET** /options/file/{optionText} | |
|[**optionsControllerFindOne**](#optionscontrollerfindone) | **GET** /options/{id} | ดึงข้อมูลตัวเลือกตาม ID|
|[**optionsControllerRemove**](#optionscontrollerremove) | **DELETE** /options/{id} | ลบตัวเลือก|
|[**optionsControllerRemoveByFilename**](#optionscontrollerremovebyfilename) | **DELETE** /options/deleteFile/{optionText} | ลบตัวเลือก|
|[**optionsControllerRemoveByItemBlockId**](#optionscontrollerremovebyitemblockid) | **DELETE** /options/item-block/{itemBlockId} | ลบตัวเลือกทั้งหมดของ item block|
|[**optionsControllerUpdate**](#optionscontrollerupdate) | **PATCH** /options/{id} | แก้ไขข้อมูล&lt;|im_start|&gt;|
|[**optionsControllerUpdateForQuestion**](#optionscontrollerupdateforquestion) | **PATCH** /options/{questionId}/{optionId} | แก้ไขตัวเลือกสำหรับคำถาม|

# **optionsControllerCreate**
> optionsControllerCreate()


### Example

```typescript
import {
    ASMOptionsApi,
    Configuration
} from './api';

const configuration = new Configuration();
const apiInstance = new ASMOptionsApi(configuration);

let optionText: string; //The text content of the option (default to undefined)
let sequence: number; //The order/position of this option in the assessment (default to undefined)
let imagePath: File; //Path to the image associated with the option (optional) (default to undefined)
let value: number; //The score value assigned to this option (for scoring) (optional) (default to 1)
let nextSection: number; //ID of the next section to navigate to if this option is selected (for branching logic) (optional) (default to undefined)
let itemBlockId: number; //ID of the item block this option belongs to (optional) (default to undefined)

const { status, data } = await apiInstance.optionsControllerCreate(
    optionText,
    sequence,
    imagePath,
    value,
    nextSection,
    itemBlockId
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **optionText** | [**string**] | The text content of the option | defaults to undefined|
| **sequence** | [**number**] | The order/position of this option in the assessment | defaults to undefined|
| **imagePath** | [**File**] | Path to the image associated with the option | (optional) defaults to undefined|
| **value** | [**number**] | The score value assigned to this option (for scoring) | (optional) defaults to 1|
| **nextSection** | [**number**] | ID of the next section to navigate to if this option is selected (for branching logic) | (optional) defaults to undefined|
| **itemBlockId** | [**number**] | ID of the item block this option belongs to | (optional) defaults to undefined|


### Return type

void (empty response body)

### Authorization

[bearer](../README.md#bearer)

### HTTP request headers

 - **Content-Type**: multipart/form-data
 - **Accept**: Not defined


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**201** |  |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **optionsControllerCreateForQuestion**
> optionsControllerCreateForQuestion()


### Example

```typescript
import {
    ASMOptionsApi,
    Configuration
} from './api';

const configuration = new Configuration();
const apiInstance = new ASMOptionsApi(configuration);

let questionId: number; // (default to undefined)
let optionText: string; //The text content of the option (default to undefined)
let sequence: number; //The order/position of this option in the assessment (default to undefined)
let imagePath: File; //Path to the image associated with the option (optional) (default to undefined)
let value: number; //The score value assigned to this option (for scoring) (optional) (default to 1)
let nextSection: number; //ID of the next section to navigate to if this option is selected (for branching logic) (optional) (default to undefined)
let itemBlockId: number; //ID of the item block this option belongs to (optional) (default to undefined)

const { status, data } = await apiInstance.optionsControllerCreateForQuestion(
    questionId,
    optionText,
    sequence,
    imagePath,
    value,
    nextSection,
    itemBlockId
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **questionId** | [**number**] |  | defaults to undefined|
| **optionText** | [**string**] | The text content of the option | defaults to undefined|
| **sequence** | [**number**] | The order/position of this option in the assessment | defaults to undefined|
| **imagePath** | [**File**] | Path to the image associated with the option | (optional) defaults to undefined|
| **value** | [**number**] | The score value assigned to this option (for scoring) | (optional) defaults to 1|
| **nextSection** | [**number**] | ID of the next section to navigate to if this option is selected (for branching logic) | (optional) defaults to undefined|
| **itemBlockId** | [**number**] | ID of the item block this option belongs to | (optional) defaults to undefined|


### Return type

void (empty response body)

### Authorization

[bearer](../README.md#bearer)

### HTTP request headers

 - **Content-Type**: multipart/form-data
 - **Accept**: Not defined


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**201** |  |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **optionsControllerFindAll**
> optionsControllerFindAll()


### Example

```typescript
import {
    ASMOptionsApi,
    Configuration
} from './api';

const configuration = new Configuration();
const apiInstance = new ASMOptionsApi(configuration);

const { status, data } = await apiInstance.optionsControllerFindAll();
```

### Parameters
This endpoint does not have any parameters.


### Return type

void (empty response body)

### Authorization

[bearer](../README.md#bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**200** | ดึงข้อมูลสำเร็จ |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **optionsControllerFindImagePath**
> optionsControllerFindImagePath()


### Example

```typescript
import {
    ASMOptionsApi,
    Configuration
} from './api';

const configuration = new Configuration();
const apiInstance = new ASMOptionsApi(configuration);

let optionText: string; // (default to undefined)

const { status, data } = await apiInstance.optionsControllerFindImagePath(
    optionText
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **optionText** | [**string**] |  | defaults to undefined|


### Return type

void (empty response body)

### Authorization

[bearer](../README.md#bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**200** |  |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **optionsControllerFindOne**
> optionsControllerFindOne()


### Example

```typescript
import {
    ASMOptionsApi,
    Configuration
} from './api';

const configuration = new Configuration();
const apiInstance = new ASMOptionsApi(configuration);

let id: number; // (default to undefined)

const { status, data } = await apiInstance.optionsControllerFindOne(
    id
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **id** | [**number**] |  | defaults to undefined|


### Return type

void (empty response body)

### Authorization

[bearer](../README.md#bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**200** | ดึงข้อมูลสำเร็จ |  -  |
|**404** | ไม่พบตัวเลือก |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **optionsControllerRemove**
> optionsControllerRemove()


### Example

```typescript
import {
    ASMOptionsApi,
    Configuration
} from './api';

const configuration = new Configuration();
const apiInstance = new ASMOptionsApi(configuration);

let id: number; // (default to undefined)

const { status, data } = await apiInstance.optionsControllerRemove(
    id
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **id** | [**number**] |  | defaults to undefined|


### Return type

void (empty response body)

### Authorization

[bearer](../README.md#bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**200** | ลบข้อมูลสำเร็จ |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **optionsControllerRemoveByFilename**
> optionsControllerRemoveByFilename()


### Example

```typescript
import {
    ASMOptionsApi,
    Configuration
} from './api';

const configuration = new Configuration();
const apiInstance = new ASMOptionsApi(configuration);

let optionText: string; // (default to undefined)

const { status, data } = await apiInstance.optionsControllerRemoveByFilename(
    optionText
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **optionText** | [**string**] |  | defaults to undefined|


### Return type

void (empty response body)

### Authorization

[bearer](../README.md#bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**200** | ลบข้อมูลสำเร็จ |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **optionsControllerRemoveByItemBlockId**
> optionsControllerRemoveByItemBlockId()


### Example

```typescript
import {
    ASMOptionsApi,
    Configuration
} from './api';

const configuration = new Configuration();
const apiInstance = new ASMOptionsApi(configuration);

let itemBlockId: number; // (default to undefined)

const { status, data } = await apiInstance.optionsControllerRemoveByItemBlockId(
    itemBlockId
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **itemBlockId** | [**number**] |  | defaults to undefined|


### Return type

void (empty response body)

### Authorization

[bearer](../README.md#bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**200** | ลบข้อมูลสำเร็จ |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **optionsControllerUpdate**
> optionsControllerUpdate()


### Example

```typescript
import {
    ASMOptionsApi,
    Configuration
} from './api';

const configuration = new Configuration();
const apiInstance = new ASMOptionsApi(configuration);

let id: number; // (default to undefined)
let optionText: string; //The text content of the option (optional) (default to undefined)
let imagePath: File; //Path to the image associated with the option (optional) (default to undefined)
let value: number; //The score value assigned to this option (for scoring) (optional) (default to 1)
let nextSection: number; //ID of the next section to navigate to if this option is selected (for branching logic) (optional) (default to undefined)
let sequence: number; //The order/position of this option in the assessment (optional) (default to undefined)
let itemBlockId: number; //ID of the item block this option belongs to (optional) (default to undefined)

const { status, data } = await apiInstance.optionsControllerUpdate(
    id,
    optionText,
    imagePath,
    value,
    nextSection,
    sequence,
    itemBlockId
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **id** | [**number**] |  | defaults to undefined|
| **optionText** | [**string**] | The text content of the option | (optional) defaults to undefined|
| **imagePath** | [**File**] | Path to the image associated with the option | (optional) defaults to undefined|
| **value** | [**number**] | The score value assigned to this option (for scoring) | (optional) defaults to 1|
| **nextSection** | [**number**] | ID of the next section to navigate to if this option is selected (for branching logic) | (optional) defaults to undefined|
| **sequence** | [**number**] | The order/position of this option in the assessment | (optional) defaults to undefined|
| **itemBlockId** | [**number**] | ID of the item block this option belongs to | (optional) defaults to undefined|


### Return type

void (empty response body)

### Authorization

[bearer](../README.md#bearer)

### HTTP request headers

 - **Content-Type**: multipart/form-data
 - **Accept**: Not defined


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**200** | แก้ไขข้อมูลสำเร็จ |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **optionsControllerUpdateForQuestion**
> optionsControllerUpdateForQuestion()


### Example

```typescript
import {
    ASMOptionsApi,
    Configuration
} from './api';

const configuration = new Configuration();
const apiInstance = new ASMOptionsApi(configuration);

let questionId: number; // (default to undefined)
let optionId: number; // (default to undefined)
let optionText: string; //The text content of the option (optional) (default to undefined)
let imagePath: File; //Path to the image associated with the option (optional) (default to undefined)
let value: number; //The score value assigned to this option (for scoring) (optional) (default to 1)
let nextSection: number; //ID of the next section to navigate to if this option is selected (for branching logic) (optional) (default to undefined)
let sequence: number; //The order/position of this option in the assessment (optional) (default to undefined)
let itemBlockId: number; //ID of the item block this option belongs to (optional) (default to undefined)

const { status, data } = await apiInstance.optionsControllerUpdateForQuestion(
    questionId,
    optionId,
    optionText,
    imagePath,
    value,
    nextSection,
    sequence,
    itemBlockId
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **questionId** | [**number**] |  | defaults to undefined|
| **optionId** | [**number**] |  | defaults to undefined|
| **optionText** | [**string**] | The text content of the option | (optional) defaults to undefined|
| **imagePath** | [**File**] | Path to the image associated with the option | (optional) defaults to undefined|
| **value** | [**number**] | The score value assigned to this option (for scoring) | (optional) defaults to 1|
| **nextSection** | [**number**] | ID of the next section to navigate to if this option is selected (for branching logic) | (optional) defaults to undefined|
| **sequence** | [**number**] | The order/position of this option in the assessment | (optional) defaults to undefined|
| **itemBlockId** | [**number**] | ID of the item block this option belongs to | (optional) defaults to undefined|


### Return type

void (empty response body)

### Authorization

[bearer](../README.md#bearer)

### HTTP request headers

 - **Content-Type**: multipart/form-data
 - **Accept**: Not defined


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**200** | แก้ไขข้อมูลสำเร็จ |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

