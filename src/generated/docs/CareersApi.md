# CareersApi

All URIs are relative to *http://localhost*

|Method | HTTP request | Description|
|------------- | ------------- | -------------|
|[**careersControllerCreate**](#careerscontrollercreate) | **POST** /careers | Create a new career|
|[**careersControllerFindAll**](#careerscontrollerfindall) | **GET** /careers | Get all careers with pagination|
|[**careersControllerFindCareerNamesForDropdown**](#careerscontrollerfindcareernamesfordropdown) | **GET** /careers/names | Get career names for dropdown with pagination and search|
|[**careersControllerFindOne**](#careerscontrollerfindone) | **GET** /careers/{id} | Get a single career by ID|
|[**careersControllerRemove**](#careerscontrollerremove) | **DELETE** /careers/{id} | Delete a career|
|[**careersControllerUpdate**](#careerscontrollerupdate) | **PUT** /careers/{id} | Update a career|

# **careersControllerCreate**
> careersControllerCreate(createCareerDto)


### Example

```typescript
import {
    CareersApi,
    Configuration,
    CreateCareerDto
} from './api';

const configuration = new Configuration();
const apiInstance = new CareersApi(configuration);

let createCareerDto: CreateCareerDto; //

const { status, data } = await apiInstance.careersControllerCreate(
    createCareerDto
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **createCareerDto** | **CreateCareerDto**|  | |


### Return type

void (empty response body)

### Authorization

[bearer](../README.md#bearer)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: Not defined


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**201** | The career has been successfully created |  -  |
|**400** | Bad request |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **careersControllerFindAll**
> careersControllerFindAll()


### Example

```typescript
import {
    CareersApi,
    Configuration
} from './api';

const configuration = new Configuration();
const apiInstance = new CareersApi(configuration);

let limit: number; //Number of items per page (optional) (default to 10)
let page: number; //Page number (optional) (default to 1)
let sortBy: string; //Field to sort by (optional) (default to 'id')
let order: 'ASC' | 'DESC'; //Sort order (optional) (default to 'ASC')
let careerType: 'วิชาการ' | 'สนับสนุน'; //Filter by career type (วิชาการ/สนับสนุน) (optional) (default to undefined)
let search: string; //Search term for career name (optional) (default to undefined)
let careerName: string; //Filter by career name (optional) (default to undefined)
let careerRank: string; //Filter by career rank (optional) (default to undefined)

const { status, data } = await apiInstance.careersControllerFindAll(
    limit,
    page,
    sortBy,
    order,
    careerType,
    search,
    careerName,
    careerRank
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **limit** | [**number**] | Number of items per page | (optional) defaults to 10|
| **page** | [**number**] | Page number | (optional) defaults to 1|
| **sortBy** | [**string**] | Field to sort by | (optional) defaults to 'id'|
| **order** | [**&#39;ASC&#39; | &#39;DESC&#39;**]**Array<&#39;ASC&#39; &#124; &#39;DESC&#39;>** | Sort order | (optional) defaults to 'ASC'|
| **careerType** | [**&#39;วิชาการ&#39; | &#39;สนับสนุน&#39;**]**Array<&#39;วิชาการ&#39; &#124; &#39;สนับสนุน&#39;>** | Filter by career type (วิชาการ/สนับสนุน) | (optional) defaults to undefined|
| **search** | [**string**] | Search term for career name | (optional) defaults to undefined|
| **careerName** | [**string**] | Filter by career name | (optional) defaults to undefined|
| **careerRank** | [**string**] | Filter by career rank | (optional) defaults to undefined|


### Return type

void (empty response body)

### Authorization

[bearer](../README.md#bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**200** | Returns a paginated list of careers |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **careersControllerFindCareerNamesForDropdown**
> careersControllerFindCareerNamesForDropdown()


### Example

```typescript
import {
    CareersApi,
    Configuration
} from './api';

const configuration = new Configuration();
const apiInstance = new CareersApi(configuration);

let limit: number; //Number of items per page (optional) (default to 10)
let page: number; //Page number (optional) (default to 1)
let sortBy: string; //Field to sort by (optional) (default to 'id')
let order: 'ASC' | 'DESC'; //Sort order (optional) (default to 'ASC')
let careerType: 'วิชาการ' | 'สนับสนุน'; //Filter by career type (วิชาการ/สนับสนุน) (optional) (default to undefined)
let search: string; //Search term for career name (optional) (default to undefined)
let careerName: string; //Filter by career name (optional) (default to undefined)
let careerRank: string; //Filter by career rank (optional) (default to undefined)

const { status, data } = await apiInstance.careersControllerFindCareerNamesForDropdown(
    limit,
    page,
    sortBy,
    order,
    careerType,
    search,
    careerName,
    careerRank
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **limit** | [**number**] | Number of items per page | (optional) defaults to 10|
| **page** | [**number**] | Page number | (optional) defaults to 1|
| **sortBy** | [**string**] | Field to sort by | (optional) defaults to 'id'|
| **order** | [**&#39;ASC&#39; | &#39;DESC&#39;**]**Array<&#39;ASC&#39; &#124; &#39;DESC&#39;>** | Sort order | (optional) defaults to 'ASC'|
| **careerType** | [**&#39;วิชาการ&#39; | &#39;สนับสนุน&#39;**]**Array<&#39;วิชาการ&#39; &#124; &#39;สนับสนุน&#39;>** | Filter by career type (วิชาการ/สนับสนุน) | (optional) defaults to undefined|
| **search** | [**string**] | Search term for career name | (optional) defaults to undefined|
| **careerName** | [**string**] | Filter by career name | (optional) defaults to undefined|
| **careerRank** | [**string**] | Filter by career rank | (optional) defaults to undefined|


### Return type

void (empty response body)

### Authorization

[bearer](../README.md#bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**200** | Returns career names for dropdown |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **careersControllerFindOne**
> careersControllerFindOne()


### Example

```typescript
import {
    CareersApi,
    Configuration
} from './api';

const configuration = new Configuration();
const apiInstance = new CareersApi(configuration);

let id: string; // (default to undefined)

const { status, data } = await apiInstance.careersControllerFindOne(
    id
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **id** | [**string**] |  | defaults to undefined|


### Return type

void (empty response body)

### Authorization

[bearer](../README.md#bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**200** | Returns the requested career |  -  |
|**404** | Career not found |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **careersControllerRemove**
> careersControllerRemove()


### Example

```typescript
import {
    CareersApi,
    Configuration
} from './api';

const configuration = new Configuration();
const apiInstance = new CareersApi(configuration);

let id: number; // (default to undefined)

const { status, data } = await apiInstance.careersControllerRemove(
    id
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **id** | [**number**] |  | defaults to undefined|


### Return type

void (empty response body)

### Authorization

[bearer](../README.md#bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**204** | The career has been successfully deleted |  -  |
|**404** | Career not found |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **careersControllerUpdate**
> careersControllerUpdate(updateCareerDto)


### Example

```typescript
import {
    CareersApi,
    Configuration,
    UpdateCareerDto
} from './api';

const configuration = new Configuration();
const apiInstance = new CareersApi(configuration);

let id: number; // (default to undefined)
let updateCareerDto: UpdateCareerDto; //

const { status, data } = await apiInstance.careersControllerUpdate(
    id,
    updateCareerDto
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **updateCareerDto** | **UpdateCareerDto**|  | |
| **id** | [**number**] |  | defaults to undefined|


### Return type

void (empty response body)

### Authorization

[bearer](../README.md#bearer)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: Not defined


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**200** | The career has been successfully updated |  -  |
|**404** | Career not found |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

