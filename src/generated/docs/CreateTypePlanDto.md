# CreateTypePlanDto


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**developmentPlanId** | **number** | ID ของ Development Plan | [default to undefined]
**name** | **string** | ประเภทของ Type Plan | [default to undefined]
**ageWorkId** | **number** | ID ของ Age Work | [optional] [default to undefined]
**careerId** | **number** | ID ของ Career | [optional] [default to undefined]
**privateUserId** | **number** | ID ของ Private User | [optional] [default to undefined]
**positionId** | **number** | ID ของ Position | [optional] [default to undefined]
**skillIds** | **Array&lt;string&gt;** | ID ของ Skill | [optional] [default to undefined]

## Example

```typescript
import { CreateTypePlanDto } from './api';

const instance: CreateTypePlanDto = {
    developmentPlanId,
    name,
    ageWorkId,
    careerId,
    privateUserId,
    positionId,
    skillIds,
};
```

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)
