# PersonalPlansResponseDto


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**data** | [**Array&lt;PersonalPlanItemDto&gt;**](PersonalPlanItemDto.md) | List of personal plans | [default to undefined]
**total** | **number** | Total number of records | [default to undefined]
**curPage** | **number** | Current page number | [default to undefined]
**hasPrev** | **boolean** | Has previous page | [default to undefined]
**hasNext** | **boolean** | Has next page | [default to undefined]

## Example

```typescript
import { PersonalPlansResponseDto } from './api';

const instance: PersonalPlansResponseDto = {
    data,
    total,
    curPage,
    hasPrev,
    hasNext,
};
```

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)
