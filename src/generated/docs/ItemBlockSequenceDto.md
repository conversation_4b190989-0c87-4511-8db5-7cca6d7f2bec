# ItemBlockSequenceDto


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**id** | **number** |  | [default to undefined]
**sequence** | **number** |  | [default to undefined]

## Example

```typescript
import { ItemBlockSequenceDto } from './api';

const instance: ItemBlockSequenceDto = {
    id,
    sequence,
};
```

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)
