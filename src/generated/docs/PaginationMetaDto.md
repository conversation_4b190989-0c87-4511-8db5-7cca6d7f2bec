# PaginationMetaDto


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**total** | **number** | Total number of items | [default to undefined]
**page** | **number** | Current page number | [default to undefined]
**limit** | **number** | Number of items per page | [default to undefined]
**totalPages** | **number** | Total number of pages | [default to undefined]
**hasPrev** | **boolean** | Whether there is a previous page | [default to undefined]
**hasNext** | **boolean** | Whether there is a next page | [default to undefined]

## Example

```typescript
import { PaginationMetaDto } from './api';

const instance: PaginationMetaDto = {
    total,
    page,
    limit,
    totalPages,
    hasPrev,
    hasNext,
};
```

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)
