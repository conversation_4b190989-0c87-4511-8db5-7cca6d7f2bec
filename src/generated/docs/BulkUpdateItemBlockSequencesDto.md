# BulkUpdateItemBlockSequencesDto


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**itemBlocks** | [**Array&lt;ItemBlockSequenceDto&gt;**](ItemBlockSequenceDto.md) | Array of item blocks with their new sequence numbers | [default to undefined]

## Example

```typescript
import { BulkUpdateItemBlockSequencesDto } from './api';

const instance: BulkUpdateItemBlockSequencesDto = {
    itemBlocks,
};
```

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)
