/* tslint:disable */
/* eslint-disable */
/**
 * HRD Backend API
 * Human Resource Development Backend API Documentation
 *
 * The version of the OpenAPI document: 1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


// May contain unused imports in some cases
// @ts-ignore
import type { PersonalPlanItemDto } from './personal-plan-item-dto';

/**
 * 
 * @export
 * @interface PersonalPlansResponseDto
 */
export interface PersonalPlansResponseDto {
    /**
     * List of personal plans
     * @type {Array<PersonalPlanItemDto>}
     * @memberof PersonalPlansResponseDto
     */
    'data': Array<PersonalPlanItemDto>;
    /**
     * Total number of records
     * @type {number}
     * @memberof PersonalPlansResponseDto
     */
    'total': number;
    /**
     * Current page number
     * @type {number}
     * @memberof PersonalPlansResponseDto
     */
    'curPage': number;
    /**
     * Has previous page
     * @type {boolean}
     * @memberof PersonalPlansResponseDto
     */
    'hasPrev': boolean;
    /**
     * Has next page
     * @type {boolean}
     * @memberof PersonalPlansResponseDto
     */
    'hasNext': boolean;
}

