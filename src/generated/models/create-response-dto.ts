/* tslint:disable */
/* eslint-disable */
/**
 * HRD Backend API
 * Human Resource Development Backend API Documentation
 *
 * The version of the OpenAPI document: 1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */



/**
 * 
 * @export
 * @interface CreateResponseDto
 */
export interface CreateResponseDto {
    /**
     * 
     * @type {number}
     * @memberof CreateResponseDto
     */
    'submissionId': number;
    /**
     * 
     * @type {number}
     * @memberof CreateResponseDto
     */
    'questionId': number;
    /**
     * 
     * @type {number}
     * @memberof CreateResponseDto
     */
    'responseId': number;
    /**
     * 
     * @type {number}
     * @memberof CreateResponseDto
     */
    'selectedOptionId': number;
    /**
     * 
     * @type {string}
     * @memberof CreateResponseDto
     */
    'answerText'?: string;
    /**
     * Array of selected option IDs for CHECKBOX type questions
     * @type {Array<number>}
     * @memberof CreateResponseDto
     */
    'selectedOptionIds'?: Array<number>;
}

