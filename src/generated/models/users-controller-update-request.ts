/* tslint:disable */
/* eslint-disable */
/**
 * HRD Backend API
 * Human Resource Development Backend API Documentation
 *
 * The version of the OpenAPI document: 1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */



/**
 * 
 * @export
 * @interface UsersControllerUpdateRequest
 */
export interface UsersControllerUpdateRequest {
    /**
     * 
     * @type {string}
     * @memberof UsersControllerUpdateRequest
     */
    'name'?: string;
    /**
     * 
     * @type {string}
     * @memberof UsersControllerUpdateRequest
     */
    'email'?: string;
    /**
     * 
     * @type {string}
     * @memberof UsersControllerUpdateRequest
     */
    'currentPassword'?: string;
    /**
     * 
     * @type {string}
     * @memberof UsersControllerUpdateRequest
     */
    'newPassword'?: string;
    /**
     * 
     * @type {Array<number>}
     * @memberof UsersControllerUpdateRequest
     */
    'roleIds'?: Array<number>;
}

