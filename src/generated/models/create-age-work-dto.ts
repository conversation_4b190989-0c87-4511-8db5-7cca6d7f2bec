/* tslint:disable */
/* eslint-disable */
/**
 * HRD Backend API
 * Human Resource Development Backend API Documentation
 *
 * The version of the OpenAPI document: 1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */



/**
 * 
 * @export
 * @interface CreateAgeWorkDto
 */
export interface CreateAgeWorkDto {
    /**
     * Name of the age work range
     * @type {string}
     * @memberof CreateAgeWorkDto
     */
    'name': string;
    /**
     * Starting year of work experience
     * @type {number}
     * @memberof CreateAgeWorkDto
     */
    'startYear': number;
    /**
     * Ending year of work experience
     * @type {number}
     * @memberof CreateAgeWorkDto
     */
    'endYear': number;
    /**
     * ID of the age work criteria plan this belongs to
     * @type {number}
     * @memberof CreateAgeWorkDto
     */
    'ageWorkCriteriaId': number;
}

