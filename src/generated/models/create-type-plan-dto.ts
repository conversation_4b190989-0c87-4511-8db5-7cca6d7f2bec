/* tslint:disable */
/* eslint-disable */
/**
 * HRD Backend API
 * Human Resource Development Backend API Documentation
 *
 * The version of the OpenAPI document: 1.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

/**
 *
 * @export
 * @interface CreateTypePlanDto
 */
export interface CreateTypePlanDto {
  /**
   * ID ของ Development Plan
   * @type {number}
   * @memberof CreateTypePlanDto
   */
  developmentPlanId: number;
  /**
   * ประเภทของ Type Plan
   * @type {string}
   * @memberof CreateTypePlanDto
   */
  name: CreateTypePlanDtoNameEnum;
  /**
   * ID ของ Age Work
   * @type {number}
   * @memberof CreateTypePlanDto
   */
  ageWorkId?: number;
  /**
   * ID ของ Career
   * @type {number}
   * @memberof CreateTypePlanDto
   */
  careerId?: number;
  /**
   * ID ของ Private User
   * @type {number}
   * @memberof CreateTypePlanDto
   */
  privateUserId?: number;
  /**
   * ID ของ Position
   * @type {number}
   * @memberof CreateTypePlanDto
   */
  positionId?: number;
  /**
   * ID ของ Skill
   * @type {Array<string>}
   * @memberof CreateTypePlanDto
   */
  skillIds?: Array<string>;
}

export const CreateTypePlanDtoNameEnum = {
  1: 'ทั่วไปบุคลากร',
  2: 'ทั่วไปผู้บริหาร',
  3: 'เฉพาะด้านบริหาร',
  4: 'เฉพาะด้านวิชาการ',
  5: 'เฉพาะสายสนับสนุน',
  6: 'ตำแหน่ง',
} as const;

export type CreateTypePlanDtoNameEnum =
  (typeof CreateTypePlanDtoNameEnum)[keyof typeof CreateTypePlanDtoNameEnum];
