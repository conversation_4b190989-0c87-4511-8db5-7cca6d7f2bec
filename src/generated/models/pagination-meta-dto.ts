/* tslint:disable */
/* eslint-disable */
/**
 * HRD Backend API
 * Human Resource Development Backend API Documentation
 *
 * The version of the OpenAPI document: 1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */



/**
 * 
 * @export
 * @interface PaginationMetaDto
 */
export interface PaginationMetaDto {
    /**
     * Total number of items
     * @type {number}
     * @memberof PaginationMetaDto
     */
    'total': number;
    /**
     * Current page number
     * @type {number}
     * @memberof PaginationMetaDto
     */
    'page': number;
    /**
     * Number of items per page
     * @type {number}
     * @memberof PaginationMetaDto
     */
    'limit': number;
    /**
     * Total number of pages
     * @type {number}
     * @memberof PaginationMetaDto
     */
    'totalPages': number;
    /**
     * Whether there is a previous page
     * @type {boolean}
     * @memberof PaginationMetaDto
     */
    'hasPrev': boolean;
    /**
     * Whether there is a next page
     * @type {boolean}
     * @memberof PaginationMetaDto
     */
    'hasNext': boolean;
}

