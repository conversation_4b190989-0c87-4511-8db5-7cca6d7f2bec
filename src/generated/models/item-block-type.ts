/* tslint:disable */
/* eslint-disable */
/**
 * HRD Backend API
 * Human Resource Development Backend API Documentation
 *
 * The version of the OpenAPI document: 1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */



/**
 * The type of item block (RADIO, CHECKBOX, TEXTFIELD, etc.)
 * @export
 * @enum {string}
 */

export const ItemBlockType = {
    Radio: 'RADIO',
    Checkbox: 'CHECKBOX',
    Textfield: 'TEXTFIELD',
    Grid: 'GRID',
    Header: 'HEADER',
    Image: 'IMAGE',
    Upload: 'UPLOAD',
    Nextsection: 'NEXTSECTION'
} as const;

export type ItemBlockType = typeof ItemBlockType[keyof typeof ItemBlockType];



