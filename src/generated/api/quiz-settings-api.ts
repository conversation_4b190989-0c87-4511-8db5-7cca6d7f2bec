/* tslint:disable */
/* eslint-disable */
/**
 * HRD Backend API
 * Human Resource Development Backend API Documentation
 *
 * The version of the OpenAPI document: 1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


import type { Configuration } from '../configuration';
import type { AxiosPromise, AxiosInstance, RawAxiosRequestConfig } from 'axios';
import globalAxios from 'axios';
// Some imports not used depending on template conditions
// @ts-ignore
import { DUMMY_BASE_URL, assertParamExists, setApiKeyToObject, setBasicAuthToObject, setBearerAuthToObject, setOAuthToObject, setSearchParams, serializeDataIfNeeded, toPathString, createRequestFunction } from '../common';
// @ts-ignore
import { BASE_PATH, COLLECTION_FORMATS, type RequestArgs, BaseAPI, RequiredError, operationServerMap } from '../base';
// @ts-ignore
import type { UpdateQuizSettingsDto } from '../models';
/**
 * QuizSettingsApi - axios parameter creator
 * @export
 */
export const QuizSettingsApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * ดึงการตั้งค่าของแบบทดสอบ
         * @summary Get quiz settings
         * @param {number} id รหัสของแบบทดสอบ
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        quizSettingsControllerGetQuizSettings: async (id: number, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            assertParamExists('quizSettingsControllerGetQuizSettings', 'id', id)
            const localVarPath = `/assessments/{id}/settings`
                .replace(`{${"id"}}`, encodeURIComponent(String(id)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication bearer required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * อัปเดตการตั้งค่าของแบบทดสอบ (เฉพาะฟิลด์ที่เกี่ยวข้องกับการตั้งค่า)
         * @summary Update quiz settings
         * @param {number} id รหัสของแบบทดสอบ
         * @param {UpdateQuizSettingsDto} updateQuizSettingsDto 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        quizSettingsControllerUpdateQuizSettings: async (id: number, updateQuizSettingsDto: UpdateQuizSettingsDto, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            assertParamExists('quizSettingsControllerUpdateQuizSettings', 'id', id)
            // verify required parameter 'updateQuizSettingsDto' is not null or undefined
            assertParamExists('quizSettingsControllerUpdateQuizSettings', 'updateQuizSettingsDto', updateQuizSettingsDto)
            const localVarPath = `/assessments/{id}/settings`
                .replace(`{${"id"}}`, encodeURIComponent(String(id)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'PATCH', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication bearer required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(updateQuizSettingsDto, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * QuizSettingsApi - functional programming interface
 * @export
 */
export const QuizSettingsApiFp = function(configuration?: Configuration) {
    const localVarAxiosParamCreator = QuizSettingsApiAxiosParamCreator(configuration)
    return {
        /**
         * ดึงการตั้งค่าของแบบทดสอบ
         * @summary Get quiz settings
         * @param {number} id รหัสของแบบทดสอบ
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async quizSettingsControllerGetQuizSettings(id: number, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<object>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.quizSettingsControllerGetQuizSettings(id, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['QuizSettingsApi.quizSettingsControllerGetQuizSettings']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * อัปเดตการตั้งค่าของแบบทดสอบ (เฉพาะฟิลด์ที่เกี่ยวข้องกับการตั้งค่า)
         * @summary Update quiz settings
         * @param {number} id รหัสของแบบทดสอบ
         * @param {UpdateQuizSettingsDto} updateQuizSettingsDto 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async quizSettingsControllerUpdateQuizSettings(id: number, updateQuizSettingsDto: UpdateQuizSettingsDto, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<object>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.quizSettingsControllerUpdateQuizSettings(id, updateQuizSettingsDto, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['QuizSettingsApi.quizSettingsControllerUpdateQuizSettings']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
    }
};

/**
 * QuizSettingsApi - factory interface
 * @export
 */
export const QuizSettingsApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    const localVarFp = QuizSettingsApiFp(configuration)
    return {
        /**
         * ดึงการตั้งค่าของแบบทดสอบ
         * @summary Get quiz settings
         * @param {number} id รหัสของแบบทดสอบ
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        quizSettingsControllerGetQuizSettings(id: number, options?: RawAxiosRequestConfig): AxiosPromise<object> {
            return localVarFp.quizSettingsControllerGetQuizSettings(id, options).then((request) => request(axios, basePath));
        },
        /**
         * อัปเดตการตั้งค่าของแบบทดสอบ (เฉพาะฟิลด์ที่เกี่ยวข้องกับการตั้งค่า)
         * @summary Update quiz settings
         * @param {number} id รหัสของแบบทดสอบ
         * @param {UpdateQuizSettingsDto} updateQuizSettingsDto 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        quizSettingsControllerUpdateQuizSettings(id: number, updateQuizSettingsDto: UpdateQuizSettingsDto, options?: RawAxiosRequestConfig): AxiosPromise<object> {
            return localVarFp.quizSettingsControllerUpdateQuizSettings(id, updateQuizSettingsDto, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * QuizSettingsApi - object-oriented interface
 * @export
 * @class QuizSettingsApi
 * @extends {BaseAPI}
 */
export class QuizSettingsApi extends BaseAPI {
    /**
     * ดึงการตั้งค่าของแบบทดสอบ
     * @summary Get quiz settings
     * @param {number} id รหัสของแบบทดสอบ
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof QuizSettingsApi
     */
    public quizSettingsControllerGetQuizSettings(id: number, options?: RawAxiosRequestConfig) {
        return QuizSettingsApiFp(this.configuration).quizSettingsControllerGetQuizSettings(id, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * อัปเดตการตั้งค่าของแบบทดสอบ (เฉพาะฟิลด์ที่เกี่ยวข้องกับการตั้งค่า)
     * @summary Update quiz settings
     * @param {number} id รหัสของแบบทดสอบ
     * @param {UpdateQuizSettingsDto} updateQuizSettingsDto 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof QuizSettingsApi
     */
    public quizSettingsControllerUpdateQuizSettings(id: number, updateQuizSettingsDto: UpdateQuizSettingsDto, options?: RawAxiosRequestConfig) {
        return QuizSettingsApiFp(this.configuration).quizSettingsControllerUpdateQuizSettings(id, updateQuizSettingsDto, options).then((request) => request(this.axios, this.basePath));
    }
}

