/* tslint:disable */
/* eslint-disable */
/**
 * HRD Backend API
 * Human Resource Development Backend API Documentation
 *
 * The version of the OpenAPI document: 1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


import type { Configuration } from '../configuration';
import type { AxiosPromise, AxiosInstance, RawAxiosRequestConfig } from 'axios';
import globalAxios from 'axios';
// Some imports not used depending on template conditions
// @ts-ignore
import { DUMMY_BASE_URL, assertParamExists, setApiKeyToObject, setBasicAuthToObject, setBearerAuthToObject, setOAuthToObject, setSearchParams, serializeDataIfNeeded, toPathString, createRequestFunction } from '../common';
// @ts-ignore
import { BASE_PATH, COLLECTION_FORMATS, type RequestArgs, BaseAPI, RequiredError, operationServerMap } from '../base';
// @ts-ignore
import type { CreateCareerDto } from '../models';
// @ts-ignore
import type { UpdateCareerDto } from '../models';
/**
 * CareersApi - axios parameter creator
 * @export
 */
export const CareersApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * 
         * @summary Create a new career
         * @param {CreateCareerDto} createCareerDto 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        careersControllerCreate: async (createCareerDto: CreateCareerDto, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'createCareerDto' is not null or undefined
            assertParamExists('careersControllerCreate', 'createCareerDto', createCareerDto)
            const localVarPath = `/careers`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication bearer required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(createCareerDto, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary Get all careers with pagination
         * @param {number} [limit] Number of items per page
         * @param {number} [page] Page number
         * @param {string} [sortBy] Field to sort by
         * @param {CareersControllerFindAllOrderEnum} [order] Sort order
         * @param {CareersControllerFindAllCareerTypeEnum} [careerType] Filter by career type (วิชาการ/สนับสนุน)
         * @param {string} [search] Search term for career name
         * @param {string} [careerName] Filter by career name
         * @param {string} [careerRank] Filter by career rank
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        careersControllerFindAll: async (limit?: number, page?: number, sortBy?: string, order?: CareersControllerFindAllOrderEnum, careerType?: CareersControllerFindAllCareerTypeEnum, search?: string, careerName?: string, careerRank?: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/careers`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication bearer required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)

            if (limit !== undefined) {
                localVarQueryParameter['limit'] = limit;
            }

            if (page !== undefined) {
                localVarQueryParameter['page'] = page;
            }

            if (sortBy !== undefined) {
                localVarQueryParameter['sortBy'] = sortBy;
            }

            if (order !== undefined) {
                localVarQueryParameter['order'] = order;
            }

            if (careerType !== undefined) {
                localVarQueryParameter['career_type'] = careerType;
            }

            if (search !== undefined) {
                localVarQueryParameter['search'] = search;
            }

            if (careerName !== undefined) {
                localVarQueryParameter['career_name'] = careerName;
            }

            if (careerRank !== undefined) {
                localVarQueryParameter['career_rank'] = careerRank;
            }


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary Get career names for dropdown with pagination and search
         * @param {number} [limit] Number of items per page
         * @param {number} [page] Page number
         * @param {string} [sortBy] Field to sort by
         * @param {CareersControllerFindCareerNamesForDropdownOrderEnum} [order] Sort order
         * @param {CareersControllerFindCareerNamesForDropdownCareerTypeEnum} [careerType] Filter by career type (วิชาการ/สนับสนุน)
         * @param {string} [search] Search term for career name
         * @param {string} [careerName] Filter by career name
         * @param {string} [careerRank] Filter by career rank
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        careersControllerFindCareerNamesForDropdown: async (limit?: number, page?: number, sortBy?: string, order?: CareersControllerFindCareerNamesForDropdownOrderEnum, careerType?: CareersControllerFindCareerNamesForDropdownCareerTypeEnum, search?: string, careerName?: string, careerRank?: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/careers/names`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication bearer required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)

            if (limit !== undefined) {
                localVarQueryParameter['limit'] = limit;
            }

            if (page !== undefined) {
                localVarQueryParameter['page'] = page;
            }

            if (sortBy !== undefined) {
                localVarQueryParameter['sortBy'] = sortBy;
            }

            if (order !== undefined) {
                localVarQueryParameter['order'] = order;
            }

            if (careerType !== undefined) {
                localVarQueryParameter['career_type'] = careerType;
            }

            if (search !== undefined) {
                localVarQueryParameter['search'] = search;
            }

            if (careerName !== undefined) {
                localVarQueryParameter['career_name'] = careerName;
            }

            if (careerRank !== undefined) {
                localVarQueryParameter['career_rank'] = careerRank;
            }


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary Get a single career by ID
         * @param {string} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        careersControllerFindOne: async (id: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            assertParamExists('careersControllerFindOne', 'id', id)
            const localVarPath = `/careers/{id}`
                .replace(`{${"id"}}`, encodeURIComponent(String(id)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication bearer required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary Delete a career
         * @param {number} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        careersControllerRemove: async (id: number, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            assertParamExists('careersControllerRemove', 'id', id)
            const localVarPath = `/careers/{id}`
                .replace(`{${"id"}}`, encodeURIComponent(String(id)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'DELETE', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication bearer required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary Update a career
         * @param {number} id 
         * @param {UpdateCareerDto} updateCareerDto 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        careersControllerUpdate: async (id: number, updateCareerDto: UpdateCareerDto, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            assertParamExists('careersControllerUpdate', 'id', id)
            // verify required parameter 'updateCareerDto' is not null or undefined
            assertParamExists('careersControllerUpdate', 'updateCareerDto', updateCareerDto)
            const localVarPath = `/careers/{id}`
                .replace(`{${"id"}}`, encodeURIComponent(String(id)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'PUT', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication bearer required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(updateCareerDto, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * CareersApi - functional programming interface
 * @export
 */
export const CareersApiFp = function(configuration?: Configuration) {
    const localVarAxiosParamCreator = CareersApiAxiosParamCreator(configuration)
    return {
        /**
         * 
         * @summary Create a new career
         * @param {CreateCareerDto} createCareerDto 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async careersControllerCreate(createCareerDto: CreateCareerDto, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.careersControllerCreate(createCareerDto, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['CareersApi.careersControllerCreate']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @summary Get all careers with pagination
         * @param {number} [limit] Number of items per page
         * @param {number} [page] Page number
         * @param {string} [sortBy] Field to sort by
         * @param {CareersControllerFindAllOrderEnum} [order] Sort order
         * @param {CareersControllerFindAllCareerTypeEnum} [careerType] Filter by career type (วิชาการ/สนับสนุน)
         * @param {string} [search] Search term for career name
         * @param {string} [careerName] Filter by career name
         * @param {string} [careerRank] Filter by career rank
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async careersControllerFindAll(limit?: number, page?: number, sortBy?: string, order?: CareersControllerFindAllOrderEnum, careerType?: CareersControllerFindAllCareerTypeEnum, search?: string, careerName?: string, careerRank?: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.careersControllerFindAll(limit, page, sortBy, order, careerType, search, careerName, careerRank, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['CareersApi.careersControllerFindAll']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @summary Get career names for dropdown with pagination and search
         * @param {number} [limit] Number of items per page
         * @param {number} [page] Page number
         * @param {string} [sortBy] Field to sort by
         * @param {CareersControllerFindCareerNamesForDropdownOrderEnum} [order] Sort order
         * @param {CareersControllerFindCareerNamesForDropdownCareerTypeEnum} [careerType] Filter by career type (วิชาการ/สนับสนุน)
         * @param {string} [search] Search term for career name
         * @param {string} [careerName] Filter by career name
         * @param {string} [careerRank] Filter by career rank
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async careersControllerFindCareerNamesForDropdown(limit?: number, page?: number, sortBy?: string, order?: CareersControllerFindCareerNamesForDropdownOrderEnum, careerType?: CareersControllerFindCareerNamesForDropdownCareerTypeEnum, search?: string, careerName?: string, careerRank?: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.careersControllerFindCareerNamesForDropdown(limit, page, sortBy, order, careerType, search, careerName, careerRank, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['CareersApi.careersControllerFindCareerNamesForDropdown']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @summary Get a single career by ID
         * @param {string} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async careersControllerFindOne(id: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.careersControllerFindOne(id, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['CareersApi.careersControllerFindOne']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @summary Delete a career
         * @param {number} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async careersControllerRemove(id: number, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.careersControllerRemove(id, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['CareersApi.careersControllerRemove']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @summary Update a career
         * @param {number} id 
         * @param {UpdateCareerDto} updateCareerDto 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async careersControllerUpdate(id: number, updateCareerDto: UpdateCareerDto, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.careersControllerUpdate(id, updateCareerDto, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['CareersApi.careersControllerUpdate']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
    }
};

/**
 * CareersApi - factory interface
 * @export
 */
export const CareersApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    const localVarFp = CareersApiFp(configuration)
    return {
        /**
         * 
         * @summary Create a new career
         * @param {CreateCareerDto} createCareerDto 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        careersControllerCreate(createCareerDto: CreateCareerDto, options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.careersControllerCreate(createCareerDto, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary Get all careers with pagination
         * @param {number} [limit] Number of items per page
         * @param {number} [page] Page number
         * @param {string} [sortBy] Field to sort by
         * @param {CareersControllerFindAllOrderEnum} [order] Sort order
         * @param {CareersControllerFindAllCareerTypeEnum} [careerType] Filter by career type (วิชาการ/สนับสนุน)
         * @param {string} [search] Search term for career name
         * @param {string} [careerName] Filter by career name
         * @param {string} [careerRank] Filter by career rank
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        careersControllerFindAll(limit?: number, page?: number, sortBy?: string, order?: CareersControllerFindAllOrderEnum, careerType?: CareersControllerFindAllCareerTypeEnum, search?: string, careerName?: string, careerRank?: string, options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.careersControllerFindAll(limit, page, sortBy, order, careerType, search, careerName, careerRank, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary Get career names for dropdown with pagination and search
         * @param {number} [limit] Number of items per page
         * @param {number} [page] Page number
         * @param {string} [sortBy] Field to sort by
         * @param {CareersControllerFindCareerNamesForDropdownOrderEnum} [order] Sort order
         * @param {CareersControllerFindCareerNamesForDropdownCareerTypeEnum} [careerType] Filter by career type (วิชาการ/สนับสนุน)
         * @param {string} [search] Search term for career name
         * @param {string} [careerName] Filter by career name
         * @param {string} [careerRank] Filter by career rank
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        careersControllerFindCareerNamesForDropdown(limit?: number, page?: number, sortBy?: string, order?: CareersControllerFindCareerNamesForDropdownOrderEnum, careerType?: CareersControllerFindCareerNamesForDropdownCareerTypeEnum, search?: string, careerName?: string, careerRank?: string, options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.careersControllerFindCareerNamesForDropdown(limit, page, sortBy, order, careerType, search, careerName, careerRank, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary Get a single career by ID
         * @param {string} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        careersControllerFindOne(id: string, options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.careersControllerFindOne(id, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary Delete a career
         * @param {number} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        careersControllerRemove(id: number, options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.careersControllerRemove(id, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary Update a career
         * @param {number} id 
         * @param {UpdateCareerDto} updateCareerDto 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        careersControllerUpdate(id: number, updateCareerDto: UpdateCareerDto, options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.careersControllerUpdate(id, updateCareerDto, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * CareersApi - object-oriented interface
 * @export
 * @class CareersApi
 * @extends {BaseAPI}
 */
export class CareersApi extends BaseAPI {
    /**
     * 
     * @summary Create a new career
     * @param {CreateCareerDto} createCareerDto 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof CareersApi
     */
    public careersControllerCreate(createCareerDto: CreateCareerDto, options?: RawAxiosRequestConfig) {
        return CareersApiFp(this.configuration).careersControllerCreate(createCareerDto, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @summary Get all careers with pagination
     * @param {number} [limit] Number of items per page
     * @param {number} [page] Page number
     * @param {string} [sortBy] Field to sort by
     * @param {CareersControllerFindAllOrderEnum} [order] Sort order
     * @param {CareersControllerFindAllCareerTypeEnum} [careerType] Filter by career type (วิชาการ/สนับสนุน)
     * @param {string} [search] Search term for career name
     * @param {string} [careerName] Filter by career name
     * @param {string} [careerRank] Filter by career rank
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof CareersApi
     */
    public careersControllerFindAll(limit?: number, page?: number, sortBy?: string, order?: CareersControllerFindAllOrderEnum, careerType?: CareersControllerFindAllCareerTypeEnum, search?: string, careerName?: string, careerRank?: string, options?: RawAxiosRequestConfig) {
        return CareersApiFp(this.configuration).careersControllerFindAll(limit, page, sortBy, order, careerType, search, careerName, careerRank, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @summary Get career names for dropdown with pagination and search
     * @param {number} [limit] Number of items per page
     * @param {number} [page] Page number
     * @param {string} [sortBy] Field to sort by
     * @param {CareersControllerFindCareerNamesForDropdownOrderEnum} [order] Sort order
     * @param {CareersControllerFindCareerNamesForDropdownCareerTypeEnum} [careerType] Filter by career type (วิชาการ/สนับสนุน)
     * @param {string} [search] Search term for career name
     * @param {string} [careerName] Filter by career name
     * @param {string} [careerRank] Filter by career rank
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof CareersApi
     */
    public careersControllerFindCareerNamesForDropdown(limit?: number, page?: number, sortBy?: string, order?: CareersControllerFindCareerNamesForDropdownOrderEnum, careerType?: CareersControllerFindCareerNamesForDropdownCareerTypeEnum, search?: string, careerName?: string, careerRank?: string, options?: RawAxiosRequestConfig) {
        return CareersApiFp(this.configuration).careersControllerFindCareerNamesForDropdown(limit, page, sortBy, order, careerType, search, careerName, careerRank, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @summary Get a single career by ID
     * @param {string} id 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof CareersApi
     */
    public careersControllerFindOne(id: string, options?: RawAxiosRequestConfig) {
        return CareersApiFp(this.configuration).careersControllerFindOne(id, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @summary Delete a career
     * @param {number} id 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof CareersApi
     */
    public careersControllerRemove(id: number, options?: RawAxiosRequestConfig) {
        return CareersApiFp(this.configuration).careersControllerRemove(id, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @summary Update a career
     * @param {number} id 
     * @param {UpdateCareerDto} updateCareerDto 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof CareersApi
     */
    public careersControllerUpdate(id: number, updateCareerDto: UpdateCareerDto, options?: RawAxiosRequestConfig) {
        return CareersApiFp(this.configuration).careersControllerUpdate(id, updateCareerDto, options).then((request) => request(this.axios, this.basePath));
    }
}

/**
 * @export
 */
export const CareersControllerFindAllOrderEnum = {
    Asc: 'ASC',
    Desc: 'DESC'
} as const;
export type CareersControllerFindAllOrderEnum = typeof CareersControllerFindAllOrderEnum[keyof typeof CareersControllerFindAllOrderEnum];
/**
 * @export
 */
export const CareersControllerFindAllCareerTypeEnum = {
    1: 'วิชาการ',
    2: 'สนับสนุน'
} as const;
export type CareersControllerFindAllCareerTypeEnum = typeof CareersControllerFindAllCareerTypeEnum[keyof typeof CareersControllerFindAllCareerTypeEnum];
/**
 * @export
 */
export const CareersControllerFindCareerNamesForDropdownOrderEnum = {
    Asc: 'ASC',
    Desc: 'DESC'
} as const;
export type CareersControllerFindCareerNamesForDropdownOrderEnum = typeof CareersControllerFindCareerNamesForDropdownOrderEnum[keyof typeof CareersControllerFindCareerNamesForDropdownOrderEnum];
/**
 * @export
 */
export const CareersControllerFindCareerNamesForDropdownCareerTypeEnum = {
    1: 'วิชาการ',
    2: 'สนับสนุน'
} as const;
export type CareersControllerFindCareerNamesForDropdownCareerTypeEnum = typeof CareersControllerFindCareerNamesForDropdownCareerTypeEnum[keyof typeof CareersControllerFindCareerNamesForDropdownCareerTypeEnum];
