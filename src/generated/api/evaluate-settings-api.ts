/* tslint:disable */
/* eslint-disable */
/**
 * HRD Backend API
 * Human Resource Development Backend API Documentation
 *
 * The version of the OpenAPI document: 1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


import type { Configuration } from '../configuration';
import type { AxiosPromise, AxiosInstance, RawAxiosRequestConfig } from 'axios';
import globalAxios from 'axios';
// Some imports not used depending on template conditions
// @ts-ignore
import { DUMMY_BASE_URL, assertParamExists, setApiKeyToObject, setBasicAuthToObject, setBearerAuthToObject, setOAuthToObject, setSearchParams, serializeDataIfNeeded, toPathString, createRequestFunction } from '../common';
// @ts-ignore
import { BASE_PATH, COLLECTION_FORMATS, type RequestArgs, BaseAPI, RequiredError, operationServerMap } from '../base';
// @ts-ignore
import type { UpdateEvaluateSettingsDto } from '../models';
/**
 * EvaluateSettingsApi - axios parameter creator
 * @export
 */
export const EvaluateSettingsApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * ดึงการตั้งค่าของแบบประเมิน
         * @summary Get evaluate settings
         * @param {number} id รหัสของแบบประเมิน
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        evaluateSettingsControllerGetEvaluateSettings: async (id: number, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            assertParamExists('evaluateSettingsControllerGetEvaluateSettings', 'id', id)
            const localVarPath = `/assessments/{id}/evaluate-settings`
                .replace(`{${"id"}}`, encodeURIComponent(String(id)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication bearer required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * อัปเดตการตั้งค่าของแบบประเมิน (เฉพาะฟิลด์ที่เกี่ยวข้องกับการตั้งค่า)
         * @summary Update evaluate settings
         * @param {number} id รหัสของแบบประเมิน
         * @param {UpdateEvaluateSettingsDto} updateEvaluateSettingsDto 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        evaluateSettingsControllerUpdateEvaluateSettings: async (id: number, updateEvaluateSettingsDto: UpdateEvaluateSettingsDto, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            assertParamExists('evaluateSettingsControllerUpdateEvaluateSettings', 'id', id)
            // verify required parameter 'updateEvaluateSettingsDto' is not null or undefined
            assertParamExists('evaluateSettingsControllerUpdateEvaluateSettings', 'updateEvaluateSettingsDto', updateEvaluateSettingsDto)
            const localVarPath = `/assessments/{id}/evaluate-settings`
                .replace(`{${"id"}}`, encodeURIComponent(String(id)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'PATCH', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication bearer required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(updateEvaluateSettingsDto, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * EvaluateSettingsApi - functional programming interface
 * @export
 */
export const EvaluateSettingsApiFp = function(configuration?: Configuration) {
    const localVarAxiosParamCreator = EvaluateSettingsApiAxiosParamCreator(configuration)
    return {
        /**
         * ดึงการตั้งค่าของแบบประเมิน
         * @summary Get evaluate settings
         * @param {number} id รหัสของแบบประเมิน
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async evaluateSettingsControllerGetEvaluateSettings(id: number, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<object>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.evaluateSettingsControllerGetEvaluateSettings(id, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['EvaluateSettingsApi.evaluateSettingsControllerGetEvaluateSettings']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * อัปเดตการตั้งค่าของแบบประเมิน (เฉพาะฟิลด์ที่เกี่ยวข้องกับการตั้งค่า)
         * @summary Update evaluate settings
         * @param {number} id รหัสของแบบประเมิน
         * @param {UpdateEvaluateSettingsDto} updateEvaluateSettingsDto 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async evaluateSettingsControllerUpdateEvaluateSettings(id: number, updateEvaluateSettingsDto: UpdateEvaluateSettingsDto, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<object>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.evaluateSettingsControllerUpdateEvaluateSettings(id, updateEvaluateSettingsDto, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['EvaluateSettingsApi.evaluateSettingsControllerUpdateEvaluateSettings']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
    }
};

/**
 * EvaluateSettingsApi - factory interface
 * @export
 */
export const EvaluateSettingsApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    const localVarFp = EvaluateSettingsApiFp(configuration)
    return {
        /**
         * ดึงการตั้งค่าของแบบประเมิน
         * @summary Get evaluate settings
         * @param {number} id รหัสของแบบประเมิน
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        evaluateSettingsControllerGetEvaluateSettings(id: number, options?: RawAxiosRequestConfig): AxiosPromise<object> {
            return localVarFp.evaluateSettingsControllerGetEvaluateSettings(id, options).then((request) => request(axios, basePath));
        },
        /**
         * อัปเดตการตั้งค่าของแบบประเมิน (เฉพาะฟิลด์ที่เกี่ยวข้องกับการตั้งค่า)
         * @summary Update evaluate settings
         * @param {number} id รหัสของแบบประเมิน
         * @param {UpdateEvaluateSettingsDto} updateEvaluateSettingsDto 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        evaluateSettingsControllerUpdateEvaluateSettings(id: number, updateEvaluateSettingsDto: UpdateEvaluateSettingsDto, options?: RawAxiosRequestConfig): AxiosPromise<object> {
            return localVarFp.evaluateSettingsControllerUpdateEvaluateSettings(id, updateEvaluateSettingsDto, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * EvaluateSettingsApi - object-oriented interface
 * @export
 * @class EvaluateSettingsApi
 * @extends {BaseAPI}
 */
export class EvaluateSettingsApi extends BaseAPI {
    /**
     * ดึงการตั้งค่าของแบบประเมิน
     * @summary Get evaluate settings
     * @param {number} id รหัสของแบบประเมิน
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof EvaluateSettingsApi
     */
    public evaluateSettingsControllerGetEvaluateSettings(id: number, options?: RawAxiosRequestConfig) {
        return EvaluateSettingsApiFp(this.configuration).evaluateSettingsControllerGetEvaluateSettings(id, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * อัปเดตการตั้งค่าของแบบประเมิน (เฉพาะฟิลด์ที่เกี่ยวข้องกับการตั้งค่า)
     * @summary Update evaluate settings
     * @param {number} id รหัสของแบบประเมิน
     * @param {UpdateEvaluateSettingsDto} updateEvaluateSettingsDto 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof EvaluateSettingsApi
     */
    public evaluateSettingsControllerUpdateEvaluateSettings(id: number, updateEvaluateSettingsDto: UpdateEvaluateSettingsDto, options?: RawAxiosRequestConfig) {
        return EvaluateSettingsApiFp(this.configuration).evaluateSettingsControllerUpdateEvaluateSettings(id, updateEvaluateSettingsDto, options).then((request) => request(this.axios, this.basePath));
    }
}

