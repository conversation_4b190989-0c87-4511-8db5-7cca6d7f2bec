/* tslint:disable */
/* eslint-disable */
/**
 * HRD Backend API
 * Human Resource Development Backend API Documentation
 *
 * The version of the OpenAPI document: 1.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import type { Configuration } from '../configuration';
import type { AxiosPromise, AxiosInstance, RawAxiosRequestConfig } from 'axios';
import globalAxios from 'axios';
// Some imports not used depending on template conditions
// @ts-ignore
import {
  DUMMY_BASE_URL,
  assertParamExists,
  setApiKeyToObject,
  setBasicAuthToObject,
  setBearerAuthToObject,
  setOAuthToObject,
  setSearchParams,
  serializeDataIfNeeded,
  toPathString,
  createRequestFunction,
} from '../common';
// @ts-ignore
import {
  BASE_PATH,
  COLLECTION_FORMATS,
  type RequestArgs,
  BaseAPI,
  RequiredError,
  operationServerMap,
} from '../base';
// @ts-ignore
import type { CreateCompetenciesDto } from '../models';
/**
 * CompetenciesApi - axios parameter creator
 * @export
 */
export const CompetenciesApiAxiosParamCreator = function (configuration?: Configuration) {
  return {
    /**
     *
     * @summary Create a competency
     * @param {CreateCompetenciesDto} createCompetenciesDto
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    competenciesControllerCreate: async (
      createCompetenciesDto: CreateCompetenciesDto,
      options: RawAxiosRequestConfig = {},
    ): Promise<RequestArgs> => {
      // verify required parameter 'createCompetenciesDto' is not null or undefined
      assertParamExists(
        'competenciesControllerCreate',
        'createCompetenciesDto',
        createCompetenciesDto,
      );
      const localVarPath = `/Competencies`;
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      // authentication bearer required
      // http bearer authentication required
      await setBearerAuthToObject(localVarHeaderParameter, configuration);

      localVarHeaderParameter['Content-Type'] = 'application/json';

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        createCompetenciesDto,
        localVarRequestOptions,
        configuration,
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     *
     * @summary Get all competencies (with pagination)
     * @param {any} limit Number of items per page
     * @param {any} page Page number to retrieve
     * @param {CompetenciesControllerFindAllCareerTypeEnum} [careerType]
     * @param {any} [sortBy] Field to sort by (e.g., id, name, email)
     * @param {any} [order] Sort order (ASC or DESC)
     * @param {any} [search] Search term to filter results
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    competenciesControllerFindAll: async (
      limit: any,
      page: any,
      careerType?: CompetenciesControllerFindAllCareerTypeEnum,
      sortBy?: any,
      order?: any,
      search?: any,
      options: RawAxiosRequestConfig = {},
    ): Promise<RequestArgs> => {
      // verify required parameter 'limit' is not null or undefined
      assertParamExists('competenciesControllerFindAll', 'limit', limit);
      // verify required parameter 'page' is not null or undefined
      assertParamExists('competenciesControllerFindAll', 'page', page);
      const localVarPath = `/Competencies`;
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      // authentication bearer required
      // http bearer authentication required
      await setBearerAuthToObject(localVarHeaderParameter, configuration);

      if (careerType !== undefined) {
        localVarQueryParameter['career_type'] = careerType;
      }

      if (sortBy !== undefined) {
        for (const [key, value] of Object.entries(sortBy)) {
          localVarQueryParameter[key] = value;
        }
      }

      if (order !== undefined) {
        for (const [key, value] of Object.entries(order)) {
          localVarQueryParameter[key] = value;
        }
      }

      if (limit !== undefined) {
        for (const [key, value] of Object.entries(limit)) {
          localVarQueryParameter[key] = value;
        }
      }

      if (page !== undefined) {
        for (const [key, value] of Object.entries(page)) {
          localVarQueryParameter[key] = value;
        }
      }

      if (search !== undefined) {
        for (const [key, value] of Object.entries(search)) {
          localVarQueryParameter[key] = value;
        }
      }

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     *
     * @summary Get competency by Type
     * @param {string} careerType
     * @param {any} id Competency ID
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    competenciesControllerFindByType: async (
      careerType: string,
      id: any,
      options: RawAxiosRequestConfig = {},
    ): Promise<RequestArgs> => {
      // verify required parameter 'careerType' is not null or undefined
      assertParamExists('competenciesControllerFindByType', 'careerType', careerType);
      // verify required parameter 'id' is not null or undefined
      assertParamExists('competenciesControllerFindByType', 'id', id);
      const localVarPath = `/Competencies/type/{career_type}`
        .replace(`{${'career_type'}}`, encodeURIComponent(String(careerType)))
        .replace(`{${'id'}}`, encodeURIComponent(String(id)));
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      // authentication bearer required
      // http bearer authentication required
      await setBearerAuthToObject(localVarHeaderParameter, configuration);

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     *
     * @summary Get competency by ID
     * @param {string} id Competency ID
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    competenciesControllerFindOne: async (
      id: string,
      options: RawAxiosRequestConfig = {},
    ): Promise<RequestArgs> => {
      // verify required parameter 'id' is not null or undefined
      assertParamExists('competenciesControllerFindOne', 'id', id);
      const localVarPath = `/Competencies/{id}`.replace(
        `{${'id'}}`,
        encodeURIComponent(String(id)),
      );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      // authentication bearer required
      // http bearer authentication required
      await setBearerAuthToObject(localVarHeaderParameter, configuration);

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     *
     * @summary Delete a competency
     * @param {string} id Competency ID
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    competenciesControllerRemove: async (
      id: string,
      options: RawAxiosRequestConfig = {},
    ): Promise<RequestArgs> => {
      // verify required parameter 'id' is not null or undefined
      assertParamExists('competenciesControllerRemove', 'id', id);
      const localVarPath = `/Competencies/{id}`.replace(
        `{${'id'}}`,
        encodeURIComponent(String(id)),
      );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = { method: 'DELETE', ...baseOptions, ...options };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      // authentication bearer required
      // http bearer authentication required
      await setBearerAuthToObject(localVarHeaderParameter, configuration);

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     *
     * @summary Update a competency
     * @param {string} id Competency ID
     * @param {object} body
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    competenciesControllerUpdate: async (
      id: string,
      body: object,
      options: RawAxiosRequestConfig = {},
    ): Promise<RequestArgs> => {
      // verify required parameter 'id' is not null or undefined
      assertParamExists('competenciesControllerUpdate', 'id', id);
      // verify required parameter 'body' is not null or undefined
      assertParamExists('competenciesControllerUpdate', 'body', body);
      const localVarPath = `/Competencies/{id}`.replace(
        `{${'id'}}`,
        encodeURIComponent(String(id)),
      );
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = { method: 'PATCH', ...baseOptions, ...options };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      // authentication bearer required
      // http bearer authentication required
      await setBearerAuthToObject(localVarHeaderParameter, configuration);

      localVarHeaderParameter['Content-Type'] = 'application/json';

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        body,
        localVarRequestOptions,
        configuration,
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
  };
};

/**
 * CompetenciesApi - functional programming interface
 * @export
 */
export const CompetenciesApiFp = function (configuration?: Configuration) {
  const localVarAxiosParamCreator = CompetenciesApiAxiosParamCreator(configuration);
  return {
    /**
     *
     * @summary Create a competency
     * @param {CreateCompetenciesDto} createCompetenciesDto
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async competenciesControllerCreate(
      createCompetenciesDto: CreateCompetenciesDto,
      options?: RawAxiosRequestConfig,
    ): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
      const localVarAxiosArgs = await localVarAxiosParamCreator.competenciesControllerCreate(
        createCompetenciesDto,
        options,
      );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['CompetenciesApi.competenciesControllerCreate']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration,
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     *
     * @summary Get all competencies (with pagination)
     * @param {any} limit Number of items per page
     * @param {any} page Page number to retrieve
     * @param {CompetenciesControllerFindAllCareerTypeEnum} [careerType]
     * @param {any} [sortBy] Field to sort by (e.g., id, name, email)
     * @param {any} [order] Sort order (ASC or DESC)
     * @param {any} [search] Search term to filter results
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async competenciesControllerFindAll(
      limit: any,
      page: any,
      careerType?: CompetenciesControllerFindAllCareerTypeEnum,
      sortBy?: any,
      order?: any,
      search?: any,
      options?: RawAxiosRequestConfig,
    ): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
      const localVarAxiosArgs = await localVarAxiosParamCreator.competenciesControllerFindAll(
        limit,
        page,
        careerType,
        sortBy,
        order,
        search,
        options,
      );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['CompetenciesApi.competenciesControllerFindAll']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration,
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     *
     * @summary Get competency by Type
     * @param {string} careerType
     * @param {any} id Competency ID
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async competenciesControllerFindByType(
      careerType: string,
      id: any,
      options?: RawAxiosRequestConfig,
    ): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
      const localVarAxiosArgs = await localVarAxiosParamCreator.competenciesControllerFindByType(
        careerType,
        id,
        options,
      );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['CompetenciesApi.competenciesControllerFindByType']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration,
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     *
     * @summary Get competency by ID
     * @param {string} id Competency ID
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async competenciesControllerFindOne(
      id: string,
      options?: RawAxiosRequestConfig,
    ): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
      const localVarAxiosArgs = await localVarAxiosParamCreator.competenciesControllerFindOne(
        id,
        options,
      );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['CompetenciesApi.competenciesControllerFindOne']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration,
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     *
     * @summary Delete a competency
     * @param {string} id Competency ID
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async competenciesControllerRemove(
      id: string,
      options?: RawAxiosRequestConfig,
    ): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
      const localVarAxiosArgs = await localVarAxiosParamCreator.competenciesControllerRemove(
        id,
        options,
      );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['CompetenciesApi.competenciesControllerRemove']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration,
        )(axios, localVarOperationServerBasePath || basePath);
    },
    /**
     *
     * @summary Update a competency
     * @param {string} id Competency ID
     * @param {object} body
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async competenciesControllerUpdate(
      id: string,
      body: object,
      options?: RawAxiosRequestConfig,
    ): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
      const localVarAxiosArgs = await localVarAxiosParamCreator.competenciesControllerUpdate(
        id,
        body,
        options,
      );
      const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
      const localVarOperationServerBasePath =
        operationServerMap['CompetenciesApi.competenciesControllerUpdate']?.[
          localVarOperationServerIndex
        ]?.url;
      return (axios, basePath) =>
        createRequestFunction(
          localVarAxiosArgs,
          globalAxios,
          BASE_PATH,
          configuration,
        )(axios, localVarOperationServerBasePath || basePath);
    },
  };
};

/**
 * CompetenciesApi - factory interface
 * @export
 */
export const CompetenciesApiFactory = function (
  configuration?: Configuration,
  basePath?: string,
  axios?: AxiosInstance,
) {
  const localVarFp = CompetenciesApiFp(configuration);
  return {
    /**
     *
     * @summary Create a competency
     * @param {CreateCompetenciesDto} createCompetenciesDto
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    competenciesControllerCreate(
      createCompetenciesDto: CreateCompetenciesDto,
      options?: RawAxiosRequestConfig,
    ): AxiosPromise<void> {
      return localVarFp
        .competenciesControllerCreate(createCompetenciesDto, options)
        .then((request) => request(axios, basePath));
    },
    /**
     *
     * @summary Get all competencies (with pagination)
     * @param {any} limit Number of items per page
     * @param {any} page Page number to retrieve
     * @param {CompetenciesControllerFindAllCareerTypeEnum} [careerType]
     * @param {any} [sortBy] Field to sort by (e.g., id, name, email)
     * @param {any} [order] Sort order (ASC or DESC)
     * @param {any} [search] Search term to filter results
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    competenciesControllerFindAll(
      limit: any,
      page: any,
      careerType?: CompetenciesControllerFindAllCareerTypeEnum,
      sortBy?: any,
      order?: any,
      search?: any,
      options?: RawAxiosRequestConfig,
    ): AxiosPromise<void> {
      return localVarFp
        .competenciesControllerFindAll(limit, page, careerType, sortBy, order, search, options)
        .then((request) => request(axios, basePath));
    },
    /**
     *
     * @summary Get competency by Type
     * @param {string} careerType
     * @param {any} id Competency ID
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    competenciesControllerFindByType(
      careerType: string,
      id: any,
      options?: RawAxiosRequestConfig,
    ): AxiosPromise<void> {
      return localVarFp
        .competenciesControllerFindByType(careerType, id, options)
        .then((request) => request(axios, basePath));
    },
    /**
     *
     * @summary Get competency by ID
     * @param {string} id Competency ID
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    competenciesControllerFindOne(id: string, options?: RawAxiosRequestConfig): AxiosPromise<void> {
      return localVarFp
        .competenciesControllerFindOne(id, options)
        .then((request) => request(axios, basePath));
    },
    /**
     *
     * @summary Delete a competency
     * @param {string} id Competency ID
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    competenciesControllerRemove(id: string, options?: RawAxiosRequestConfig): AxiosPromise<void> {
      return localVarFp
        .competenciesControllerRemove(id, options)
        .then((request) => request(axios, basePath));
    },
    /**
     *
     * @summary Update a competency
     * @param {string} id Competency ID
     * @param {object} body
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    competenciesControllerUpdate(
      id: string,
      body: object,
      options?: RawAxiosRequestConfig,
    ): AxiosPromise<void> {
      return localVarFp
        .competenciesControllerUpdate(id, body, options)
        .then((request) => request(axios, basePath));
    },
  };
};

/**
 * CompetenciesApi - object-oriented interface
 * @export
 * @class CompetenciesApi
 * @extends {BaseAPI}
 */
export class CompetenciesApi extends BaseAPI {
  /**
   *
   * @summary Create a competency
   * @param {CreateCompetenciesDto} createCompetenciesDto
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof CompetenciesApi
   */
  public competenciesControllerCreate(
    createCompetenciesDto: CreateCompetenciesDto,
    options?: RawAxiosRequestConfig,
  ) {
    return CompetenciesApiFp(this.configuration)
      .competenciesControllerCreate(createCompetenciesDto, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   *
   * @summary Get all competencies (with pagination)
   * @param {any} limit Number of items per page
   * @param {any} page Page number to retrieve
   * @param {CompetenciesControllerFindAllCareerTypeEnum} [careerType]
   * @param {any} [sortBy] Field to sort by (e.g., id, name, email)
   * @param {any} [order] Sort order (ASC or DESC)
   * @param {any} [search] Search term to filter results
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof CompetenciesApi
   */
  public competenciesControllerFindAll(
    limit: any,
    page: any,
    careerType?: CompetenciesControllerFindAllCareerTypeEnum,
    sortBy?: any,
    order?: any,
    search?: any,
    options?: RawAxiosRequestConfig,
  ) {
    return CompetenciesApiFp(this.configuration)
      .competenciesControllerFindAll(limit, page, careerType, sortBy, order, search, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   *
   * @summary Get competency by Type
   * @param {string} careerType
   * @param {any} id Competency ID
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof CompetenciesApi
   */
  public competenciesControllerFindByType(
    careerType: string,
    id: any,
    options?: RawAxiosRequestConfig,
  ) {
    return CompetenciesApiFp(this.configuration)
      .competenciesControllerFindByType(careerType, id, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   *
   * @summary Get competency by ID
   * @param {string} id Competency ID
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof CompetenciesApi
   */
  public competenciesControllerFindOne(id: string, options?: RawAxiosRequestConfig) {
    return CompetenciesApiFp(this.configuration)
      .competenciesControllerFindOne(id, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   *
   * @summary Delete a competency
   * @param {string} id Competency ID
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof CompetenciesApi
   */
  public competenciesControllerRemove(id: string, options?: RawAxiosRequestConfig) {
    return CompetenciesApiFp(this.configuration)
      .competenciesControllerRemove(id, options)
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   *
   * @summary Update a competency
   * @param {string} id Competency ID
   * @param {object} body
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof CompetenciesApi
   */
  public competenciesControllerUpdate(id: string, body: object, options?: RawAxiosRequestConfig) {
    return CompetenciesApiFp(this.configuration)
      .competenciesControllerUpdate(id, body, options)
      .then((request) => request(this.axios, this.basePath));
  }
}

/**
 * @export
 */
export const CompetenciesControllerFindAllCareerTypeEnum = {
  1: 'สมรรถนะหลัก',
  2: 'สมรรถนะสายวิชาการ',
  3: 'สมรรถนะสายสนับสนุนวิชการ',
  4: 'สมรรถนะทางการบริหาร',
} as const;
export type CompetenciesControllerFindAllCareerTypeEnum =
  (typeof CompetenciesControllerFindAllCareerTypeEnum)[keyof typeof CompetenciesControllerFindAllCareerTypeEnum];
