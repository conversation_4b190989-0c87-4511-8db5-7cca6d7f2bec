/* tslint:disable */
/* eslint-disable */
/**
 * HRD Backend API
 * Human Resource Development Backend API Documentation
 *
 * The version of the OpenAPI document: 1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


import type { Configuration } from '../configuration';
import type { AxiosPromise, AxiosInstance, RawAxiosRequestConfig } from 'axios';
import globalAxios from 'axios';
// Some imports not used depending on template conditions
// @ts-ignore
import { DUMMY_BASE_URL, assertParamExists, setApiKeyToObject, setBasicAuthToObject, setBearerAuthToObject, setOAuthToObject, setSearchParams, serializeDataIfNeeded, toPathString, createRequestFunction } from '../common';
// @ts-ignore
import { BASE_PATH, COLLECTION_FORMATS, type RequestArgs, BaseAPI, RequiredError, operationServerMap } from '../base';
/**
 * PermissionsApi - axios parameter creator
 * @export
 */
export const PermissionsApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * 
         * @param {string} name ชื่อสิทธิ์
         * @param {string} nameEn รายละเอียดสิทธิ์ภาษาอังกฤษ
         * @param {string} descTh รายละเอียดสิทธิ์ภาษาไทย
         * @param {boolean} [status] สถานะการใช้งาน
         * @param {boolean} [isDefault] เป็นสิทธิ์เริ่มต้นหรือไม่
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        permissionsControllerCreate: async (name: string, nameEn: string, descTh: string, status?: boolean, isDefault?: boolean, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'name' is not null or undefined
            assertParamExists('permissionsControllerCreate', 'name', name)
            // verify required parameter 'nameEn' is not null or undefined
            assertParamExists('permissionsControllerCreate', 'nameEn', nameEn)
            // verify required parameter 'descTh' is not null or undefined
            assertParamExists('permissionsControllerCreate', 'descTh', descTh)
            const localVarPath = `/permissions`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;
            const localVarFormParams = new ((configuration && configuration.formDataCtor) || FormData)();

            // authentication bearer required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)


            if (name !== undefined) { 
                localVarFormParams.append('name', name as any);
            }
    
            if (nameEn !== undefined) { 
                localVarFormParams.append('nameEn', nameEn as any);
            }
    
            if (descTh !== undefined) { 
                localVarFormParams.append('descTh', descTh as any);
            }
    
            if (status !== undefined) { 
                localVarFormParams.append('status', String(status) as any);
            }
    
            if (isDefault !== undefined) { 
                localVarFormParams.append('isDefault', String(isDefault) as any);
            }
    
    
            localVarHeaderParameter['Content-Type'] = 'multipart/form-data';
    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = localVarFormParams;

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {any} limit Number of items per page
         * @param {any} page Page number to retrieve
         * @param {any} [sortBy] Field to sort by (e.g., id, name, email)
         * @param {any} [order] Sort order (ASC or DESC)
         * @param {any} [search] Search term to filter results
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        permissionsControllerFindAll: async (limit: any, page: any, sortBy?: any, order?: any, search?: any, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'limit' is not null or undefined
            assertParamExists('permissionsControllerFindAll', 'limit', limit)
            // verify required parameter 'page' is not null or undefined
            assertParamExists('permissionsControllerFindAll', 'page', page)
            const localVarPath = `/permissions`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication bearer required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)

            if (sortBy !== undefined) {
                for (const [key, value] of Object.entries(sortBy)) {
                    localVarQueryParameter[key] = value;
                }
            }

            if (order !== undefined) {
                for (const [key, value] of Object.entries(order)) {
                    localVarQueryParameter[key] = value;
                }
            }

            if (limit !== undefined) {
                for (const [key, value] of Object.entries(limit)) {
                    localVarQueryParameter[key] = value;
                }
            }

            if (page !== undefined) {
                for (const [key, value] of Object.entries(page)) {
                    localVarQueryParameter[key] = value;
                }
            }

            if (search !== undefined) {
                for (const [key, value] of Object.entries(search)) {
                    localVarQueryParameter[key] = value;
                }
            }


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {boolean} perStatus 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        permissionsControllerFindAllByStatus: async (perStatus: boolean, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'perStatus' is not null or undefined
            assertParamExists('permissionsControllerFindAllByStatus', 'perStatus', perStatus)
            const localVarPath = `/permissions/status`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication bearer required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)

            if (perStatus !== undefined) {
                localVarQueryParameter['perStatus'] = perStatus;
            }


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {number} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        permissionsControllerFindOne: async (id: number, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            assertParamExists('permissionsControllerFindOne', 'id', id)
            const localVarPath = `/permissions/{id}`
                .replace(`{${"id"}}`, encodeURIComponent(String(id)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication bearer required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {number} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        permissionsControllerRemove: async (id: number, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            assertParamExists('permissionsControllerRemove', 'id', id)
            const localVarPath = `/permissions/{id}`
                .replace(`{${"id"}}`, encodeURIComponent(String(id)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'DELETE', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication bearer required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {number} id 
         * @param {boolean} status สถานะการใช้งานสิทธิ์
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        permissionsControllerSetStatus: async (id: number, status: boolean, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            assertParamExists('permissionsControllerSetStatus', 'id', id)
            // verify required parameter 'status' is not null or undefined
            assertParamExists('permissionsControllerSetStatus', 'status', status)
            const localVarPath = `/permissions/{id}/set-status`
                .replace(`{${"id"}}`, encodeURIComponent(String(id)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'PATCH', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;
            const localVarFormParams = new ((configuration && configuration.formDataCtor) || FormData)();

            // authentication bearer required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)


            if (status !== undefined) { 
                localVarFormParams.append('status', String(status) as any);
            }
    
    
            localVarHeaderParameter['Content-Type'] = 'multipart/form-data';
    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = localVarFormParams;

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {number} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        permissionsControllerUpdate: async (id: number, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            assertParamExists('permissionsControllerUpdate', 'id', id)
            const localVarPath = `/permissions/{id}`
                .replace(`{${"id"}}`, encodeURIComponent(String(id)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'PATCH', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication bearer required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * PermissionsApi - functional programming interface
 * @export
 */
export const PermissionsApiFp = function(configuration?: Configuration) {
    const localVarAxiosParamCreator = PermissionsApiAxiosParamCreator(configuration)
    return {
        /**
         * 
         * @param {string} name ชื่อสิทธิ์
         * @param {string} nameEn รายละเอียดสิทธิ์ภาษาอังกฤษ
         * @param {string} descTh รายละเอียดสิทธิ์ภาษาไทย
         * @param {boolean} [status] สถานะการใช้งาน
         * @param {boolean} [isDefault] เป็นสิทธิ์เริ่มต้นหรือไม่
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async permissionsControllerCreate(name: string, nameEn: string, descTh: string, status?: boolean, isDefault?: boolean, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.permissionsControllerCreate(name, nameEn, descTh, status, isDefault, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['PermissionsApi.permissionsControllerCreate']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {any} limit Number of items per page
         * @param {any} page Page number to retrieve
         * @param {any} [sortBy] Field to sort by (e.g., id, name, email)
         * @param {any} [order] Sort order (ASC or DESC)
         * @param {any} [search] Search term to filter results
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async permissionsControllerFindAll(limit: any, page: any, sortBy?: any, order?: any, search?: any, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.permissionsControllerFindAll(limit, page, sortBy, order, search, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['PermissionsApi.permissionsControllerFindAll']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {boolean} perStatus 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async permissionsControllerFindAllByStatus(perStatus: boolean, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.permissionsControllerFindAllByStatus(perStatus, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['PermissionsApi.permissionsControllerFindAllByStatus']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {number} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async permissionsControllerFindOne(id: number, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.permissionsControllerFindOne(id, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['PermissionsApi.permissionsControllerFindOne']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {number} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async permissionsControllerRemove(id: number, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.permissionsControllerRemove(id, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['PermissionsApi.permissionsControllerRemove']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {number} id 
         * @param {boolean} status สถานะการใช้งานสิทธิ์
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async permissionsControllerSetStatus(id: number, status: boolean, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.permissionsControllerSetStatus(id, status, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['PermissionsApi.permissionsControllerSetStatus']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {number} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async permissionsControllerUpdate(id: number, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.permissionsControllerUpdate(id, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['PermissionsApi.permissionsControllerUpdate']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
    }
};

/**
 * PermissionsApi - factory interface
 * @export
 */
export const PermissionsApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    const localVarFp = PermissionsApiFp(configuration)
    return {
        /**
         * 
         * @param {string} name ชื่อสิทธิ์
         * @param {string} nameEn รายละเอียดสิทธิ์ภาษาอังกฤษ
         * @param {string} descTh รายละเอียดสิทธิ์ภาษาไทย
         * @param {boolean} [status] สถานะการใช้งาน
         * @param {boolean} [isDefault] เป็นสิทธิ์เริ่มต้นหรือไม่
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        permissionsControllerCreate(name: string, nameEn: string, descTh: string, status?: boolean, isDefault?: boolean, options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.permissionsControllerCreate(name, nameEn, descTh, status, isDefault, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {any} limit Number of items per page
         * @param {any} page Page number to retrieve
         * @param {any} [sortBy] Field to sort by (e.g., id, name, email)
         * @param {any} [order] Sort order (ASC or DESC)
         * @param {any} [search] Search term to filter results
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        permissionsControllerFindAll(limit: any, page: any, sortBy?: any, order?: any, search?: any, options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.permissionsControllerFindAll(limit, page, sortBy, order, search, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {boolean} perStatus 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        permissionsControllerFindAllByStatus(perStatus: boolean, options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.permissionsControllerFindAllByStatus(perStatus, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {number} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        permissionsControllerFindOne(id: number, options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.permissionsControllerFindOne(id, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {number} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        permissionsControllerRemove(id: number, options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.permissionsControllerRemove(id, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {number} id 
         * @param {boolean} status สถานะการใช้งานสิทธิ์
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        permissionsControllerSetStatus(id: number, status: boolean, options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.permissionsControllerSetStatus(id, status, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {number} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        permissionsControllerUpdate(id: number, options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.permissionsControllerUpdate(id, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * PermissionsApi - object-oriented interface
 * @export
 * @class PermissionsApi
 * @extends {BaseAPI}
 */
export class PermissionsApi extends BaseAPI {
    /**
     * 
     * @param {string} name ชื่อสิทธิ์
     * @param {string} nameEn รายละเอียดสิทธิ์ภาษาอังกฤษ
     * @param {string} descTh รายละเอียดสิทธิ์ภาษาไทย
     * @param {boolean} [status] สถานะการใช้งาน
     * @param {boolean} [isDefault] เป็นสิทธิ์เริ่มต้นหรือไม่
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof PermissionsApi
     */
    public permissionsControllerCreate(name: string, nameEn: string, descTh: string, status?: boolean, isDefault?: boolean, options?: RawAxiosRequestConfig) {
        return PermissionsApiFp(this.configuration).permissionsControllerCreate(name, nameEn, descTh, status, isDefault, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {any} limit Number of items per page
     * @param {any} page Page number to retrieve
     * @param {any} [sortBy] Field to sort by (e.g., id, name, email)
     * @param {any} [order] Sort order (ASC or DESC)
     * @param {any} [search] Search term to filter results
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof PermissionsApi
     */
    public permissionsControllerFindAll(limit: any, page: any, sortBy?: any, order?: any, search?: any, options?: RawAxiosRequestConfig) {
        return PermissionsApiFp(this.configuration).permissionsControllerFindAll(limit, page, sortBy, order, search, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {boolean} perStatus 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof PermissionsApi
     */
    public permissionsControllerFindAllByStatus(perStatus: boolean, options?: RawAxiosRequestConfig) {
        return PermissionsApiFp(this.configuration).permissionsControllerFindAllByStatus(perStatus, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {number} id 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof PermissionsApi
     */
    public permissionsControllerFindOne(id: number, options?: RawAxiosRequestConfig) {
        return PermissionsApiFp(this.configuration).permissionsControllerFindOne(id, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {number} id 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof PermissionsApi
     */
    public permissionsControllerRemove(id: number, options?: RawAxiosRequestConfig) {
        return PermissionsApiFp(this.configuration).permissionsControllerRemove(id, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {number} id 
     * @param {boolean} status สถานะการใช้งานสิทธิ์
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof PermissionsApi
     */
    public permissionsControllerSetStatus(id: number, status: boolean, options?: RawAxiosRequestConfig) {
        return PermissionsApiFp(this.configuration).permissionsControllerSetStatus(id, status, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {number} id 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof PermissionsApi
     */
    public permissionsControllerUpdate(id: number, options?: RawAxiosRequestConfig) {
        return PermissionsApiFp(this.configuration).permissionsControllerUpdate(id, options).then((request) => request(this.axios, this.basePath));
    }
}

