/* tslint:disable */
/* eslint-disable */
/**
 * HRD Backend API
 * Human Resource Development Backend API Documentation
 *
 * The version of the OpenAPI document: 1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


import type { Configuration } from '../configuration';
import type { AxiosPromise, AxiosInstance, RawAxiosRequestConfig } from 'axios';
import globalAxios from 'axios';
// Some imports not used depending on template conditions
// @ts-ignore
import { DUMMY_BASE_URL, assertParamExists, setApiKeyToObject, setBasicAuthToObject, setBearerAuthToObject, setOAuthToObject, setSearchParams, serializeDataIfNeeded, toPathString, createRequestFunction } from '../common';
// @ts-ignore
import { BASE_PATH, COLLECTION_FORMATS, type RequestArgs, BaseAPI, RequiredError, operationServerMap } from '../base';
/**
 * ASMOptionsApi - axios parameter creator
 * @export
 */
export const ASMOptionsApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * 
         * @summary สร้างตัวเลือกใหม่
         * @param {string} optionText The text content of the option
         * @param {number} sequence The order/position of this option in the assessment
         * @param {File} [imagePath] Path to the image associated with the option
         * @param {number} [value] The score value assigned to this option (for scoring)
         * @param {number} [nextSection] ID of the next section to navigate to if this option is selected (for branching logic)
         * @param {number} [itemBlockId] ID of the item block this option belongs to
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        optionsControllerCreate: async (optionText: string, sequence: number, imagePath?: File, value?: number, nextSection?: number, itemBlockId?: number, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'optionText' is not null or undefined
            assertParamExists('optionsControllerCreate', 'optionText', optionText)
            // verify required parameter 'sequence' is not null or undefined
            assertParamExists('optionsControllerCreate', 'sequence', sequence)
            const localVarPath = `/options`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;
            const localVarFormParams = new ((configuration && configuration.formDataCtor) || FormData)();

            // authentication bearer required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)


            if (optionText !== undefined) { 
                localVarFormParams.append('optionText', optionText as any);
            }
    
            if (imagePath !== undefined) { 
                localVarFormParams.append('imagePath', imagePath as any);
            }
    
            if (value !== undefined) { 
                localVarFormParams.append('value', value as any);
            }
    
            if (nextSection !== undefined) { 
                localVarFormParams.append('nextSection', nextSection as any);
            }
    
            if (sequence !== undefined) { 
                localVarFormParams.append('sequence', sequence as any);
            }
    
            if (itemBlockId !== undefined) { 
                localVarFormParams.append('itemBlockId', itemBlockId as any);
            }
    
    
            localVarHeaderParameter['Content-Type'] = 'multipart/form-data';
    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = localVarFormParams;

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary สร้างตัวเลือกใหม่สำหรับคำถาม
         * @param {number} questionId 
         * @param {string} optionText The text content of the option
         * @param {number} sequence The order/position of this option in the assessment
         * @param {File} [imagePath] Path to the image associated with the option
         * @param {number} [value] The score value assigned to this option (for scoring)
         * @param {number} [nextSection] ID of the next section to navigate to if this option is selected (for branching logic)
         * @param {number} [itemBlockId] ID of the item block this option belongs to
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        optionsControllerCreateForQuestion: async (questionId: number, optionText: string, sequence: number, imagePath?: File, value?: number, nextSection?: number, itemBlockId?: number, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'questionId' is not null or undefined
            assertParamExists('optionsControllerCreateForQuestion', 'questionId', questionId)
            // verify required parameter 'optionText' is not null or undefined
            assertParamExists('optionsControllerCreateForQuestion', 'optionText', optionText)
            // verify required parameter 'sequence' is not null or undefined
            assertParamExists('optionsControllerCreateForQuestion', 'sequence', sequence)
            const localVarPath = `/options/{questionId}`
                .replace(`{${"questionId"}}`, encodeURIComponent(String(questionId)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;
            const localVarFormParams = new ((configuration && configuration.formDataCtor) || FormData)();

            // authentication bearer required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)


            if (optionText !== undefined) { 
                localVarFormParams.append('optionText', optionText as any);
            }
    
            if (imagePath !== undefined) { 
                localVarFormParams.append('imagePath', imagePath as any);
            }
    
            if (value !== undefined) { 
                localVarFormParams.append('value', value as any);
            }
    
            if (nextSection !== undefined) { 
                localVarFormParams.append('nextSection', nextSection as any);
            }
    
            if (sequence !== undefined) { 
                localVarFormParams.append('sequence', sequence as any);
            }
    
            if (itemBlockId !== undefined) { 
                localVarFormParams.append('itemBlockId', itemBlockId as any);
            }
    
    
            localVarHeaderParameter['Content-Type'] = 'multipart/form-data';
    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = localVarFormParams;

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary ดึงข้อมูลตัวเลือกทั้งหมด
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        optionsControllerFindAll: async (options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/options`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication bearer required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {string} optionText 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        optionsControllerFindImagePath: async (optionText: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'optionText' is not null or undefined
            assertParamExists('optionsControllerFindImagePath', 'optionText', optionText)
            const localVarPath = `/options/file/{optionText}`
                .replace(`{${"optionText"}}`, encodeURIComponent(String(optionText)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication bearer required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary ดึงข้อมูลตัวเลือกตาม ID
         * @param {number} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        optionsControllerFindOne: async (id: number, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            assertParamExists('optionsControllerFindOne', 'id', id)
            const localVarPath = `/options/{id}`
                .replace(`{${"id"}}`, encodeURIComponent(String(id)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication bearer required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary ลบตัวเลือก
         * @param {number} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        optionsControllerRemove: async (id: number, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            assertParamExists('optionsControllerRemove', 'id', id)
            const localVarPath = `/options/{id}`
                .replace(`{${"id"}}`, encodeURIComponent(String(id)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'DELETE', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication bearer required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary ลบตัวเลือก
         * @param {string} optionText 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        optionsControllerRemoveByFilename: async (optionText: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'optionText' is not null or undefined
            assertParamExists('optionsControllerRemoveByFilename', 'optionText', optionText)
            const localVarPath = `/options/deleteFile/{optionText}`
                .replace(`{${"optionText"}}`, encodeURIComponent(String(optionText)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'DELETE', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication bearer required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary ลบตัวเลือกทั้งหมดของ item block
         * @param {number} itemBlockId 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        optionsControllerRemoveByItemBlockId: async (itemBlockId: number, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'itemBlockId' is not null or undefined
            assertParamExists('optionsControllerRemoveByItemBlockId', 'itemBlockId', itemBlockId)
            const localVarPath = `/options/item-block/{itemBlockId}`
                .replace(`{${"itemBlockId"}}`, encodeURIComponent(String(itemBlockId)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'DELETE', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication bearer required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary แก้ไขข้อมูล<|im_start|>
         * @param {number} id 
         * @param {string} [optionText] The text content of the option
         * @param {File} [imagePath] Path to the image associated with the option
         * @param {number} [value] The score value assigned to this option (for scoring)
         * @param {number} [nextSection] ID of the next section to navigate to if this option is selected (for branching logic)
         * @param {number} [sequence] The order/position of this option in the assessment
         * @param {number} [itemBlockId] ID of the item block this option belongs to
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        optionsControllerUpdate: async (id: number, optionText?: string, imagePath?: File, value?: number, nextSection?: number, sequence?: number, itemBlockId?: number, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            assertParamExists('optionsControllerUpdate', 'id', id)
            const localVarPath = `/options/{id}`
                .replace(`{${"id"}}`, encodeURIComponent(String(id)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'PATCH', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;
            const localVarFormParams = new ((configuration && configuration.formDataCtor) || FormData)();

            // authentication bearer required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)


            if (optionText !== undefined) { 
                localVarFormParams.append('optionText', optionText as any);
            }
    
            if (imagePath !== undefined) { 
                localVarFormParams.append('imagePath', imagePath as any);
            }
    
            if (value !== undefined) { 
                localVarFormParams.append('value', value as any);
            }
    
            if (nextSection !== undefined) { 
                localVarFormParams.append('nextSection', nextSection as any);
            }
    
            if (sequence !== undefined) { 
                localVarFormParams.append('sequence', sequence as any);
            }
    
            if (itemBlockId !== undefined) { 
                localVarFormParams.append('itemBlockId', itemBlockId as any);
            }
    
    
            localVarHeaderParameter['Content-Type'] = 'multipart/form-data';
    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = localVarFormParams;

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary แก้ไขตัวเลือกสำหรับคำถาม
         * @param {number} questionId 
         * @param {number} optionId 
         * @param {string} [optionText] The text content of the option
         * @param {File} [imagePath] Path to the image associated with the option
         * @param {number} [value] The score value assigned to this option (for scoring)
         * @param {number} [nextSection] ID of the next section to navigate to if this option is selected (for branching logic)
         * @param {number} [sequence] The order/position of this option in the assessment
         * @param {number} [itemBlockId] ID of the item block this option belongs to
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        optionsControllerUpdateForQuestion: async (questionId: number, optionId: number, optionText?: string, imagePath?: File, value?: number, nextSection?: number, sequence?: number, itemBlockId?: number, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'questionId' is not null or undefined
            assertParamExists('optionsControllerUpdateForQuestion', 'questionId', questionId)
            // verify required parameter 'optionId' is not null or undefined
            assertParamExists('optionsControllerUpdateForQuestion', 'optionId', optionId)
            const localVarPath = `/options/{questionId}/{optionId}`
                .replace(`{${"questionId"}}`, encodeURIComponent(String(questionId)))
                .replace(`{${"optionId"}}`, encodeURIComponent(String(optionId)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'PATCH', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;
            const localVarFormParams = new ((configuration && configuration.formDataCtor) || FormData)();

            // authentication bearer required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)


            if (optionText !== undefined) { 
                localVarFormParams.append('optionText', optionText as any);
            }
    
            if (imagePath !== undefined) { 
                localVarFormParams.append('imagePath', imagePath as any);
            }
    
            if (value !== undefined) { 
                localVarFormParams.append('value', value as any);
            }
    
            if (nextSection !== undefined) { 
                localVarFormParams.append('nextSection', nextSection as any);
            }
    
            if (sequence !== undefined) { 
                localVarFormParams.append('sequence', sequence as any);
            }
    
            if (itemBlockId !== undefined) { 
                localVarFormParams.append('itemBlockId', itemBlockId as any);
            }
    
    
            localVarHeaderParameter['Content-Type'] = 'multipart/form-data';
    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = localVarFormParams;

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * ASMOptionsApi - functional programming interface
 * @export
 */
export const ASMOptionsApiFp = function(configuration?: Configuration) {
    const localVarAxiosParamCreator = ASMOptionsApiAxiosParamCreator(configuration)
    return {
        /**
         * 
         * @summary สร้างตัวเลือกใหม่
         * @param {string} optionText The text content of the option
         * @param {number} sequence The order/position of this option in the assessment
         * @param {File} [imagePath] Path to the image associated with the option
         * @param {number} [value] The score value assigned to this option (for scoring)
         * @param {number} [nextSection] ID of the next section to navigate to if this option is selected (for branching logic)
         * @param {number} [itemBlockId] ID of the item block this option belongs to
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async optionsControllerCreate(optionText: string, sequence: number, imagePath?: File, value?: number, nextSection?: number, itemBlockId?: number, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.optionsControllerCreate(optionText, sequence, imagePath, value, nextSection, itemBlockId, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['ASMOptionsApi.optionsControllerCreate']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @summary สร้างตัวเลือกใหม่สำหรับคำถาม
         * @param {number} questionId 
         * @param {string} optionText The text content of the option
         * @param {number} sequence The order/position of this option in the assessment
         * @param {File} [imagePath] Path to the image associated with the option
         * @param {number} [value] The score value assigned to this option (for scoring)
         * @param {number} [nextSection] ID of the next section to navigate to if this option is selected (for branching logic)
         * @param {number} [itemBlockId] ID of the item block this option belongs to
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async optionsControllerCreateForQuestion(questionId: number, optionText: string, sequence: number, imagePath?: File, value?: number, nextSection?: number, itemBlockId?: number, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.optionsControllerCreateForQuestion(questionId, optionText, sequence, imagePath, value, nextSection, itemBlockId, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['ASMOptionsApi.optionsControllerCreateForQuestion']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @summary ดึงข้อมูลตัวเลือกทั้งหมด
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async optionsControllerFindAll(options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.optionsControllerFindAll(options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['ASMOptionsApi.optionsControllerFindAll']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {string} optionText 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async optionsControllerFindImagePath(optionText: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.optionsControllerFindImagePath(optionText, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['ASMOptionsApi.optionsControllerFindImagePath']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @summary ดึงข้อมูลตัวเลือกตาม ID
         * @param {number} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async optionsControllerFindOne(id: number, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.optionsControllerFindOne(id, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['ASMOptionsApi.optionsControllerFindOne']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @summary ลบตัวเลือก
         * @param {number} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async optionsControllerRemove(id: number, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.optionsControllerRemove(id, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['ASMOptionsApi.optionsControllerRemove']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @summary ลบตัวเลือก
         * @param {string} optionText 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async optionsControllerRemoveByFilename(optionText: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.optionsControllerRemoveByFilename(optionText, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['ASMOptionsApi.optionsControllerRemoveByFilename']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @summary ลบตัวเลือกทั้งหมดของ item block
         * @param {number} itemBlockId 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async optionsControllerRemoveByItemBlockId(itemBlockId: number, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.optionsControllerRemoveByItemBlockId(itemBlockId, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['ASMOptionsApi.optionsControllerRemoveByItemBlockId']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @summary แก้ไขข้อมูล<|im_start|>
         * @param {number} id 
         * @param {string} [optionText] The text content of the option
         * @param {File} [imagePath] Path to the image associated with the option
         * @param {number} [value] The score value assigned to this option (for scoring)
         * @param {number} [nextSection] ID of the next section to navigate to if this option is selected (for branching logic)
         * @param {number} [sequence] The order/position of this option in the assessment
         * @param {number} [itemBlockId] ID of the item block this option belongs to
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async optionsControllerUpdate(id: number, optionText?: string, imagePath?: File, value?: number, nextSection?: number, sequence?: number, itemBlockId?: number, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.optionsControllerUpdate(id, optionText, imagePath, value, nextSection, sequence, itemBlockId, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['ASMOptionsApi.optionsControllerUpdate']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @summary แก้ไขตัวเลือกสำหรับคำถาม
         * @param {number} questionId 
         * @param {number} optionId 
         * @param {string} [optionText] The text content of the option
         * @param {File} [imagePath] Path to the image associated with the option
         * @param {number} [value] The score value assigned to this option (for scoring)
         * @param {number} [nextSection] ID of the next section to navigate to if this option is selected (for branching logic)
         * @param {number} [sequence] The order/position of this option in the assessment
         * @param {number} [itemBlockId] ID of the item block this option belongs to
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async optionsControllerUpdateForQuestion(questionId: number, optionId: number, optionText?: string, imagePath?: File, value?: number, nextSection?: number, sequence?: number, itemBlockId?: number, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.optionsControllerUpdateForQuestion(questionId, optionId, optionText, imagePath, value, nextSection, sequence, itemBlockId, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['ASMOptionsApi.optionsControllerUpdateForQuestion']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
    }
};

/**
 * ASMOptionsApi - factory interface
 * @export
 */
export const ASMOptionsApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    const localVarFp = ASMOptionsApiFp(configuration)
    return {
        /**
         * 
         * @summary สร้างตัวเลือกใหม่
         * @param {string} optionText The text content of the option
         * @param {number} sequence The order/position of this option in the assessment
         * @param {File} [imagePath] Path to the image associated with the option
         * @param {number} [value] The score value assigned to this option (for scoring)
         * @param {number} [nextSection] ID of the next section to navigate to if this option is selected (for branching logic)
         * @param {number} [itemBlockId] ID of the item block this option belongs to
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        optionsControllerCreate(optionText: string, sequence: number, imagePath?: File, value?: number, nextSection?: number, itemBlockId?: number, options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.optionsControllerCreate(optionText, sequence, imagePath, value, nextSection, itemBlockId, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary สร้างตัวเลือกใหม่สำหรับคำถาม
         * @param {number} questionId 
         * @param {string} optionText The text content of the option
         * @param {number} sequence The order/position of this option in the assessment
         * @param {File} [imagePath] Path to the image associated with the option
         * @param {number} [value] The score value assigned to this option (for scoring)
         * @param {number} [nextSection] ID of the next section to navigate to if this option is selected (for branching logic)
         * @param {number} [itemBlockId] ID of the item block this option belongs to
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        optionsControllerCreateForQuestion(questionId: number, optionText: string, sequence: number, imagePath?: File, value?: number, nextSection?: number, itemBlockId?: number, options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.optionsControllerCreateForQuestion(questionId, optionText, sequence, imagePath, value, nextSection, itemBlockId, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary ดึงข้อมูลตัวเลือกทั้งหมด
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        optionsControllerFindAll(options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.optionsControllerFindAll(options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {string} optionText 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        optionsControllerFindImagePath(optionText: string, options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.optionsControllerFindImagePath(optionText, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary ดึงข้อมูลตัวเลือกตาม ID
         * @param {number} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        optionsControllerFindOne(id: number, options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.optionsControllerFindOne(id, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary ลบตัวเลือก
         * @param {number} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        optionsControllerRemove(id: number, options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.optionsControllerRemove(id, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary ลบตัวเลือก
         * @param {string} optionText 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        optionsControllerRemoveByFilename(optionText: string, options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.optionsControllerRemoveByFilename(optionText, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary ลบตัวเลือกทั้งหมดของ item block
         * @param {number} itemBlockId 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        optionsControllerRemoveByItemBlockId(itemBlockId: number, options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.optionsControllerRemoveByItemBlockId(itemBlockId, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary แก้ไขข้อมูล<|im_start|>
         * @param {number} id 
         * @param {string} [optionText] The text content of the option
         * @param {File} [imagePath] Path to the image associated with the option
         * @param {number} [value] The score value assigned to this option (for scoring)
         * @param {number} [nextSection] ID of the next section to navigate to if this option is selected (for branching logic)
         * @param {number} [sequence] The order/position of this option in the assessment
         * @param {number} [itemBlockId] ID of the item block this option belongs to
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        optionsControllerUpdate(id: number, optionText?: string, imagePath?: File, value?: number, nextSection?: number, sequence?: number, itemBlockId?: number, options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.optionsControllerUpdate(id, optionText, imagePath, value, nextSection, sequence, itemBlockId, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary แก้ไขตัวเลือกสำหรับคำถาม
         * @param {number} questionId 
         * @param {number} optionId 
         * @param {string} [optionText] The text content of the option
         * @param {File} [imagePath] Path to the image associated with the option
         * @param {number} [value] The score value assigned to this option (for scoring)
         * @param {number} [nextSection] ID of the next section to navigate to if this option is selected (for branching logic)
         * @param {number} [sequence] The order/position of this option in the assessment
         * @param {number} [itemBlockId] ID of the item block this option belongs to
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        optionsControllerUpdateForQuestion(questionId: number, optionId: number, optionText?: string, imagePath?: File, value?: number, nextSection?: number, sequence?: number, itemBlockId?: number, options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.optionsControllerUpdateForQuestion(questionId, optionId, optionText, imagePath, value, nextSection, sequence, itemBlockId, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * ASMOptionsApi - object-oriented interface
 * @export
 * @class ASMOptionsApi
 * @extends {BaseAPI}
 */
export class ASMOptionsApi extends BaseAPI {
    /**
     * 
     * @summary สร้างตัวเลือกใหม่
     * @param {string} optionText The text content of the option
     * @param {number} sequence The order/position of this option in the assessment
     * @param {File} [imagePath] Path to the image associated with the option
     * @param {number} [value] The score value assigned to this option (for scoring)
     * @param {number} [nextSection] ID of the next section to navigate to if this option is selected (for branching logic)
     * @param {number} [itemBlockId] ID of the item block this option belongs to
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ASMOptionsApi
     */
    public optionsControllerCreate(optionText: string, sequence: number, imagePath?: File, value?: number, nextSection?: number, itemBlockId?: number, options?: RawAxiosRequestConfig) {
        return ASMOptionsApiFp(this.configuration).optionsControllerCreate(optionText, sequence, imagePath, value, nextSection, itemBlockId, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @summary สร้างตัวเลือกใหม่สำหรับคำถาม
     * @param {number} questionId 
     * @param {string} optionText The text content of the option
     * @param {number} sequence The order/position of this option in the assessment
     * @param {File} [imagePath] Path to the image associated with the option
     * @param {number} [value] The score value assigned to this option (for scoring)
     * @param {number} [nextSection] ID of the next section to navigate to if this option is selected (for branching logic)
     * @param {number} [itemBlockId] ID of the item block this option belongs to
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ASMOptionsApi
     */
    public optionsControllerCreateForQuestion(questionId: number, optionText: string, sequence: number, imagePath?: File, value?: number, nextSection?: number, itemBlockId?: number, options?: RawAxiosRequestConfig) {
        return ASMOptionsApiFp(this.configuration).optionsControllerCreateForQuestion(questionId, optionText, sequence, imagePath, value, nextSection, itemBlockId, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @summary ดึงข้อมูลตัวเลือกทั้งหมด
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ASMOptionsApi
     */
    public optionsControllerFindAll(options?: RawAxiosRequestConfig) {
        return ASMOptionsApiFp(this.configuration).optionsControllerFindAll(options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {string} optionText 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ASMOptionsApi
     */
    public optionsControllerFindImagePath(optionText: string, options?: RawAxiosRequestConfig) {
        return ASMOptionsApiFp(this.configuration).optionsControllerFindImagePath(optionText, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @summary ดึงข้อมูลตัวเลือกตาม ID
     * @param {number} id 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ASMOptionsApi
     */
    public optionsControllerFindOne(id: number, options?: RawAxiosRequestConfig) {
        return ASMOptionsApiFp(this.configuration).optionsControllerFindOne(id, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @summary ลบตัวเลือก
     * @param {number} id 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ASMOptionsApi
     */
    public optionsControllerRemove(id: number, options?: RawAxiosRequestConfig) {
        return ASMOptionsApiFp(this.configuration).optionsControllerRemove(id, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @summary ลบตัวเลือก
     * @param {string} optionText 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ASMOptionsApi
     */
    public optionsControllerRemoveByFilename(optionText: string, options?: RawAxiosRequestConfig) {
        return ASMOptionsApiFp(this.configuration).optionsControllerRemoveByFilename(optionText, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @summary ลบตัวเลือกทั้งหมดของ item block
     * @param {number} itemBlockId 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ASMOptionsApi
     */
    public optionsControllerRemoveByItemBlockId(itemBlockId: number, options?: RawAxiosRequestConfig) {
        return ASMOptionsApiFp(this.configuration).optionsControllerRemoveByItemBlockId(itemBlockId, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @summary แก้ไขข้อมูล<|im_start|>
     * @param {number} id 
     * @param {string} [optionText] The text content of the option
     * @param {File} [imagePath] Path to the image associated with the option
     * @param {number} [value] The score value assigned to this option (for scoring)
     * @param {number} [nextSection] ID of the next section to navigate to if this option is selected (for branching logic)
     * @param {number} [sequence] The order/position of this option in the assessment
     * @param {number} [itemBlockId] ID of the item block this option belongs to
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ASMOptionsApi
     */
    public optionsControllerUpdate(id: number, optionText?: string, imagePath?: File, value?: number, nextSection?: number, sequence?: number, itemBlockId?: number, options?: RawAxiosRequestConfig) {
        return ASMOptionsApiFp(this.configuration).optionsControllerUpdate(id, optionText, imagePath, value, nextSection, sequence, itemBlockId, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @summary แก้ไขตัวเลือกสำหรับคำถาม
     * @param {number} questionId 
     * @param {number} optionId 
     * @param {string} [optionText] The text content of the option
     * @param {File} [imagePath] Path to the image associated with the option
     * @param {number} [value] The score value assigned to this option (for scoring)
     * @param {number} [nextSection] ID of the next section to navigate to if this option is selected (for branching logic)
     * @param {number} [sequence] The order/position of this option in the assessment
     * @param {number} [itemBlockId] ID of the item block this option belongs to
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ASMOptionsApi
     */
    public optionsControllerUpdateForQuestion(questionId: number, optionId: number, optionText?: string, imagePath?: File, value?: number, nextSection?: number, sequence?: number, itemBlockId?: number, options?: RawAxiosRequestConfig) {
        return ASMOptionsApiFp(this.configuration).optionsControllerUpdateForQuestion(questionId, optionId, optionText, imagePath, value, nextSection, sequence, itemBlockId, options).then((request) => request(this.axios, this.basePath));
    }
}

