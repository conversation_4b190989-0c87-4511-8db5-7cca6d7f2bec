/* tslint:disable */
/* eslint-disable */
/**
 * HRD Backend API
 * Human Resource Development Backend API Documentation
 *
 * The version of the OpenAPI document: 1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


import type { Configuration } from '../configuration';
import type { AxiosPromise, AxiosInstance, RawAxiosRequestConfig } from 'axios';
import globalAxios from 'axios';
// Some imports not used depending on template conditions
// @ts-ignore
import { DUMMY_BASE_URL, assertParamExists, setApiKeyToObject, setBasicAuthToObject, setBearerAuthToObject, setOAuthToObject, setSearchParams, serializeDataIfNeeded, toPathString, createRequestFunction } from '../common';
// @ts-ignore
import { BASE_PATH, COLLECTION_FORMATS, type RequestArgs, BaseAPI, RequiredError, operationServerMap } from '../base';
// @ts-ignore
import type { CreateResponseDto } from '../models';
/**
 * ASMResponsesApi - axios parameter creator
 * @export
 */
export const ASMResponsesApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * 
         * @summary ลบคำตอบ
         * @param {number} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        responsesControllerClear: async (id: number, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            assertParamExists('responsesControllerClear', 'id', id)
            const localVarPath = `/responses/clear/{id}`
                .replace(`{${"id"}}`, encodeURIComponent(String(id)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'DELETE', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication bearer required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary สร้างคำตอบใหม่
         * @param {CreateResponseDto} createResponseDto 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        responsesControllerCreate: async (createResponseDto: CreateResponseDto, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'createResponseDto' is not null or undefined
            assertParamExists('responsesControllerCreate', 'createResponseDto', createResponseDto)
            const localVarPath = `/responses`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication bearer required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(createResponseDto, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary ดึงข้อมูลคำตอบทั้งหมด
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        responsesControllerFindAll: async (options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/responses`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication bearer required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary  ข้อมูลคำตอบทั้งหมดของ submission ตาม ID
         * @param {string} submissionId 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        responsesControllerFindAllBySubmissionId: async (submissionId: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'submissionId' is not null or undefined
            assertParamExists('responsesControllerFindAllBySubmissionId', 'submissionId', submissionId)
            const localVarPath = `/responses/by/submission/{submissionId}`
                .replace(`{${"submissionId"}}`, encodeURIComponent(String(submissionId)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication bearer required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary ดึงข้อมูลคำตอบตาม ID
         * @param {number} submissionId 
         * @param {number} questionId 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        responsesControllerFindAnswer: async (submissionId: number, questionId: number, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'submissionId' is not null or undefined
            assertParamExists('responsesControllerFindAnswer', 'submissionId', submissionId)
            // verify required parameter 'questionId' is not null or undefined
            assertParamExists('responsesControllerFindAnswer', 'questionId', questionId)
            const localVarPath = `/responses/{submissionId}/{questionId}`
                .replace(`{${"submissionId"}}`, encodeURIComponent(String(submissionId)))
                .replace(`{${"questionId"}}`, encodeURIComponent(String(questionId)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication bearer required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {number} submissionId 
         * @param {number} questionId 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        responsesControllerFindCheckboxAnswers: async (submissionId: number, questionId: number, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'submissionId' is not null or undefined
            assertParamExists('responsesControllerFindCheckboxAnswers', 'submissionId', submissionId)
            // verify required parameter 'questionId' is not null or undefined
            assertParamExists('responsesControllerFindCheckboxAnswers', 'questionId', questionId)
            const localVarPath = `/responses/checkbox/{submissionId}/{questionId}`
                .replace(`{${"submissionId"}}`, encodeURIComponent(String(submissionId)))
                .replace(`{${"questionId"}}`, encodeURIComponent(String(questionId)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication bearer required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary ดึงข้อมูลคำตอบตาม ID
         * @param {number} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        responsesControllerFindOne: async (id: number, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            assertParamExists('responsesControllerFindOne', 'id', id)
            const localVarPath = `/responses/{id}`
                .replace(`{${"id"}}`, encodeURIComponent(String(id)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication bearer required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary ดึงข้อมูลคำตอบตาม ID
         * @param {number} submissionId 
         * @param {number} questionId 
         * @param {number} selectedOptionId 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        responsesControllerFindRemoveCheckBoxAnswer: async (submissionId: number, questionId: number, selectedOptionId: number, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'submissionId' is not null or undefined
            assertParamExists('responsesControllerFindRemoveCheckBoxAnswer', 'submissionId', submissionId)
            // verify required parameter 'questionId' is not null or undefined
            assertParamExists('responsesControllerFindRemoveCheckBoxAnswer', 'questionId', questionId)
            // verify required parameter 'selectedOptionId' is not null or undefined
            assertParamExists('responsesControllerFindRemoveCheckBoxAnswer', 'selectedOptionId', selectedOptionId)
            const localVarPath = `/responses/{submissionId}/{questionId}/{selectedOptionId}`
                .replace(`{${"submissionId"}}`, encodeURIComponent(String(submissionId)))
                .replace(`{${"questionId"}}`, encodeURIComponent(String(questionId)))
                .replace(`{${"selectedOptionId"}}`, encodeURIComponent(String(selectedOptionId)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication bearer required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary ลบคำตอบ
         * @param {number} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        responsesControllerRemove: async (id: number, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            assertParamExists('responsesControllerRemove', 'id', id)
            const localVarPath = `/responses/{id}`
                .replace(`{${"id"}}`, encodeURIComponent(String(id)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'DELETE', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication bearer required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary แก้ไขคำตอบ
         * @param {number} id 
         * @param {object} body 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        responsesControllerUpdate: async (id: number, body: object, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            assertParamExists('responsesControllerUpdate', 'id', id)
            // verify required parameter 'body' is not null or undefined
            assertParamExists('responsesControllerUpdate', 'body', body)
            const localVarPath = `/responses/{id}`
                .replace(`{${"id"}}`, encodeURIComponent(String(id)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'PATCH', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication bearer required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(body, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {CreateResponseDto} createResponseDto 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        responsesControllerUserSaveQuizResponse: async (createResponseDto: CreateResponseDto, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'createResponseDto' is not null or undefined
            assertParamExists('responsesControllerUserSaveQuizResponse', 'createResponseDto', createResponseDto)
            const localVarPath = `/responses/quiz/save-response`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication bearer required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(createResponseDto, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * ASMResponsesApi - functional programming interface
 * @export
 */
export const ASMResponsesApiFp = function(configuration?: Configuration) {
    const localVarAxiosParamCreator = ASMResponsesApiAxiosParamCreator(configuration)
    return {
        /**
         * 
         * @summary ลบคำตอบ
         * @param {number} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async responsesControllerClear(id: number, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.responsesControllerClear(id, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['ASMResponsesApi.responsesControllerClear']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @summary สร้างคำตอบใหม่
         * @param {CreateResponseDto} createResponseDto 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async responsesControllerCreate(createResponseDto: CreateResponseDto, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<any>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.responsesControllerCreate(createResponseDto, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['ASMResponsesApi.responsesControllerCreate']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @summary ดึงข้อมูลคำตอบทั้งหมด
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async responsesControllerFindAll(options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.responsesControllerFindAll(options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['ASMResponsesApi.responsesControllerFindAll']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @summary  ข้อมูลคำตอบทั้งหมดของ submission ตาม ID
         * @param {string} submissionId 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async responsesControllerFindAllBySubmissionId(submissionId: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.responsesControllerFindAllBySubmissionId(submissionId, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['ASMResponsesApi.responsesControllerFindAllBySubmissionId']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @summary ดึงข้อมูลคำตอบตาม ID
         * @param {number} submissionId 
         * @param {number} questionId 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async responsesControllerFindAnswer(submissionId: number, questionId: number, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.responsesControllerFindAnswer(submissionId, questionId, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['ASMResponsesApi.responsesControllerFindAnswer']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {number} submissionId 
         * @param {number} questionId 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async responsesControllerFindCheckboxAnswers(submissionId: number, questionId: number, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.responsesControllerFindCheckboxAnswers(submissionId, questionId, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['ASMResponsesApi.responsesControllerFindCheckboxAnswers']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @summary ดึงข้อมูลคำตอบตาม ID
         * @param {number} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async responsesControllerFindOne(id: number, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.responsesControllerFindOne(id, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['ASMResponsesApi.responsesControllerFindOne']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @summary ดึงข้อมูลคำตอบตาม ID
         * @param {number} submissionId 
         * @param {number} questionId 
         * @param {number} selectedOptionId 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async responsesControllerFindRemoveCheckBoxAnswer(submissionId: number, questionId: number, selectedOptionId: number, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.responsesControllerFindRemoveCheckBoxAnswer(submissionId, questionId, selectedOptionId, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['ASMResponsesApi.responsesControllerFindRemoveCheckBoxAnswer']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @summary ลบคำตอบ
         * @param {number} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async responsesControllerRemove(id: number, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.responsesControllerRemove(id, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['ASMResponsesApi.responsesControllerRemove']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @summary แก้ไขคำตอบ
         * @param {number} id 
         * @param {object} body 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async responsesControllerUpdate(id: number, body: object, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<any>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.responsesControllerUpdate(id, body, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['ASMResponsesApi.responsesControllerUpdate']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {CreateResponseDto} createResponseDto 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async responsesControllerUserSaveQuizResponse(createResponseDto: CreateResponseDto, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.responsesControllerUserSaveQuizResponse(createResponseDto, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['ASMResponsesApi.responsesControllerUserSaveQuizResponse']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
    }
};

/**
 * ASMResponsesApi - factory interface
 * @export
 */
export const ASMResponsesApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    const localVarFp = ASMResponsesApiFp(configuration)
    return {
        /**
         * 
         * @summary ลบคำตอบ
         * @param {number} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        responsesControllerClear(id: number, options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.responsesControllerClear(id, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary สร้างคำตอบใหม่
         * @param {CreateResponseDto} createResponseDto 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        responsesControllerCreate(createResponseDto: CreateResponseDto, options?: RawAxiosRequestConfig): AxiosPromise<any> {
            return localVarFp.responsesControllerCreate(createResponseDto, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary ดึงข้อมูลคำตอบทั้งหมด
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        responsesControllerFindAll(options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.responsesControllerFindAll(options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary  ข้อมูลคำตอบทั้งหมดของ submission ตาม ID
         * @param {string} submissionId 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        responsesControllerFindAllBySubmissionId(submissionId: string, options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.responsesControllerFindAllBySubmissionId(submissionId, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary ดึงข้อมูลคำตอบตาม ID
         * @param {number} submissionId 
         * @param {number} questionId 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        responsesControllerFindAnswer(submissionId: number, questionId: number, options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.responsesControllerFindAnswer(submissionId, questionId, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {number} submissionId 
         * @param {number} questionId 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        responsesControllerFindCheckboxAnswers(submissionId: number, questionId: number, options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.responsesControllerFindCheckboxAnswers(submissionId, questionId, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary ดึงข้อมูลคำตอบตาม ID
         * @param {number} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        responsesControllerFindOne(id: number, options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.responsesControllerFindOne(id, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary ดึงข้อมูลคำตอบตาม ID
         * @param {number} submissionId 
         * @param {number} questionId 
         * @param {number} selectedOptionId 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        responsesControllerFindRemoveCheckBoxAnswer(submissionId: number, questionId: number, selectedOptionId: number, options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.responsesControllerFindRemoveCheckBoxAnswer(submissionId, questionId, selectedOptionId, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary ลบคำตอบ
         * @param {number} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        responsesControllerRemove(id: number, options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.responsesControllerRemove(id, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary แก้ไขคำตอบ
         * @param {number} id 
         * @param {object} body 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        responsesControllerUpdate(id: number, body: object, options?: RawAxiosRequestConfig): AxiosPromise<any> {
            return localVarFp.responsesControllerUpdate(id, body, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {CreateResponseDto} createResponseDto 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        responsesControllerUserSaveQuizResponse(createResponseDto: CreateResponseDto, options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.responsesControllerUserSaveQuizResponse(createResponseDto, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * ASMResponsesApi - object-oriented interface
 * @export
 * @class ASMResponsesApi
 * @extends {BaseAPI}
 */
export class ASMResponsesApi extends BaseAPI {
    /**
     * 
     * @summary ลบคำตอบ
     * @param {number} id 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ASMResponsesApi
     */
    public responsesControllerClear(id: number, options?: RawAxiosRequestConfig) {
        return ASMResponsesApiFp(this.configuration).responsesControllerClear(id, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @summary สร้างคำตอบใหม่
     * @param {CreateResponseDto} createResponseDto 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ASMResponsesApi
     */
    public responsesControllerCreate(createResponseDto: CreateResponseDto, options?: RawAxiosRequestConfig) {
        return ASMResponsesApiFp(this.configuration).responsesControllerCreate(createResponseDto, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @summary ดึงข้อมูลคำตอบทั้งหมด
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ASMResponsesApi
     */
    public responsesControllerFindAll(options?: RawAxiosRequestConfig) {
        return ASMResponsesApiFp(this.configuration).responsesControllerFindAll(options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @summary  ข้อมูลคำตอบทั้งหมดของ submission ตาม ID
     * @param {string} submissionId 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ASMResponsesApi
     */
    public responsesControllerFindAllBySubmissionId(submissionId: string, options?: RawAxiosRequestConfig) {
        return ASMResponsesApiFp(this.configuration).responsesControllerFindAllBySubmissionId(submissionId, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @summary ดึงข้อมูลคำตอบตาม ID
     * @param {number} submissionId 
     * @param {number} questionId 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ASMResponsesApi
     */
    public responsesControllerFindAnswer(submissionId: number, questionId: number, options?: RawAxiosRequestConfig) {
        return ASMResponsesApiFp(this.configuration).responsesControllerFindAnswer(submissionId, questionId, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {number} submissionId 
     * @param {number} questionId 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ASMResponsesApi
     */
    public responsesControllerFindCheckboxAnswers(submissionId: number, questionId: number, options?: RawAxiosRequestConfig) {
        return ASMResponsesApiFp(this.configuration).responsesControllerFindCheckboxAnswers(submissionId, questionId, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @summary ดึงข้อมูลคำตอบตาม ID
     * @param {number} id 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ASMResponsesApi
     */
    public responsesControllerFindOne(id: number, options?: RawAxiosRequestConfig) {
        return ASMResponsesApiFp(this.configuration).responsesControllerFindOne(id, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @summary ดึงข้อมูลคำตอบตาม ID
     * @param {number} submissionId 
     * @param {number} questionId 
     * @param {number} selectedOptionId 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ASMResponsesApi
     */
    public responsesControllerFindRemoveCheckBoxAnswer(submissionId: number, questionId: number, selectedOptionId: number, options?: RawAxiosRequestConfig) {
        return ASMResponsesApiFp(this.configuration).responsesControllerFindRemoveCheckBoxAnswer(submissionId, questionId, selectedOptionId, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @summary ลบคำตอบ
     * @param {number} id 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ASMResponsesApi
     */
    public responsesControllerRemove(id: number, options?: RawAxiosRequestConfig) {
        return ASMResponsesApiFp(this.configuration).responsesControllerRemove(id, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @summary แก้ไขคำตอบ
     * @param {number} id 
     * @param {object} body 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ASMResponsesApi
     */
    public responsesControllerUpdate(id: number, body: object, options?: RawAxiosRequestConfig) {
        return ASMResponsesApiFp(this.configuration).responsesControllerUpdate(id, body, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {CreateResponseDto} createResponseDto 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ASMResponsesApi
     */
    public responsesControllerUserSaveQuizResponse(createResponseDto: CreateResponseDto, options?: RawAxiosRequestConfig) {
        return ASMResponsesApiFp(this.configuration).responsesControllerUserSaveQuizResponse(createResponseDto, options).then((request) => request(this.axios, this.basePath));
    }
}

