/* tslint:disable */
/* eslint-disable */
/**
 * HRD Backend API
 * Human Resource Development Backend API Documentation
 *
 * The version of the OpenAPI document: 1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


import type { Configuration } from '../configuration';
import type { AxiosPromise, AxiosInstance, RawAxiosRequestConfig } from 'axios';
import globalAxios from 'axios';
// Some imports not used depending on template conditions
// @ts-ignore
import { DUMMY_BASE_URL, assertParamExists, setApiKeyToObject, setBasicAuthToObject, setBearerAuthToObject, setOAuthToObject, setSearchParams, serializeDataIfNeeded, toPathString, createRequestFunction } from '../common';
// @ts-ignore
import { BASE_PATH, COLLECTION_FORMATS, type RequestArgs, BaseAPI, RequiredError, operationServerMap } from '../base';
// @ts-ignore
import type { AuthControllerSignInRequest } from '../models';
// @ts-ignore
import type { AuthControllerSimpleSignInRequest } from '../models';
/**
 * AuthenticationApi - axios parameter creator
 * @export
 */
export const AuthenticationApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * 
         * @summary Legacy encrypted login (paused for now)
         * @param {AuthControllerSignInRequest} authControllerSignInRequest 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        authControllerSignIn: async (authControllerSignInRequest: AuthControllerSignInRequest, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'authControllerSignInRequest' is not null or undefined
            assertParamExists('authControllerSignIn', 'authControllerSignInRequest', authControllerSignInRequest)
            const localVarPath = `/auth/loginBuu`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(authControllerSignInRequest, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary Simple login with username and password
         * @param {AuthControllerSimpleSignInRequest} authControllerSimpleSignInRequest 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        authControllerSimpleSignIn: async (authControllerSimpleSignInRequest: AuthControllerSimpleSignInRequest, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'authControllerSimpleSignInRequest' is not null or undefined
            assertParamExists('authControllerSimpleSignIn', 'authControllerSimpleSignInRequest', authControllerSimpleSignInRequest)
            const localVarPath = `/auth/login`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(authControllerSimpleSignInRequest, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * AuthenticationApi - functional programming interface
 * @export
 */
export const AuthenticationApiFp = function(configuration?: Configuration) {
    const localVarAxiosParamCreator = AuthenticationApiAxiosParamCreator(configuration)
    return {
        /**
         * 
         * @summary Legacy encrypted login (paused for now)
         * @param {AuthControllerSignInRequest} authControllerSignInRequest 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async authControllerSignIn(authControllerSignInRequest: AuthControllerSignInRequest, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.authControllerSignIn(authControllerSignInRequest, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['AuthenticationApi.authControllerSignIn']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @summary Simple login with username and password
         * @param {AuthControllerSimpleSignInRequest} authControllerSimpleSignInRequest 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async authControllerSimpleSignIn(authControllerSimpleSignInRequest: AuthControllerSimpleSignInRequest, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.authControllerSimpleSignIn(authControllerSimpleSignInRequest, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['AuthenticationApi.authControllerSimpleSignIn']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
    }
};

/**
 * AuthenticationApi - factory interface
 * @export
 */
export const AuthenticationApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    const localVarFp = AuthenticationApiFp(configuration)
    return {
        /**
         * 
         * @summary Legacy encrypted login (paused for now)
         * @param {AuthControllerSignInRequest} authControllerSignInRequest 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        authControllerSignIn(authControllerSignInRequest: AuthControllerSignInRequest, options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.authControllerSignIn(authControllerSignInRequest, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary Simple login with username and password
         * @param {AuthControllerSimpleSignInRequest} authControllerSimpleSignInRequest 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        authControllerSimpleSignIn(authControllerSimpleSignInRequest: AuthControllerSimpleSignInRequest, options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.authControllerSimpleSignIn(authControllerSimpleSignInRequest, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * AuthenticationApi - object-oriented interface
 * @export
 * @class AuthenticationApi
 * @extends {BaseAPI}
 */
export class AuthenticationApi extends BaseAPI {
    /**
     * 
     * @summary Legacy encrypted login (paused for now)
     * @param {AuthControllerSignInRequest} authControllerSignInRequest 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof AuthenticationApi
     */
    public authControllerSignIn(authControllerSignInRequest: AuthControllerSignInRequest, options?: RawAxiosRequestConfig) {
        return AuthenticationApiFp(this.configuration).authControllerSignIn(authControllerSignInRequest, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @summary Simple login with username and password
     * @param {AuthControllerSimpleSignInRequest} authControllerSimpleSignInRequest 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof AuthenticationApi
     */
    public authControllerSimpleSignIn(authControllerSimpleSignInRequest: AuthControllerSimpleSignInRequest, options?: RawAxiosRequestConfig) {
        return AuthenticationApiFp(this.configuration).authControllerSimpleSignIn(authControllerSimpleSignInRequest, options).then((request) => request(this.axios, this.basePath));
    }
}

