/* tslint:disable */
/* eslint-disable */
/**
 * HRD Backend API
 * Human Resource Development Backend API Documentation
 *
 * The version of the OpenAPI document: 1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


import type { Configuration } from '../configuration';
import type { AxiosPromise, AxiosInstance, RawAxiosRequestConfig } from 'axios';
import globalAxios from 'axios';
// Some imports not used depending on template conditions
// @ts-ignore
import { DUMMY_BASE_URL, assertParamExists, setApiKeyToObject, setBasicAuthToObject, setBearerAuthToObject, setOAuthToObject, setSearchParams, serializeDataIfNeeded, toPathString, createRequestFunction } from '../common';
// @ts-ignore
import { BASE_PATH, COLLECTION_FORMATS, type RequestArgs, BaseAPI, RequiredError, operationServerMap } from '../base';
// @ts-ignore
import type { CreateTypePlanDto } from '../models';
// @ts-ignore
import type { RemoveSkillDto } from '../models';
// @ts-ignore
import type { SelectSkillsDto } from '../models';
/**
 * TypePlansApi - axios parameter creator
 * @export
 */
export const TypePlansApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * 
         * @param {CreateTypePlanDto} createTypePlanDto 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        typePlansControllerCreate: async (createTypePlanDto: CreateTypePlanDto, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'createTypePlanDto' is not null or undefined
            assertParamExists('typePlansControllerCreate', 'createTypePlanDto', createTypePlanDto)
            const localVarPath = `/type-plans`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication bearer required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(createTypePlanDto, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        typePlansControllerFindAll: async (options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/type-plans`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication bearer required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {string} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        typePlansControllerFindOne: async (id: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            assertParamExists('typePlansControllerFindOne', 'id', id)
            const localVarPath = `/type-plans/{id}`
                .replace(`{${"id"}}`, encodeURIComponent(String(id)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication bearer required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {RemoveSkillDto} removeSkillDto 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        typePlansControllerRemoveSkillFromType: async (removeSkillDto: RemoveSkillDto, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'removeSkillDto' is not null or undefined
            assertParamExists('typePlansControllerRemoveSkillFromType', 'removeSkillDto', removeSkillDto)
            const localVarPath = `/type-plans/remove-skill`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'DELETE', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication bearer required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(removeSkillDto, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {SelectSkillsDto} selectSkillsDto 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        typePlansControllerSelectSkillToType: async (selectSkillsDto: SelectSkillsDto, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'selectSkillsDto' is not null or undefined
            assertParamExists('typePlansControllerSelectSkillToType', 'selectSkillsDto', selectSkillsDto)
            const localVarPath = `/type-plans/select-skill`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication bearer required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(selectSkillsDto, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * TypePlansApi - functional programming interface
 * @export
 */
export const TypePlansApiFp = function(configuration?: Configuration) {
    const localVarAxiosParamCreator = TypePlansApiAxiosParamCreator(configuration)
    return {
        /**
         * 
         * @param {CreateTypePlanDto} createTypePlanDto 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async typePlansControllerCreate(createTypePlanDto: CreateTypePlanDto, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.typePlansControllerCreate(createTypePlanDto, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['TypePlansApi.typePlansControllerCreate']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async typePlansControllerFindAll(options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.typePlansControllerFindAll(options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['TypePlansApi.typePlansControllerFindAll']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {string} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async typePlansControllerFindOne(id: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.typePlansControllerFindOne(id, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['TypePlansApi.typePlansControllerFindOne']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {RemoveSkillDto} removeSkillDto 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async typePlansControllerRemoveSkillFromType(removeSkillDto: RemoveSkillDto, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.typePlansControllerRemoveSkillFromType(removeSkillDto, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['TypePlansApi.typePlansControllerRemoveSkillFromType']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {SelectSkillsDto} selectSkillsDto 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async typePlansControllerSelectSkillToType(selectSkillsDto: SelectSkillsDto, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.typePlansControllerSelectSkillToType(selectSkillsDto, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['TypePlansApi.typePlansControllerSelectSkillToType']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
    }
};

/**
 * TypePlansApi - factory interface
 * @export
 */
export const TypePlansApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    const localVarFp = TypePlansApiFp(configuration)
    return {
        /**
         * 
         * @param {CreateTypePlanDto} createTypePlanDto 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        typePlansControllerCreate(createTypePlanDto: CreateTypePlanDto, options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.typePlansControllerCreate(createTypePlanDto, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        typePlansControllerFindAll(options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.typePlansControllerFindAll(options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {string} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        typePlansControllerFindOne(id: string, options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.typePlansControllerFindOne(id, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {RemoveSkillDto} removeSkillDto 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        typePlansControllerRemoveSkillFromType(removeSkillDto: RemoveSkillDto, options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.typePlansControllerRemoveSkillFromType(removeSkillDto, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {SelectSkillsDto} selectSkillsDto 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        typePlansControllerSelectSkillToType(selectSkillsDto: SelectSkillsDto, options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.typePlansControllerSelectSkillToType(selectSkillsDto, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * TypePlansApi - object-oriented interface
 * @export
 * @class TypePlansApi
 * @extends {BaseAPI}
 */
export class TypePlansApi extends BaseAPI {
    /**
     * 
     * @param {CreateTypePlanDto} createTypePlanDto 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof TypePlansApi
     */
    public typePlansControllerCreate(createTypePlanDto: CreateTypePlanDto, options?: RawAxiosRequestConfig) {
        return TypePlansApiFp(this.configuration).typePlansControllerCreate(createTypePlanDto, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof TypePlansApi
     */
    public typePlansControllerFindAll(options?: RawAxiosRequestConfig) {
        return TypePlansApiFp(this.configuration).typePlansControllerFindAll(options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {string} id 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof TypePlansApi
     */
    public typePlansControllerFindOne(id: string, options?: RawAxiosRequestConfig) {
        return TypePlansApiFp(this.configuration).typePlansControllerFindOne(id, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {RemoveSkillDto} removeSkillDto 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof TypePlansApi
     */
    public typePlansControllerRemoveSkillFromType(removeSkillDto: RemoveSkillDto, options?: RawAxiosRequestConfig) {
        return TypePlansApiFp(this.configuration).typePlansControllerRemoveSkillFromType(removeSkillDto, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {SelectSkillsDto} selectSkillsDto 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof TypePlansApi
     */
    public typePlansControllerSelectSkillToType(selectSkillsDto: SelectSkillsDto, options?: RawAxiosRequestConfig) {
        return TypePlansApiFp(this.configuration).typePlansControllerSelectSkillToType(selectSkillsDto, options).then((request) => request(this.axios, this.basePath));
    }
}

