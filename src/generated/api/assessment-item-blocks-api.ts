/* tslint:disable */
/* eslint-disable */
/**
 * HRD Backend API
 * Human Resource Development Backend API Documentation
 *
 * The version of the OpenAPI document: 1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


import type { Configuration } from '../configuration';
import type { AxiosPromise, AxiosInstance, RawAxiosRequestConfig } from 'axios';
import globalAxios from 'axios';
// Some imports not used depending on template conditions
// @ts-ignore
import { DUMMY_BASE_URL, assertParamExists, setApiKeyToObject, setBasicAuthToObject, setBearerAuthToObject, setOAuthToObject, setSearchParams, serializeDataIfNeeded, toPathString, createRequestFunction } from '../common';
// @ts-ignore
import { BASE_PATH, COLLECTION_FORMATS, type RequestArgs, BaseAPI, RequiredError, operationServerMap } from '../base';
// @ts-ignore
import type { BulkUpdateItemBlockSequencesDto } from '../models';
// @ts-ignore
import type { ItemBlockType } from '../models';
/**
 * AssessmentItemBlocksApi - axios parameter creator
 * @export
 */
export const AssessmentItemBlocksApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        itemBlocksControllerCreate: async (options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/item-blocks/block`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication bearer required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * Creates a complete copy of an existing Item Block with all its content (atomic operation)
         * @summary Duplicate an existing Item Block
         * @param {number} sourceId 
         * @param {number} assessmentId 
         * @param {number} [sequence] 
         * @param {number} [section] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        itemBlocksControllerDuplicateBlock: async (sourceId: number, assessmentId: number, sequence?: number, section?: number, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'sourceId' is not null or undefined
            assertParamExists('itemBlocksControllerDuplicateBlock', 'sourceId', sourceId)
            // verify required parameter 'assessmentId' is not null or undefined
            assertParamExists('itemBlocksControllerDuplicateBlock', 'assessmentId', assessmentId)
            const localVarPath = `/item-blocks/{sourceId}/duplicate`
                .replace(`{${"sourceId"}}`, encodeURIComponent(String(sourceId)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;
            const localVarFormParams = new ((configuration && configuration.formDataCtor) || FormData)();

            // authentication bearer required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)


            if (assessmentId !== undefined) { 
                localVarFormParams.append('assessmentId', assessmentId as any);
            }
    
            if (sequence !== undefined) { 
                localVarFormParams.append('sequence', sequence as any);
            }
    
            if (section !== undefined) { 
                localVarFormParams.append('section', section as any);
            }
    
    
            localVarHeaderParameter['Content-Type'] = 'multipart/form-data';
    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = localVarFormParams;

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {number} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        itemBlocksControllerFindItemOne: async (id: number, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            assertParamExists('itemBlocksControllerFindItemOne', 'id', id)
            const localVarPath = `/item-blocks/{id}`
                .replace(`{${"id"}}`, encodeURIComponent(String(id)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication bearer required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {number} assessmentId 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        itemBlocksControllerFindOne: async (assessmentId: number, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'assessmentId' is not null or undefined
            assertParamExists('itemBlocksControllerFindOne', 'assessmentId', assessmentId)
            const localVarPath = `/item-blocks/{assessmentId}/block`
                .replace(`{${"assessmentId"}}`, encodeURIComponent(String(assessmentId)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication bearer required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {number} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        itemBlocksControllerRemove: async (id: number, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            assertParamExists('itemBlocksControllerRemove', 'id', id)
            const localVarPath = `/item-blocks/{id}`
                .replace(`{${"id"}}`, encodeURIComponent(String(id)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'DELETE', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication bearer required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {number} submissionId 
         * @param {number} sequence 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        itemBlocksControllerSequenceQuestion: async (submissionId: number, sequence: number, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'submissionId' is not null or undefined
            assertParamExists('itemBlocksControllerSequenceQuestion', 'submissionId', submissionId)
            // verify required parameter 'sequence' is not null or undefined
            assertParamExists('itemBlocksControllerSequenceQuestion', 'sequence', sequence)
            const localVarPath = `/item-blocks/quiz/sequence/{submissionId}/{sequence}`
                .replace(`{${"submissionId"}}`, encodeURIComponent(String(submissionId)))
                .replace(`{${"sequence"}}`, encodeURIComponent(String(sequence)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication bearer required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * อัปเดตItem Block (Evaluate) ตาม template ที่กำหนด
         * @summary อัปเดตItem Block
         * @param {string} id 
         * @param {number} [sequence] The order/position of this item block in the assessment
         * @param {number} [section] The section number this item block belongs to
         * @param {ItemBlockType} [type] The type of item block (RADIO, CHECKBOX, TEXTFIELD, etc.)
         * @param {boolean} [isRequired] Whether this item block is required to be answered
         * @param {number} [assessmentId] ID of the assessment this item block belongs to
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        itemBlocksControllerUpdate: async (id: string, sequence?: number, section?: number, type?: ItemBlockType, isRequired?: boolean, assessmentId?: number, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            assertParamExists('itemBlocksControllerUpdate', 'id', id)
            const localVarPath = `/item-blocks/{id}`
                .replace(`{${"id"}}`, encodeURIComponent(String(id)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'PATCH', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;
            const localVarFormParams = new ((configuration && configuration.formDataCtor) || FormData)();

            // authentication bearer required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)


            if (sequence !== undefined) { 
                localVarFormParams.append('sequence', sequence as any);
            }
    
            if (section !== undefined) { 
                localVarFormParams.append('section', section as any);
            }
    
            if (type !== undefined) { 
                localVarFormParams.append('type', type as any);
            }
    
            if (isRequired !== undefined) { 
                localVarFormParams.append('isRequired', String(isRequired) as any);
            }
    
            if (assessmentId !== undefined) { 
                localVarFormParams.append('assessmentId', assessmentId as any);
            }
    
    
            localVarHeaderParameter['Content-Type'] = 'multipart/form-data';
    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = localVarFormParams;

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {BulkUpdateItemBlockSequencesDto} bulkUpdateItemBlockSequencesDto 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        itemBlocksControllerUpdateSequences: async (bulkUpdateItemBlockSequencesDto: BulkUpdateItemBlockSequencesDto, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'bulkUpdateItemBlockSequencesDto' is not null or undefined
            assertParamExists('itemBlocksControllerUpdateSequences', 'bulkUpdateItemBlockSequencesDto', bulkUpdateItemBlockSequencesDto)
            const localVarPath = `/item-blocks/update/sequences`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'PATCH', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication bearer required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(bulkUpdateItemBlockSequencesDto, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * AssessmentItemBlocksApi - functional programming interface
 * @export
 */
export const AssessmentItemBlocksApiFp = function(configuration?: Configuration) {
    const localVarAxiosParamCreator = AssessmentItemBlocksApiAxiosParamCreator(configuration)
    return {
        /**
         * 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async itemBlocksControllerCreate(options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.itemBlocksControllerCreate(options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['AssessmentItemBlocksApi.itemBlocksControllerCreate']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * Creates a complete copy of an existing Item Block with all its content (atomic operation)
         * @summary Duplicate an existing Item Block
         * @param {number} sourceId 
         * @param {number} assessmentId 
         * @param {number} [sequence] 
         * @param {number} [section] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async itemBlocksControllerDuplicateBlock(sourceId: number, assessmentId: number, sequence?: number, section?: number, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.itemBlocksControllerDuplicateBlock(sourceId, assessmentId, sequence, section, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['AssessmentItemBlocksApi.itemBlocksControllerDuplicateBlock']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {number} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async itemBlocksControllerFindItemOne(id: number, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.itemBlocksControllerFindItemOne(id, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['AssessmentItemBlocksApi.itemBlocksControllerFindItemOne']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {number} assessmentId 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async itemBlocksControllerFindOne(assessmentId: number, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.itemBlocksControllerFindOne(assessmentId, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['AssessmentItemBlocksApi.itemBlocksControllerFindOne']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {number} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async itemBlocksControllerRemove(id: number, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.itemBlocksControllerRemove(id, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['AssessmentItemBlocksApi.itemBlocksControllerRemove']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {number} submissionId 
         * @param {number} sequence 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async itemBlocksControllerSequenceQuestion(submissionId: number, sequence: number, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.itemBlocksControllerSequenceQuestion(submissionId, sequence, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['AssessmentItemBlocksApi.itemBlocksControllerSequenceQuestion']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * อัปเดตItem Block (Evaluate) ตาม template ที่กำหนด
         * @summary อัปเดตItem Block
         * @param {string} id 
         * @param {number} [sequence] The order/position of this item block in the assessment
         * @param {number} [section] The section number this item block belongs to
         * @param {ItemBlockType} [type] The type of item block (RADIO, CHECKBOX, TEXTFIELD, etc.)
         * @param {boolean} [isRequired] Whether this item block is required to be answered
         * @param {number} [assessmentId] ID of the assessment this item block belongs to
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async itemBlocksControllerUpdate(id: string, sequence?: number, section?: number, type?: ItemBlockType, isRequired?: boolean, assessmentId?: number, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.itemBlocksControllerUpdate(id, sequence, section, type, isRequired, assessmentId, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['AssessmentItemBlocksApi.itemBlocksControllerUpdate']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {BulkUpdateItemBlockSequencesDto} bulkUpdateItemBlockSequencesDto 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async itemBlocksControllerUpdateSequences(bulkUpdateItemBlockSequencesDto: BulkUpdateItemBlockSequencesDto, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.itemBlocksControllerUpdateSequences(bulkUpdateItemBlockSequencesDto, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['AssessmentItemBlocksApi.itemBlocksControllerUpdateSequences']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
    }
};

/**
 * AssessmentItemBlocksApi - factory interface
 * @export
 */
export const AssessmentItemBlocksApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    const localVarFp = AssessmentItemBlocksApiFp(configuration)
    return {
        /**
         * 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        itemBlocksControllerCreate(options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.itemBlocksControllerCreate(options).then((request) => request(axios, basePath));
        },
        /**
         * Creates a complete copy of an existing Item Block with all its content (atomic operation)
         * @summary Duplicate an existing Item Block
         * @param {number} sourceId 
         * @param {number} assessmentId 
         * @param {number} [sequence] 
         * @param {number} [section] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        itemBlocksControllerDuplicateBlock(sourceId: number, assessmentId: number, sequence?: number, section?: number, options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.itemBlocksControllerDuplicateBlock(sourceId, assessmentId, sequence, section, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {number} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        itemBlocksControllerFindItemOne(id: number, options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.itemBlocksControllerFindItemOne(id, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {number} assessmentId 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        itemBlocksControllerFindOne(assessmentId: number, options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.itemBlocksControllerFindOne(assessmentId, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {number} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        itemBlocksControllerRemove(id: number, options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.itemBlocksControllerRemove(id, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {number} submissionId 
         * @param {number} sequence 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        itemBlocksControllerSequenceQuestion(submissionId: number, sequence: number, options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.itemBlocksControllerSequenceQuestion(submissionId, sequence, options).then((request) => request(axios, basePath));
        },
        /**
         * อัปเดตItem Block (Evaluate) ตาม template ที่กำหนด
         * @summary อัปเดตItem Block
         * @param {string} id 
         * @param {number} [sequence] The order/position of this item block in the assessment
         * @param {number} [section] The section number this item block belongs to
         * @param {ItemBlockType} [type] The type of item block (RADIO, CHECKBOX, TEXTFIELD, etc.)
         * @param {boolean} [isRequired] Whether this item block is required to be answered
         * @param {number} [assessmentId] ID of the assessment this item block belongs to
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        itemBlocksControllerUpdate(id: string, sequence?: number, section?: number, type?: ItemBlockType, isRequired?: boolean, assessmentId?: number, options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.itemBlocksControllerUpdate(id, sequence, section, type, isRequired, assessmentId, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {BulkUpdateItemBlockSequencesDto} bulkUpdateItemBlockSequencesDto 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        itemBlocksControllerUpdateSequences(bulkUpdateItemBlockSequencesDto: BulkUpdateItemBlockSequencesDto, options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.itemBlocksControllerUpdateSequences(bulkUpdateItemBlockSequencesDto, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * AssessmentItemBlocksApi - object-oriented interface
 * @export
 * @class AssessmentItemBlocksApi
 * @extends {BaseAPI}
 */
export class AssessmentItemBlocksApi extends BaseAPI {
    /**
     * 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof AssessmentItemBlocksApi
     */
    public itemBlocksControllerCreate(options?: RawAxiosRequestConfig) {
        return AssessmentItemBlocksApiFp(this.configuration).itemBlocksControllerCreate(options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * Creates a complete copy of an existing Item Block with all its content (atomic operation)
     * @summary Duplicate an existing Item Block
     * @param {number} sourceId 
     * @param {number} assessmentId 
     * @param {number} [sequence] 
     * @param {number} [section] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof AssessmentItemBlocksApi
     */
    public itemBlocksControllerDuplicateBlock(sourceId: number, assessmentId: number, sequence?: number, section?: number, options?: RawAxiosRequestConfig) {
        return AssessmentItemBlocksApiFp(this.configuration).itemBlocksControllerDuplicateBlock(sourceId, assessmentId, sequence, section, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {number} id 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof AssessmentItemBlocksApi
     */
    public itemBlocksControllerFindItemOne(id: number, options?: RawAxiosRequestConfig) {
        return AssessmentItemBlocksApiFp(this.configuration).itemBlocksControllerFindItemOne(id, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {number} assessmentId 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof AssessmentItemBlocksApi
     */
    public itemBlocksControllerFindOne(assessmentId: number, options?: RawAxiosRequestConfig) {
        return AssessmentItemBlocksApiFp(this.configuration).itemBlocksControllerFindOne(assessmentId, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {number} id 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof AssessmentItemBlocksApi
     */
    public itemBlocksControllerRemove(id: number, options?: RawAxiosRequestConfig) {
        return AssessmentItemBlocksApiFp(this.configuration).itemBlocksControllerRemove(id, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {number} submissionId 
     * @param {number} sequence 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof AssessmentItemBlocksApi
     */
    public itemBlocksControllerSequenceQuestion(submissionId: number, sequence: number, options?: RawAxiosRequestConfig) {
        return AssessmentItemBlocksApiFp(this.configuration).itemBlocksControllerSequenceQuestion(submissionId, sequence, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * อัปเดตItem Block (Evaluate) ตาม template ที่กำหนด
     * @summary อัปเดตItem Block
     * @param {string} id 
     * @param {number} [sequence] The order/position of this item block in the assessment
     * @param {number} [section] The section number this item block belongs to
     * @param {ItemBlockType} [type] The type of item block (RADIO, CHECKBOX, TEXTFIELD, etc.)
     * @param {boolean} [isRequired] Whether this item block is required to be answered
     * @param {number} [assessmentId] ID of the assessment this item block belongs to
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof AssessmentItemBlocksApi
     */
    public itemBlocksControllerUpdate(id: string, sequence?: number, section?: number, type?: ItemBlockType, isRequired?: boolean, assessmentId?: number, options?: RawAxiosRequestConfig) {
        return AssessmentItemBlocksApiFp(this.configuration).itemBlocksControllerUpdate(id, sequence, section, type, isRequired, assessmentId, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {BulkUpdateItemBlockSequencesDto} bulkUpdateItemBlockSequencesDto 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof AssessmentItemBlocksApi
     */
    public itemBlocksControllerUpdateSequences(bulkUpdateItemBlockSequencesDto: BulkUpdateItemBlockSequencesDto, options?: RawAxiosRequestConfig) {
        return AssessmentItemBlocksApiFp(this.configuration).itemBlocksControllerUpdateSequences(bulkUpdateItemBlockSequencesDto, options).then((request) => request(this.axios, this.basePath));
    }
}

