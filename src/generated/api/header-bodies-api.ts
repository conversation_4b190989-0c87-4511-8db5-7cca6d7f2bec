/* tslint:disable */
/* eslint-disable */
/**
 * HRD Backend API
 * Human Resource Development Backend API Documentation
 *
 * The version of the OpenAPI document: 1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


import type { Configuration } from '../configuration';
import type { AxiosPromise, AxiosInstance, RawAxiosRequestConfig } from 'axios';
import globalAxios from 'axios';
// Some imports not used depending on template conditions
// @ts-ignore
import { DUMMY_BASE_URL, assertParamExists, setApiKeyToObject, setBasicAuthToObject, setBearerAuthToObject, setOAuthToObject, setSearchParams, serializeDataIfNeeded, toPathString, createRequestFunction } from '../common';
// @ts-ignore
import { BASE_PATH, COLLECTION_FORMATS, type RequestArgs, BaseAPI, RequiredError, operationServerMap } from '../base';
/**
 * HeaderBodiesApi - axios parameter creator
 * @export
 */
export const HeaderBodiesApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * สร้าง Header Body ใหม่สำหรับ (Evaluate)
         * @summary สร้าง Header Bodyใหม่
         * @param {number} itemBlockId 
         * @param {string} [title] 
         * @param {string} [description] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        headerBodiesControllerCreate: async (itemBlockId: number, title?: string, description?: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'itemBlockId' is not null or undefined
            assertParamExists('headerBodiesControllerCreate', 'itemBlockId', itemBlockId)
            const localVarPath = `/header-bodies`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;
            const localVarFormParams = new ((configuration && configuration.formDataCtor) || FormData)();

            // authentication bearer required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)


            if (itemBlockId !== undefined) { 
                localVarFormParams.append('itemBlockId', itemBlockId as any);
            }
    
            if (title !== undefined) { 
                localVarFormParams.append('title', title as any);
            }
    
            if (description !== undefined) { 
                localVarFormParams.append('description', description as any);
            }
    
    
            localVarHeaderParameter['Content-Type'] = 'multipart/form-data';
    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = localVarFormParams;

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        headerBodiesControllerFindAll: async (options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/header-bodies`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication bearer required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {string} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        headerBodiesControllerFindOne: async (id: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            assertParamExists('headerBodiesControllerFindOne', 'id', id)
            const localVarPath = `/header-bodies/{id}`
                .replace(`{${"id"}}`, encodeURIComponent(String(id)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication bearer required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {string} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        headerBodiesControllerRemove: async (id: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            assertParamExists('headerBodiesControllerRemove', 'id', id)
            const localVarPath = `/header-bodies/{id}`
                .replace(`{${"id"}}`, encodeURIComponent(String(id)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'DELETE', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication bearer required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * อัปเดตHeader Body (Evaluate) ตาม template ที่กำหนด
         * @summary อัปเดตHeader Body
         * @param {string} id 
         * @param {string} [title] The title of the header section
         * @param {string} [description] Detailed description or instructions for this section
         * @param {number} [itemBlockId] ID of the item block this header belongs to
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        headerBodiesControllerUpdate: async (id: string, title?: string, description?: string, itemBlockId?: number, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            assertParamExists('headerBodiesControllerUpdate', 'id', id)
            const localVarPath = `/header-bodies/{id}`
                .replace(`{${"id"}}`, encodeURIComponent(String(id)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'PATCH', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;
            const localVarFormParams = new ((configuration && configuration.formDataCtor) || FormData)();

            // authentication bearer required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)


            if (title !== undefined) { 
                localVarFormParams.append('title', title as any);
            }
    
            if (description !== undefined) { 
                localVarFormParams.append('description', description as any);
            }
    
            if (itemBlockId !== undefined) { 
                localVarFormParams.append('itemBlockId', itemBlockId as any);
            }
    
    
            localVarHeaderParameter['Content-Type'] = 'multipart/form-data';
    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = localVarFormParams;

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * HeaderBodiesApi - functional programming interface
 * @export
 */
export const HeaderBodiesApiFp = function(configuration?: Configuration) {
    const localVarAxiosParamCreator = HeaderBodiesApiAxiosParamCreator(configuration)
    return {
        /**
         * สร้าง Header Body ใหม่สำหรับ (Evaluate)
         * @summary สร้าง Header Bodyใหม่
         * @param {number} itemBlockId 
         * @param {string} [title] 
         * @param {string} [description] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async headerBodiesControllerCreate(itemBlockId: number, title?: string, description?: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.headerBodiesControllerCreate(itemBlockId, title, description, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['HeaderBodiesApi.headerBodiesControllerCreate']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async headerBodiesControllerFindAll(options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.headerBodiesControllerFindAll(options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['HeaderBodiesApi.headerBodiesControllerFindAll']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {string} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async headerBodiesControllerFindOne(id: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.headerBodiesControllerFindOne(id, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['HeaderBodiesApi.headerBodiesControllerFindOne']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {string} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async headerBodiesControllerRemove(id: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.headerBodiesControllerRemove(id, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['HeaderBodiesApi.headerBodiesControllerRemove']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * อัปเดตHeader Body (Evaluate) ตาม template ที่กำหนด
         * @summary อัปเดตHeader Body
         * @param {string} id 
         * @param {string} [title] The title of the header section
         * @param {string} [description] Detailed description or instructions for this section
         * @param {number} [itemBlockId] ID of the item block this header belongs to
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async headerBodiesControllerUpdate(id: string, title?: string, description?: string, itemBlockId?: number, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.headerBodiesControllerUpdate(id, title, description, itemBlockId, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['HeaderBodiesApi.headerBodiesControllerUpdate']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
    }
};

/**
 * HeaderBodiesApi - factory interface
 * @export
 */
export const HeaderBodiesApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    const localVarFp = HeaderBodiesApiFp(configuration)
    return {
        /**
         * สร้าง Header Body ใหม่สำหรับ (Evaluate)
         * @summary สร้าง Header Bodyใหม่
         * @param {number} itemBlockId 
         * @param {string} [title] 
         * @param {string} [description] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        headerBodiesControllerCreate(itemBlockId: number, title?: string, description?: string, options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.headerBodiesControllerCreate(itemBlockId, title, description, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        headerBodiesControllerFindAll(options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.headerBodiesControllerFindAll(options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {string} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        headerBodiesControllerFindOne(id: string, options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.headerBodiesControllerFindOne(id, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {string} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        headerBodiesControllerRemove(id: string, options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.headerBodiesControllerRemove(id, options).then((request) => request(axios, basePath));
        },
        /**
         * อัปเดตHeader Body (Evaluate) ตาม template ที่กำหนด
         * @summary อัปเดตHeader Body
         * @param {string} id 
         * @param {string} [title] The title of the header section
         * @param {string} [description] Detailed description or instructions for this section
         * @param {number} [itemBlockId] ID of the item block this header belongs to
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        headerBodiesControllerUpdate(id: string, title?: string, description?: string, itemBlockId?: number, options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.headerBodiesControllerUpdate(id, title, description, itemBlockId, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * HeaderBodiesApi - object-oriented interface
 * @export
 * @class HeaderBodiesApi
 * @extends {BaseAPI}
 */
export class HeaderBodiesApi extends BaseAPI {
    /**
     * สร้าง Header Body ใหม่สำหรับ (Evaluate)
     * @summary สร้าง Header Bodyใหม่
     * @param {number} itemBlockId 
     * @param {string} [title] 
     * @param {string} [description] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof HeaderBodiesApi
     */
    public headerBodiesControllerCreate(itemBlockId: number, title?: string, description?: string, options?: RawAxiosRequestConfig) {
        return HeaderBodiesApiFp(this.configuration).headerBodiesControllerCreate(itemBlockId, title, description, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof HeaderBodiesApi
     */
    public headerBodiesControllerFindAll(options?: RawAxiosRequestConfig) {
        return HeaderBodiesApiFp(this.configuration).headerBodiesControllerFindAll(options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {string} id 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof HeaderBodiesApi
     */
    public headerBodiesControllerFindOne(id: string, options?: RawAxiosRequestConfig) {
        return HeaderBodiesApiFp(this.configuration).headerBodiesControllerFindOne(id, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {string} id 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof HeaderBodiesApi
     */
    public headerBodiesControllerRemove(id: string, options?: RawAxiosRequestConfig) {
        return HeaderBodiesApiFp(this.configuration).headerBodiesControllerRemove(id, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * อัปเดตHeader Body (Evaluate) ตาม template ที่กำหนด
     * @summary อัปเดตHeader Body
     * @param {string} id 
     * @param {string} [title] The title of the header section
     * @param {string} [description] Detailed description or instructions for this section
     * @param {number} [itemBlockId] ID of the item block this header belongs to
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof HeaderBodiesApi
     */
    public headerBodiesControllerUpdate(id: string, title?: string, description?: string, itemBlockId?: number, options?: RawAxiosRequestConfig) {
        return HeaderBodiesApiFp(this.configuration).headerBodiesControllerUpdate(id, title, description, itemBlockId, options).then((request) => request(this.axios, this.basePath));
    }
}

