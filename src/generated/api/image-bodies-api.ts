/* tslint:disable */
/* eslint-disable */
/**
 * HRD Backend API
 * Human Resource Development Backend API Documentation
 *
 * The version of the OpenAPI document: 1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


import type { Configuration } from '../configuration';
import type { AxiosPromise, AxiosInstance, RawAxiosRequestConfig } from 'axios';
import globalAxios from 'axios';
// Some imports not used depending on template conditions
// @ts-ignore
import { DUMMY_BASE_URL, assertParamExists, setApiKeyToObject, setBasicAuthToObject, setBearerAuthToObject, setOAuthToObject, setSearchParams, serializeDataIfNeeded, toPathString, createRequestFunction } from '../common';
// @ts-ignore
import { BASE_PATH, COLLECTION_FORMATS, type RequestArgs, BaseAPI, RequiredError, operationServerMap } from '../base';
/**
 * ImageBodiesApi - axios parameter creator
 * @export
 */
export const ImageBodiesApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * สร้าง Image Body ใหม่สำหรับ (Evaluate)
         * @summary สร้าง Image Bodyใหม่
         * @param {number} itemBlockId 
         * @param {string} [imageText] 
         * @param {number} [imageWidth] 
         * @param {number} [imageHeight] 
         * @param {File} [imagePath] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        imageBodiesControllerCreate: async (itemBlockId: number, imageText?: string, imageWidth?: number, imageHeight?: number, imagePath?: File, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'itemBlockId' is not null or undefined
            assertParamExists('imageBodiesControllerCreate', 'itemBlockId', itemBlockId)
            const localVarPath = `/image-bodies`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;
            const localVarFormParams = new ((configuration && configuration.formDataCtor) || FormData)();

            // authentication bearer required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)


            if (itemBlockId !== undefined) { 
                localVarFormParams.append('itemBlockId', itemBlockId as any);
            }
    
            if (imageText !== undefined) { 
                localVarFormParams.append('imageText', imageText as any);
            }
    
            if (imageWidth !== undefined) { 
                localVarFormParams.append('imageWidth', imageWidth as any);
            }
    
            if (imageHeight !== undefined) { 
                localVarFormParams.append('imageHeight', imageHeight as any);
            }
    
            if (imagePath !== undefined) { 
                localVarFormParams.append('imagePath', imagePath as any);
            }
    
    
            localVarHeaderParameter['Content-Type'] = 'multipart/form-data';
    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = localVarFormParams;

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        imageBodiesControllerFindAll: async (options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/image-bodies`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication bearer required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {string} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        imageBodiesControllerFindOne: async (id: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            assertParamExists('imageBodiesControllerFindOne', 'id', id)
            const localVarPath = `/image-bodies/{id}`
                .replace(`{${"id"}}`, encodeURIComponent(String(id)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication bearer required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {number} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        imageBodiesControllerRemove: async (id: number, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            assertParamExists('imageBodiesControllerRemove', 'id', id)
            const localVarPath = `/image-bodies/{id}`
                .replace(`{${"id"}}`, encodeURIComponent(String(id)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'DELETE', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication bearer required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * อัปเดตImage Body (Evaluate) ตาม template ที่กำหนด
         * @summary อัปเดตImage Body
         * @param {string} id 
         * @param {string} [imageText] Caption or text associated with the image
         * @param {string} [imagePath] Path to the image file
         * @param {number} [itemBlockId] ID of the item block this image belongs to
         * @param {number} [imageWidth] Width of the image in pixels
         * @param {number} [imageHeight] Height of the image in pixels
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        imageBodiesControllerUpdate: async (id: string, imageText?: string, imagePath?: string, itemBlockId?: number, imageWidth?: number, imageHeight?: number, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            assertParamExists('imageBodiesControllerUpdate', 'id', id)
            const localVarPath = `/image-bodies/{id}`
                .replace(`{${"id"}}`, encodeURIComponent(String(id)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'PATCH', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;
            const localVarFormParams = new ((configuration && configuration.formDataCtor) || FormData)();

            // authentication bearer required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)


            if (imageText !== undefined) { 
                localVarFormParams.append('imageText', imageText as any);
            }
    
            if (imagePath !== undefined) { 
                localVarFormParams.append('imagePath', imagePath as any);
            }
    
            if (itemBlockId !== undefined) { 
                localVarFormParams.append('itemBlockId', itemBlockId as any);
            }
    
            if (imageWidth !== undefined) { 
                localVarFormParams.append('imageWidth', imageWidth as any);
            }
    
            if (imageHeight !== undefined) { 
                localVarFormParams.append('imageHeight', imageHeight as any);
            }
    
    
            localVarHeaderParameter['Content-Type'] = 'multipart/form-data';
    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = localVarFormParams;

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * ImageBodiesApi - functional programming interface
 * @export
 */
export const ImageBodiesApiFp = function(configuration?: Configuration) {
    const localVarAxiosParamCreator = ImageBodiesApiAxiosParamCreator(configuration)
    return {
        /**
         * สร้าง Image Body ใหม่สำหรับ (Evaluate)
         * @summary สร้าง Image Bodyใหม่
         * @param {number} itemBlockId 
         * @param {string} [imageText] 
         * @param {number} [imageWidth] 
         * @param {number} [imageHeight] 
         * @param {File} [imagePath] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async imageBodiesControllerCreate(itemBlockId: number, imageText?: string, imageWidth?: number, imageHeight?: number, imagePath?: File, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.imageBodiesControllerCreate(itemBlockId, imageText, imageWidth, imageHeight, imagePath, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['ImageBodiesApi.imageBodiesControllerCreate']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async imageBodiesControllerFindAll(options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.imageBodiesControllerFindAll(options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['ImageBodiesApi.imageBodiesControllerFindAll']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {string} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async imageBodiesControllerFindOne(id: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.imageBodiesControllerFindOne(id, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['ImageBodiesApi.imageBodiesControllerFindOne']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {number} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async imageBodiesControllerRemove(id: number, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.imageBodiesControllerRemove(id, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['ImageBodiesApi.imageBodiesControllerRemove']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * อัปเดตImage Body (Evaluate) ตาม template ที่กำหนด
         * @summary อัปเดตImage Body
         * @param {string} id 
         * @param {string} [imageText] Caption or text associated with the image
         * @param {string} [imagePath] Path to the image file
         * @param {number} [itemBlockId] ID of the item block this image belongs to
         * @param {number} [imageWidth] Width of the image in pixels
         * @param {number} [imageHeight] Height of the image in pixels
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async imageBodiesControllerUpdate(id: string, imageText?: string, imagePath?: string, itemBlockId?: number, imageWidth?: number, imageHeight?: number, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.imageBodiesControllerUpdate(id, imageText, imagePath, itemBlockId, imageWidth, imageHeight, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['ImageBodiesApi.imageBodiesControllerUpdate']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
    }
};

/**
 * ImageBodiesApi - factory interface
 * @export
 */
export const ImageBodiesApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    const localVarFp = ImageBodiesApiFp(configuration)
    return {
        /**
         * สร้าง Image Body ใหม่สำหรับ (Evaluate)
         * @summary สร้าง Image Bodyใหม่
         * @param {number} itemBlockId 
         * @param {string} [imageText] 
         * @param {number} [imageWidth] 
         * @param {number} [imageHeight] 
         * @param {File} [imagePath] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        imageBodiesControllerCreate(itemBlockId: number, imageText?: string, imageWidth?: number, imageHeight?: number, imagePath?: File, options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.imageBodiesControllerCreate(itemBlockId, imageText, imageWidth, imageHeight, imagePath, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        imageBodiesControllerFindAll(options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.imageBodiesControllerFindAll(options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {string} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        imageBodiesControllerFindOne(id: string, options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.imageBodiesControllerFindOne(id, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {number} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        imageBodiesControllerRemove(id: number, options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.imageBodiesControllerRemove(id, options).then((request) => request(axios, basePath));
        },
        /**
         * อัปเดตImage Body (Evaluate) ตาม template ที่กำหนด
         * @summary อัปเดตImage Body
         * @param {string} id 
         * @param {string} [imageText] Caption or text associated with the image
         * @param {string} [imagePath] Path to the image file
         * @param {number} [itemBlockId] ID of the item block this image belongs to
         * @param {number} [imageWidth] Width of the image in pixels
         * @param {number} [imageHeight] Height of the image in pixels
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        imageBodiesControllerUpdate(id: string, imageText?: string, imagePath?: string, itemBlockId?: number, imageWidth?: number, imageHeight?: number, options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.imageBodiesControllerUpdate(id, imageText, imagePath, itemBlockId, imageWidth, imageHeight, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * ImageBodiesApi - object-oriented interface
 * @export
 * @class ImageBodiesApi
 * @extends {BaseAPI}
 */
export class ImageBodiesApi extends BaseAPI {
    /**
     * สร้าง Image Body ใหม่สำหรับ (Evaluate)
     * @summary สร้าง Image Bodyใหม่
     * @param {number} itemBlockId 
     * @param {string} [imageText] 
     * @param {number} [imageWidth] 
     * @param {number} [imageHeight] 
     * @param {File} [imagePath] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ImageBodiesApi
     */
    public imageBodiesControllerCreate(itemBlockId: number, imageText?: string, imageWidth?: number, imageHeight?: number, imagePath?: File, options?: RawAxiosRequestConfig) {
        return ImageBodiesApiFp(this.configuration).imageBodiesControllerCreate(itemBlockId, imageText, imageWidth, imageHeight, imagePath, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ImageBodiesApi
     */
    public imageBodiesControllerFindAll(options?: RawAxiosRequestConfig) {
        return ImageBodiesApiFp(this.configuration).imageBodiesControllerFindAll(options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {string} id 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ImageBodiesApi
     */
    public imageBodiesControllerFindOne(id: string, options?: RawAxiosRequestConfig) {
        return ImageBodiesApiFp(this.configuration).imageBodiesControllerFindOne(id, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {number} id 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ImageBodiesApi
     */
    public imageBodiesControllerRemove(id: number, options?: RawAxiosRequestConfig) {
        return ImageBodiesApiFp(this.configuration).imageBodiesControllerRemove(id, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * อัปเดตImage Body (Evaluate) ตาม template ที่กำหนด
     * @summary อัปเดตImage Body
     * @param {string} id 
     * @param {string} [imageText] Caption or text associated with the image
     * @param {string} [imagePath] Path to the image file
     * @param {number} [itemBlockId] ID of the item block this image belongs to
     * @param {number} [imageWidth] Width of the image in pixels
     * @param {number} [imageHeight] Height of the image in pixels
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ImageBodiesApi
     */
    public imageBodiesControllerUpdate(id: string, imageText?: string, imagePath?: string, itemBlockId?: number, imageWidth?: number, imageHeight?: number, options?: RawAxiosRequestConfig) {
        return ImageBodiesApiFp(this.configuration).imageBodiesControllerUpdate(id, imageText, imagePath, itemBlockId, imageWidth, imageHeight, options).then((request) => request(this.axios, this.basePath));
    }
}

