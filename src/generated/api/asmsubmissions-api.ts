/* tslint:disable */
/* eslint-disable */
/**
 * HRD Backend API
 * Human Resource Development Backend API Documentation
 *
 * The version of the OpenAPI document: 1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


import type { Configuration } from '../configuration';
import type { AxiosPromise, AxiosInstance, RawAxiosRequestConfig } from 'axios';
import globalAxios from 'axios';
// Some imports not used depending on template conditions
// @ts-ignore
import { DUMMY_BASE_URL, assertParamExists, setApiKeyToObject, setBasicAuthToObject, setBearerAuthToObject, setOAuthToObject, setSearchParams, serializeDataIfNeeded, toPathString, createRequestFunction } from '../common';
// @ts-ignore
import { BASE_PATH, COLLECTION_FORMATS, type RequestArgs, BaseAPI, RequiredError, operationServerMap } from '../base';
// @ts-ignore
import type { StartQuizDto } from '../models';
/**
 * ASMSubmissionsApi - axios parameter creator
 * @export
 */
export const ASMSubmissionsApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         *      สร้าง submission ใหม่สำหรับการทำแบบทดสอบ     - ตรวจสอบว่า assessment ที่ระบุมีอยู่จริง     - ตรวจสอบ submitLimit ว่าผู้ใช้สามารถทำแบบทดสอบได้     - บันทึกเวลาเริ่มทำแบบทดสอบ     
         * @summary สร้าง submission ใหม่
         * @param {object} body 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        submissionsControllerCreate: async (body: object, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'body' is not null or undefined
            assertParamExists('submissionsControllerCreate', 'body', body)
            const localVarPath = `/submissions`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication bearer required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(body, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary ดึงข้อมูล submissions ทั้งหมด
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        submissionsControllerFindAll: async (options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/submissions`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication bearer required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary ดึงข้อมูล Draft submissions ทั้งหมด
         * @param {number} assessmentId 
         * @param {number} userId 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        submissionsControllerFindDraft: async (assessmentId: number, userId: number, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'assessmentId' is not null or undefined
            assertParamExists('submissionsControllerFindDraft', 'assessmentId', assessmentId)
            // verify required parameter 'userId' is not null or undefined
            assertParamExists('submissionsControllerFindDraft', 'userId', userId)
            const localVarPath = `/submissions/{assessmentId}/{userId}`
                .replace(`{${"assessmentId"}}`, encodeURIComponent(String(assessmentId)))
                .replace(`{${"userId"}}`, encodeURIComponent(String(userId)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication bearer required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary ดึงข้อมูล submission ตาม ID
         * @param {number} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        submissionsControllerFindOne: async (id: number, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            assertParamExists('submissionsControllerFindOne', 'id', id)
            const localVarPath = `/submissions/{id}`
                .replace(`{${"id"}}`, encodeURIComponent(String(id)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication bearer required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {string} linkUrl 
         * @param {number} userId 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        submissionsControllerFindSubmissionById: async (linkUrl: string, userId: number, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'linkUrl' is not null or undefined
            assertParamExists('submissionsControllerFindSubmissionById', 'linkUrl', linkUrl)
            // verify required parameter 'userId' is not null or undefined
            assertParamExists('submissionsControllerFindSubmissionById', 'userId', userId)
            const localVarPath = `/submissions/find-submission/{linkUrl}/{userId}`
                .replace(`{${"linkUrl"}}`, encodeURIComponent(String(linkUrl)))
                .replace(`{${"userId"}}`, encodeURIComponent(String(userId)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication bearer required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary ดูคะแนนแบบทดสอบ
         * @param {number} submissionId 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        submissionsControllerGetQuizScore: async (submissionId: number, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'submissionId' is not null or undefined
            assertParamExists('submissionsControllerGetQuizScore', 'submissionId', submissionId)
            const localVarPath = `/submissions/quiz/score/{submissionId}`
                .replace(`{${"submissionId"}}`, encodeURIComponent(String(submissionId)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication bearer required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary ลบ submission
         * @param {number} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        submissionsControllerRemove: async (id: number, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            assertParamExists('submissionsControllerRemove', 'id', id)
            const localVarPath = `/submissions/{id}`
                .replace(`{${"id"}}`, encodeURIComponent(String(id)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'DELETE', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication bearer required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary เริ่มทำแบบทดสอบ
         * @param {StartQuizDto} startQuizDto 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        submissionsControllerStartAssessment: async (startQuizDto: StartQuizDto, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'startQuizDto' is not null or undefined
            assertParamExists('submissionsControllerStartAssessment', 'startQuizDto', startQuizDto)
            const localVarPath = `/submissions/start-assessment`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication bearer required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(startQuizDto, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary ส่งแบบทดสอบ
         * @param {number} submissionId 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        submissionsControllerSubmitAssessment: async (submissionId: number, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'submissionId' is not null or undefined
            assertParamExists('submissionsControllerSubmitAssessment', 'submissionId', submissionId)
            const localVarPath = `/submissions/submit-assessment/{submissionId}`
                .replace(`{${"submissionId"}}`, encodeURIComponent(String(submissionId)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'PATCH', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication bearer required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary อัปเดตข้อมูล submission
         * @param {number} id 
         * @param {object} body 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        submissionsControllerUpdate: async (id: number, body: object, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            assertParamExists('submissionsControllerUpdate', 'id', id)
            // verify required parameter 'body' is not null or undefined
            assertParamExists('submissionsControllerUpdate', 'body', body)
            const localVarPath = `/submissions/{id}`
                .replace(`{${"id"}}`, encodeURIComponent(String(id)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'PATCH', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication bearer required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(body, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * ASMSubmissionsApi - functional programming interface
 * @export
 */
export const ASMSubmissionsApiFp = function(configuration?: Configuration) {
    const localVarAxiosParamCreator = ASMSubmissionsApiAxiosParamCreator(configuration)
    return {
        /**
         *      สร้าง submission ใหม่สำหรับการทำแบบทดสอบ     - ตรวจสอบว่า assessment ที่ระบุมีอยู่จริง     - ตรวจสอบ submitLimit ว่าผู้ใช้สามารถทำแบบทดสอบได้     - บันทึกเวลาเริ่มทำแบบทดสอบ     
         * @summary สร้าง submission ใหม่
         * @param {object} body 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async submissionsControllerCreate(body: object, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<any>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.submissionsControllerCreate(body, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['ASMSubmissionsApi.submissionsControllerCreate']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @summary ดึงข้อมูล submissions ทั้งหมด
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async submissionsControllerFindAll(options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.submissionsControllerFindAll(options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['ASMSubmissionsApi.submissionsControllerFindAll']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @summary ดึงข้อมูล Draft submissions ทั้งหมด
         * @param {number} assessmentId 
         * @param {number} userId 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async submissionsControllerFindDraft(assessmentId: number, userId: number, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.submissionsControllerFindDraft(assessmentId, userId, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['ASMSubmissionsApi.submissionsControllerFindDraft']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @summary ดึงข้อมูล submission ตาม ID
         * @param {number} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async submissionsControllerFindOne(id: number, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.submissionsControllerFindOne(id, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['ASMSubmissionsApi.submissionsControllerFindOne']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {string} linkUrl 
         * @param {number} userId 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async submissionsControllerFindSubmissionById(linkUrl: string, userId: number, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.submissionsControllerFindSubmissionById(linkUrl, userId, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['ASMSubmissionsApi.submissionsControllerFindSubmissionById']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @summary ดูคะแนนแบบทดสอบ
         * @param {number} submissionId 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async submissionsControllerGetQuizScore(submissionId: number, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.submissionsControllerGetQuizScore(submissionId, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['ASMSubmissionsApi.submissionsControllerGetQuizScore']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @summary ลบ submission
         * @param {number} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async submissionsControllerRemove(id: number, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.submissionsControllerRemove(id, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['ASMSubmissionsApi.submissionsControllerRemove']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @summary เริ่มทำแบบทดสอบ
         * @param {StartQuizDto} startQuizDto 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async submissionsControllerStartAssessment(startQuizDto: StartQuizDto, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.submissionsControllerStartAssessment(startQuizDto, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['ASMSubmissionsApi.submissionsControllerStartAssessment']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @summary ส่งแบบทดสอบ
         * @param {number} submissionId 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async submissionsControllerSubmitAssessment(submissionId: number, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.submissionsControllerSubmitAssessment(submissionId, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['ASMSubmissionsApi.submissionsControllerSubmitAssessment']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @summary อัปเดตข้อมูล submission
         * @param {number} id 
         * @param {object} body 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async submissionsControllerUpdate(id: number, body: object, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.submissionsControllerUpdate(id, body, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['ASMSubmissionsApi.submissionsControllerUpdate']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
    }
};

/**
 * ASMSubmissionsApi - factory interface
 * @export
 */
export const ASMSubmissionsApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    const localVarFp = ASMSubmissionsApiFp(configuration)
    return {
        /**
         *      สร้าง submission ใหม่สำหรับการทำแบบทดสอบ     - ตรวจสอบว่า assessment ที่ระบุมีอยู่จริง     - ตรวจสอบ submitLimit ว่าผู้ใช้สามารถทำแบบทดสอบได้     - บันทึกเวลาเริ่มทำแบบทดสอบ     
         * @summary สร้าง submission ใหม่
         * @param {object} body 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        submissionsControllerCreate(body: object, options?: RawAxiosRequestConfig): AxiosPromise<any> {
            return localVarFp.submissionsControllerCreate(body, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary ดึงข้อมูล submissions ทั้งหมด
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        submissionsControllerFindAll(options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.submissionsControllerFindAll(options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary ดึงข้อมูล Draft submissions ทั้งหมด
         * @param {number} assessmentId 
         * @param {number} userId 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        submissionsControllerFindDraft(assessmentId: number, userId: number, options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.submissionsControllerFindDraft(assessmentId, userId, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary ดึงข้อมูล submission ตาม ID
         * @param {number} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        submissionsControllerFindOne(id: number, options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.submissionsControllerFindOne(id, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {string} linkUrl 
         * @param {number} userId 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        submissionsControllerFindSubmissionById(linkUrl: string, userId: number, options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.submissionsControllerFindSubmissionById(linkUrl, userId, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary ดูคะแนนแบบทดสอบ
         * @param {number} submissionId 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        submissionsControllerGetQuizScore(submissionId: number, options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.submissionsControllerGetQuizScore(submissionId, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary ลบ submission
         * @param {number} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        submissionsControllerRemove(id: number, options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.submissionsControllerRemove(id, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary เริ่มทำแบบทดสอบ
         * @param {StartQuizDto} startQuizDto 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        submissionsControllerStartAssessment(startQuizDto: StartQuizDto, options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.submissionsControllerStartAssessment(startQuizDto, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary ส่งแบบทดสอบ
         * @param {number} submissionId 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        submissionsControllerSubmitAssessment(submissionId: number, options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.submissionsControllerSubmitAssessment(submissionId, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary อัปเดตข้อมูล submission
         * @param {number} id 
         * @param {object} body 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        submissionsControllerUpdate(id: number, body: object, options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.submissionsControllerUpdate(id, body, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * ASMSubmissionsApi - object-oriented interface
 * @export
 * @class ASMSubmissionsApi
 * @extends {BaseAPI}
 */
export class ASMSubmissionsApi extends BaseAPI {
    /**
     *      สร้าง submission ใหม่สำหรับการทำแบบทดสอบ     - ตรวจสอบว่า assessment ที่ระบุมีอยู่จริง     - ตรวจสอบ submitLimit ว่าผู้ใช้สามารถทำแบบทดสอบได้     - บันทึกเวลาเริ่มทำแบบทดสอบ     
     * @summary สร้าง submission ใหม่
     * @param {object} body 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ASMSubmissionsApi
     */
    public submissionsControllerCreate(body: object, options?: RawAxiosRequestConfig) {
        return ASMSubmissionsApiFp(this.configuration).submissionsControllerCreate(body, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @summary ดึงข้อมูล submissions ทั้งหมด
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ASMSubmissionsApi
     */
    public submissionsControllerFindAll(options?: RawAxiosRequestConfig) {
        return ASMSubmissionsApiFp(this.configuration).submissionsControllerFindAll(options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @summary ดึงข้อมูล Draft submissions ทั้งหมด
     * @param {number} assessmentId 
     * @param {number} userId 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ASMSubmissionsApi
     */
    public submissionsControllerFindDraft(assessmentId: number, userId: number, options?: RawAxiosRequestConfig) {
        return ASMSubmissionsApiFp(this.configuration).submissionsControllerFindDraft(assessmentId, userId, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @summary ดึงข้อมูล submission ตาม ID
     * @param {number} id 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ASMSubmissionsApi
     */
    public submissionsControllerFindOne(id: number, options?: RawAxiosRequestConfig) {
        return ASMSubmissionsApiFp(this.configuration).submissionsControllerFindOne(id, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {string} linkUrl 
     * @param {number} userId 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ASMSubmissionsApi
     */
    public submissionsControllerFindSubmissionById(linkUrl: string, userId: number, options?: RawAxiosRequestConfig) {
        return ASMSubmissionsApiFp(this.configuration).submissionsControllerFindSubmissionById(linkUrl, userId, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @summary ดูคะแนนแบบทดสอบ
     * @param {number} submissionId 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ASMSubmissionsApi
     */
    public submissionsControllerGetQuizScore(submissionId: number, options?: RawAxiosRequestConfig) {
        return ASMSubmissionsApiFp(this.configuration).submissionsControllerGetQuizScore(submissionId, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @summary ลบ submission
     * @param {number} id 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ASMSubmissionsApi
     */
    public submissionsControllerRemove(id: number, options?: RawAxiosRequestConfig) {
        return ASMSubmissionsApiFp(this.configuration).submissionsControllerRemove(id, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @summary เริ่มทำแบบทดสอบ
     * @param {StartQuizDto} startQuizDto 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ASMSubmissionsApi
     */
    public submissionsControllerStartAssessment(startQuizDto: StartQuizDto, options?: RawAxiosRequestConfig) {
        return ASMSubmissionsApiFp(this.configuration).submissionsControllerStartAssessment(startQuizDto, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @summary ส่งแบบทดสอบ
     * @param {number} submissionId 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ASMSubmissionsApi
     */
    public submissionsControllerSubmitAssessment(submissionId: number, options?: RawAxiosRequestConfig) {
        return ASMSubmissionsApiFp(this.configuration).submissionsControllerSubmitAssessment(submissionId, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @summary อัปเดตข้อมูล submission
     * @param {number} id 
     * @param {object} body 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ASMSubmissionsApi
     */
    public submissionsControllerUpdate(id: number, body: object, options?: RawAxiosRequestConfig) {
        return ASMSubmissionsApiFp(this.configuration).submissionsControllerUpdate(id, body, options).then((request) => request(this.axios, this.basePath));
    }
}

