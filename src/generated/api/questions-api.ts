/* tslint:disable */
/* eslint-disable */
/**
 * HRD Backend API
 * Human Resource Development Backend API Documentation
 *
 * The version of the OpenAPI document: 1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


import type { Configuration } from '../configuration';
import type { AxiosPromise, AxiosInstance, RawAxiosRequestConfig } from 'axios';
import globalAxios from 'axios';
// Some imports not used depending on template conditions
// @ts-ignore
import { DUMMY_BASE_URL, assertParamExists, setApiKeyToObject, setBasicAuthToObject, setBearerAuthToObject, setOAuthToObject, setSearchParams, serializeDataIfNeeded, toPathString, createRequestFunction } from '../common';
// @ts-ignore
import { BASE_PATH, COLLECTION_FORMATS, type RequestArgs, BaseAPI, RequiredError, operationServerMap } from '../base';
/**
 * QuestionsApi - axios parameter creator
 * @export
 */
export const QuestionsApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * สร้างคำถาม (Evaluate) ใหม่ตาม template ที่กำหนด
         * @summary สร้างคำถามใหม่
         * @param {string} questionText The text content of the question
         * @param {number} sequence The order/position of this question in the assessment
         * @param {File} [imagePath] Image file for the question
         * @param {boolean} [isHeader] Whether this question serves as a header/section title
         * @param {number} [sizeLimit] Maximum file size limit in bytes for file upload questions
         * @param {string} [acceptFile] Accepted file types for file upload questions (comma-separated MIME types or extensions)
         * @param {number} [uploadLimit] Maximum number of files that can be uploaded for this question
         * @param {number} [itemBlockId] ID of the item block this question belongs to
         * @param {number} [score] Score for the question
         * @param {number} [imageWidth] Width of the image in pixels
         * @param {number} [imageHeight] Height of the image in pixels
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        questionsControllerCreate: async (questionText: string, sequence: number, imagePath?: File, isHeader?: boolean, sizeLimit?: number, acceptFile?: string, uploadLimit?: number, itemBlockId?: number, score?: number, imageWidth?: number, imageHeight?: number, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'questionText' is not null or undefined
            assertParamExists('questionsControllerCreate', 'questionText', questionText)
            // verify required parameter 'sequence' is not null or undefined
            assertParamExists('questionsControllerCreate', 'sequence', sequence)
            const localVarPath = `/questions`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;
            const localVarFormParams = new ((configuration && configuration.formDataCtor) || FormData)();

            // authentication bearer required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)


            if (questionText !== undefined) { 
                localVarFormParams.append('questionText', questionText as any);
            }
    
            if (imagePath !== undefined) { 
                localVarFormParams.append('imagePath', imagePath as any);
            }
    
            if (isHeader !== undefined) { 
                localVarFormParams.append('isHeader', String(isHeader) as any);
            }
    
            if (sequence !== undefined) { 
                localVarFormParams.append('sequence', sequence as any);
            }
    
            if (sizeLimit !== undefined) { 
                localVarFormParams.append('sizeLimit', sizeLimit as any);
            }
    
            if (acceptFile !== undefined) { 
                localVarFormParams.append('acceptFile', acceptFile as any);
            }
    
            if (uploadLimit !== undefined) { 
                localVarFormParams.append('uploadLimit', uploadLimit as any);
            }
    
            if (itemBlockId !== undefined) { 
                localVarFormParams.append('itemBlockId', itemBlockId as any);
            }
    
            if (score !== undefined) { 
                localVarFormParams.append('score', score as any);
            }
    
            if (imageWidth !== undefined) { 
                localVarFormParams.append('imageWidth', imageWidth as any);
            }
    
            if (imageHeight !== undefined) { 
                localVarFormParams.append('imageHeight', imageHeight as any);
            }
    
    
            localVarHeaderParameter['Content-Type'] = 'multipart/form-data';
    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = localVarFormParams;

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        questionsControllerFindAll: async (options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/questions`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication bearer required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {string} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        questionsControllerFindOne: async (id: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            assertParamExists('questionsControllerFindOne', 'id', id)
            const localVarPath = `/questions/{id}`
                .replace(`{${"id"}}`, encodeURIComponent(String(id)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication bearer required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {string} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        questionsControllerRemove: async (id: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            assertParamExists('questionsControllerRemove', 'id', id)
            const localVarPath = `/questions/{id}`
                .replace(`{${"id"}}`, encodeURIComponent(String(id)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'DELETE', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication bearer required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * อัปเดตคำถาม (Evaluate) ตาม template ที่กำหนด
         * @summary อัปเดตคำถาม
         * @param {string} id 
         * @param {string} [questionText] The text content of the question
         * @param {File} [imagePath] Image file for the question
         * @param {boolean} [isHeader] Whether this question serves as a header/section title
         * @param {number} [sequence] The order/position of this question in the assessment
         * @param {number} [sizeLimit] Maximum file size limit in bytes for file upload questions
         * @param {string} [acceptFile] Accepted file types for file upload questions (comma-separated MIME types or extensions)
         * @param {number} [uploadLimit] Maximum number of files that can be uploaded for this question
         * @param {number} [itemBlockId] ID of the item block this question belongs to
         * @param {number} [score] Score for the question
         * @param {number} [imageWidth] Width of the image in pixels
         * @param {number} [imageHeight] Height of the image in pixels
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        questionsControllerUpdate: async (id: string, questionText?: string, imagePath?: File, isHeader?: boolean, sequence?: number, sizeLimit?: number, acceptFile?: string, uploadLimit?: number, itemBlockId?: number, score?: number, imageWidth?: number, imageHeight?: number, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            assertParamExists('questionsControllerUpdate', 'id', id)
            const localVarPath = `/questions/{id}`
                .replace(`{${"id"}}`, encodeURIComponent(String(id)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'PATCH', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;
            const localVarFormParams = new ((configuration && configuration.formDataCtor) || FormData)();

            // authentication bearer required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)


            if (questionText !== undefined) { 
                localVarFormParams.append('questionText', questionText as any);
            }
    
            if (imagePath !== undefined) { 
                localVarFormParams.append('imagePath', imagePath as any);
            }
    
            if (isHeader !== undefined) { 
                localVarFormParams.append('isHeader', String(isHeader) as any);
            }
    
            if (sequence !== undefined) { 
                localVarFormParams.append('sequence', sequence as any);
            }
    
            if (sizeLimit !== undefined) { 
                localVarFormParams.append('sizeLimit', sizeLimit as any);
            }
    
            if (acceptFile !== undefined) { 
                localVarFormParams.append('acceptFile', acceptFile as any);
            }
    
            if (uploadLimit !== undefined) { 
                localVarFormParams.append('uploadLimit', uploadLimit as any);
            }
    
            if (itemBlockId !== undefined) { 
                localVarFormParams.append('itemBlockId', itemBlockId as any);
            }
    
            if (score !== undefined) { 
                localVarFormParams.append('score', score as any);
            }
    
            if (imageWidth !== undefined) { 
                localVarFormParams.append('imageWidth', imageWidth as any);
            }
    
            if (imageHeight !== undefined) { 
                localVarFormParams.append('imageHeight', imageHeight as any);
            }
    
    
            localVarHeaderParameter['Content-Type'] = 'multipart/form-data';
    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = localVarFormParams;

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * QuestionsApi - functional programming interface
 * @export
 */
export const QuestionsApiFp = function(configuration?: Configuration) {
    const localVarAxiosParamCreator = QuestionsApiAxiosParamCreator(configuration)
    return {
        /**
         * สร้างคำถาม (Evaluate) ใหม่ตาม template ที่กำหนด
         * @summary สร้างคำถามใหม่
         * @param {string} questionText The text content of the question
         * @param {number} sequence The order/position of this question in the assessment
         * @param {File} [imagePath] Image file for the question
         * @param {boolean} [isHeader] Whether this question serves as a header/section title
         * @param {number} [sizeLimit] Maximum file size limit in bytes for file upload questions
         * @param {string} [acceptFile] Accepted file types for file upload questions (comma-separated MIME types or extensions)
         * @param {number} [uploadLimit] Maximum number of files that can be uploaded for this question
         * @param {number} [itemBlockId] ID of the item block this question belongs to
         * @param {number} [score] Score for the question
         * @param {number} [imageWidth] Width of the image in pixels
         * @param {number} [imageHeight] Height of the image in pixels
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async questionsControllerCreate(questionText: string, sequence: number, imagePath?: File, isHeader?: boolean, sizeLimit?: number, acceptFile?: string, uploadLimit?: number, itemBlockId?: number, score?: number, imageWidth?: number, imageHeight?: number, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.questionsControllerCreate(questionText, sequence, imagePath, isHeader, sizeLimit, acceptFile, uploadLimit, itemBlockId, score, imageWidth, imageHeight, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['QuestionsApi.questionsControllerCreate']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async questionsControllerFindAll(options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.questionsControllerFindAll(options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['QuestionsApi.questionsControllerFindAll']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {string} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async questionsControllerFindOne(id: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.questionsControllerFindOne(id, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['QuestionsApi.questionsControllerFindOne']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {string} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async questionsControllerRemove(id: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.questionsControllerRemove(id, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['QuestionsApi.questionsControllerRemove']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * อัปเดตคำถาม (Evaluate) ตาม template ที่กำหนด
         * @summary อัปเดตคำถาม
         * @param {string} id 
         * @param {string} [questionText] The text content of the question
         * @param {File} [imagePath] Image file for the question
         * @param {boolean} [isHeader] Whether this question serves as a header/section title
         * @param {number} [sequence] The order/position of this question in the assessment
         * @param {number} [sizeLimit] Maximum file size limit in bytes for file upload questions
         * @param {string} [acceptFile] Accepted file types for file upload questions (comma-separated MIME types or extensions)
         * @param {number} [uploadLimit] Maximum number of files that can be uploaded for this question
         * @param {number} [itemBlockId] ID of the item block this question belongs to
         * @param {number} [score] Score for the question
         * @param {number} [imageWidth] Width of the image in pixels
         * @param {number} [imageHeight] Height of the image in pixels
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async questionsControllerUpdate(id: string, questionText?: string, imagePath?: File, isHeader?: boolean, sequence?: number, sizeLimit?: number, acceptFile?: string, uploadLimit?: number, itemBlockId?: number, score?: number, imageWidth?: number, imageHeight?: number, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.questionsControllerUpdate(id, questionText, imagePath, isHeader, sequence, sizeLimit, acceptFile, uploadLimit, itemBlockId, score, imageWidth, imageHeight, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['QuestionsApi.questionsControllerUpdate']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
    }
};

/**
 * QuestionsApi - factory interface
 * @export
 */
export const QuestionsApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    const localVarFp = QuestionsApiFp(configuration)
    return {
        /**
         * สร้างคำถาม (Evaluate) ใหม่ตาม template ที่กำหนด
         * @summary สร้างคำถามใหม่
         * @param {string} questionText The text content of the question
         * @param {number} sequence The order/position of this question in the assessment
         * @param {File} [imagePath] Image file for the question
         * @param {boolean} [isHeader] Whether this question serves as a header/section title
         * @param {number} [sizeLimit] Maximum file size limit in bytes for file upload questions
         * @param {string} [acceptFile] Accepted file types for file upload questions (comma-separated MIME types or extensions)
         * @param {number} [uploadLimit] Maximum number of files that can be uploaded for this question
         * @param {number} [itemBlockId] ID of the item block this question belongs to
         * @param {number} [score] Score for the question
         * @param {number} [imageWidth] Width of the image in pixels
         * @param {number} [imageHeight] Height of the image in pixels
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        questionsControllerCreate(questionText: string, sequence: number, imagePath?: File, isHeader?: boolean, sizeLimit?: number, acceptFile?: string, uploadLimit?: number, itemBlockId?: number, score?: number, imageWidth?: number, imageHeight?: number, options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.questionsControllerCreate(questionText, sequence, imagePath, isHeader, sizeLimit, acceptFile, uploadLimit, itemBlockId, score, imageWidth, imageHeight, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        questionsControllerFindAll(options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.questionsControllerFindAll(options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {string} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        questionsControllerFindOne(id: string, options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.questionsControllerFindOne(id, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {string} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        questionsControllerRemove(id: string, options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.questionsControllerRemove(id, options).then((request) => request(axios, basePath));
        },
        /**
         * อัปเดตคำถาม (Evaluate) ตาม template ที่กำหนด
         * @summary อัปเดตคำถาม
         * @param {string} id 
         * @param {string} [questionText] The text content of the question
         * @param {File} [imagePath] Image file for the question
         * @param {boolean} [isHeader] Whether this question serves as a header/section title
         * @param {number} [sequence] The order/position of this question in the assessment
         * @param {number} [sizeLimit] Maximum file size limit in bytes for file upload questions
         * @param {string} [acceptFile] Accepted file types for file upload questions (comma-separated MIME types or extensions)
         * @param {number} [uploadLimit] Maximum number of files that can be uploaded for this question
         * @param {number} [itemBlockId] ID of the item block this question belongs to
         * @param {number} [score] Score for the question
         * @param {number} [imageWidth] Width of the image in pixels
         * @param {number} [imageHeight] Height of the image in pixels
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        questionsControllerUpdate(id: string, questionText?: string, imagePath?: File, isHeader?: boolean, sequence?: number, sizeLimit?: number, acceptFile?: string, uploadLimit?: number, itemBlockId?: number, score?: number, imageWidth?: number, imageHeight?: number, options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.questionsControllerUpdate(id, questionText, imagePath, isHeader, sequence, sizeLimit, acceptFile, uploadLimit, itemBlockId, score, imageWidth, imageHeight, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * QuestionsApi - object-oriented interface
 * @export
 * @class QuestionsApi
 * @extends {BaseAPI}
 */
export class QuestionsApi extends BaseAPI {
    /**
     * สร้างคำถาม (Evaluate) ใหม่ตาม template ที่กำหนด
     * @summary สร้างคำถามใหม่
     * @param {string} questionText The text content of the question
     * @param {number} sequence The order/position of this question in the assessment
     * @param {File} [imagePath] Image file for the question
     * @param {boolean} [isHeader] Whether this question serves as a header/section title
     * @param {number} [sizeLimit] Maximum file size limit in bytes for file upload questions
     * @param {string} [acceptFile] Accepted file types for file upload questions (comma-separated MIME types or extensions)
     * @param {number} [uploadLimit] Maximum number of files that can be uploaded for this question
     * @param {number} [itemBlockId] ID of the item block this question belongs to
     * @param {number} [score] Score for the question
     * @param {number} [imageWidth] Width of the image in pixels
     * @param {number} [imageHeight] Height of the image in pixels
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof QuestionsApi
     */
    public questionsControllerCreate(questionText: string, sequence: number, imagePath?: File, isHeader?: boolean, sizeLimit?: number, acceptFile?: string, uploadLimit?: number, itemBlockId?: number, score?: number, imageWidth?: number, imageHeight?: number, options?: RawAxiosRequestConfig) {
        return QuestionsApiFp(this.configuration).questionsControllerCreate(questionText, sequence, imagePath, isHeader, sizeLimit, acceptFile, uploadLimit, itemBlockId, score, imageWidth, imageHeight, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof QuestionsApi
     */
    public questionsControllerFindAll(options?: RawAxiosRequestConfig) {
        return QuestionsApiFp(this.configuration).questionsControllerFindAll(options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {string} id 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof QuestionsApi
     */
    public questionsControllerFindOne(id: string, options?: RawAxiosRequestConfig) {
        return QuestionsApiFp(this.configuration).questionsControllerFindOne(id, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {string} id 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof QuestionsApi
     */
    public questionsControllerRemove(id: string, options?: RawAxiosRequestConfig) {
        return QuestionsApiFp(this.configuration).questionsControllerRemove(id, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * อัปเดตคำถาม (Evaluate) ตาม template ที่กำหนด
     * @summary อัปเดตคำถาม
     * @param {string} id 
     * @param {string} [questionText] The text content of the question
     * @param {File} [imagePath] Image file for the question
     * @param {boolean} [isHeader] Whether this question serves as a header/section title
     * @param {number} [sequence] The order/position of this question in the assessment
     * @param {number} [sizeLimit] Maximum file size limit in bytes for file upload questions
     * @param {string} [acceptFile] Accepted file types for file upload questions (comma-separated MIME types or extensions)
     * @param {number} [uploadLimit] Maximum number of files that can be uploaded for this question
     * @param {number} [itemBlockId] ID of the item block this question belongs to
     * @param {number} [score] Score for the question
     * @param {number} [imageWidth] Width of the image in pixels
     * @param {number} [imageHeight] Height of the image in pixels
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof QuestionsApi
     */
    public questionsControllerUpdate(id: string, questionText?: string, imagePath?: File, isHeader?: boolean, sequence?: number, sizeLimit?: number, acceptFile?: string, uploadLimit?: number, itemBlockId?: number, score?: number, imageWidth?: number, imageHeight?: number, options?: RawAxiosRequestConfig) {
        return QuestionsApiFp(this.configuration).questionsControllerUpdate(id, questionText, imagePath, isHeader, sequence, sizeLimit, acceptFile, uploadLimit, itemBlockId, score, imageWidth, imageHeight, options).then((request) => request(this.axios, this.basePath));
    }
}

