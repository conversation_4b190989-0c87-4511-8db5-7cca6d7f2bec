/* tslint:disable */
/* eslint-disable */
/**
 * HRD Backend API
 * Human Resource Development Backend API Documentation
 *
 * The version of the OpenAPI document: 1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


import type { Configuration } from '../configuration';
import type { AxiosPromise, AxiosInstance, RawAxiosRequestConfig } from 'axios';
import globalAxios from 'axios';
// Some imports not used depending on template conditions
// @ts-ignore
import { DUMMY_BASE_URL, assertParamExists, setApiKeyToObject, setBasicAuthToObject, setBearerAuthToObject, setOAuthToObject, setSearchParams, serializeDataIfNeeded, toPathString, createRequestFunction } from '../common';
// @ts-ignore
import { BASE_PATH, COLLECTION_FORMATS, type RequestArgs, BaseAPI, RequiredError, operationServerMap } from '../base';
// @ts-ignore
import type { AssessmentType } from '../models';
// @ts-ignore
import type { AssessmentsControllerSaveTextFieldScore200Response } from '../models';
/**
 * AssessmentApi - axios parameter creator
 * @export
 */
export const AssessmentApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * 
         * @summary Copy content from one assessment to another
         * @param {number} sourceId Source assessment ID
         * @param {number} targetId Target assessment ID
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        assessmentsControllerCopyAssessmentContent: async (sourceId: number, targetId: number, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'sourceId' is not null or undefined
            assertParamExists('assessmentsControllerCopyAssessmentContent', 'sourceId', sourceId)
            // verify required parameter 'targetId' is not null or undefined
            assertParamExists('assessmentsControllerCopyAssessmentContent', 'targetId', targetId)
            const localVarPath = `/assessments/{sourceId}/copy-to/{targetId}`
                .replace(`{${"sourceId"}}`, encodeURIComponent(String(sourceId)))
                .replace(`{${"targetId"}}`, encodeURIComponent(String(targetId)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'PATCH', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication bearer required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary New Assessment (Quiz/Evaluate)
         * @param {number} creatorUserId รหัสผู้สร้างแบบประเมิน
         * @param {AssessmentType} type ประเภทของแบบประเมิน (QUIZ หรือ FORM)
         * @param {number} programId รหัสโปรแกรม
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        assessmentsControllerCreate: async (creatorUserId: number, type: AssessmentType, programId: number, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'creatorUserId' is not null or undefined
            assertParamExists('assessmentsControllerCreate', 'creatorUserId', creatorUserId)
            // verify required parameter 'type' is not null or undefined
            assertParamExists('assessmentsControllerCreate', 'type', type)
            // verify required parameter 'programId' is not null or undefined
            assertParamExists('assessmentsControllerCreate', 'programId', programId)
            const localVarPath = `/assessments`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;
            const localVarFormParams = new ((configuration && configuration.formDataCtor) || FormData)();

            // authentication bearer required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)


            if (creatorUserId !== undefined) { 
                localVarFormParams.append('creatorUserId', creatorUserId as any);
            }
    
            if (type !== undefined) { 
                localVarFormParams.append('type', type as any);
            }
    
            if (programId !== undefined) { 
                localVarFormParams.append('programId', programId as any);
            }
    
    
            localVarHeaderParameter['Content-Type'] = 'multipart/form-data';
    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = localVarFormParams;

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * Creates a complete assessment with customizable questions and submissions for dashboard testing
         * @summary Create Complete Mockup Data (Assessment + Submissions)
         * @param {number} [totalQuestions] Total number of questions (default: 100)
         * @param {number} [submissionCount] Number of submissions (default: 50)
         * @param {number} [totalScore] Total score (auto-calculated if not provided)
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        assessmentsControllerCreateCompleteAssessmentMockup: async (totalQuestions?: number, submissionCount?: number, totalScore?: number, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/assessments/mockup/complete`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication bearer required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)

            if (totalQuestions !== undefined) {
                localVarQueryParameter['totalQuestions'] = totalQuestions;
            }

            if (submissionCount !== undefined) {
                localVarQueryParameter['submissionCount'] = submissionCount;
            }

            if (totalScore !== undefined) {
                localVarQueryParameter['totalScore'] = totalScore;
            }


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {number} [totalQuestions] Total number of questions (default: 100)
         * @param {number} [totalScore] Total score (auto-calculated if not provided)
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        assessmentsControllerCreateMockupAssessment: async (totalQuestions?: number, totalScore?: number, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/assessments/mockup`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication bearer required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)

            if (totalQuestions !== undefined) {
                localVarQueryParameter['totalQuestions'] = totalQuestions;
            }

            if (totalScore !== undefined) {
                localVarQueryParameter['totalScore'] = totalScore;
            }


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * Creates 50 mockup submissions with realistic response patterns for the specified assessment
         * @summary Create Mockup Submissions for Assessment
         * @param {number} id Assessment ID to create submissions for
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        assessmentsControllerCreateMockupSubmissions: async (id: number, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            assertParamExists('assessmentsControllerCreateMockupSubmissions', 'id', id)
            const localVarPath = `/assessments/mockup/submissions/{id}`
                .replace(`{${"id"}}`, encodeURIComponent(String(id)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication bearer required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * Creates a quick assessment with fewer questions for testing
         * @summary Create Quick Mockup Assessment
         * @param {number} [totalQuestions] Total number of questions (default: 20)
         * @param {number} [submissionCount] Number of submissions (default: 20)
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        assessmentsControllerCreateQuickMockup: async (totalQuestions?: number, submissionCount?: number, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/assessments/mockup/quick`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication bearer required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)

            if (totalQuestions !== undefined) {
                localVarQueryParameter['totalQuestions'] = totalQuestions;
            }

            if (submissionCount !== undefined) {
                localVarQueryParameter['submissionCount'] = submissionCount;
            }


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary Export assessment questions to Excel file
         * @param {number} id Assessment ID to export
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        assessmentsControllerExportAssessmentToExcel: async (id: number, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            assertParamExists('assessmentsControllerExportAssessmentToExcel', 'id', id)
            const localVarPath = `/assessments/{id}/export/excel`
                .replace(`{${"id"}}`, encodeURIComponent(String(id)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication bearer required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary Get a single assessment by ID with paginated itemBlocks
         * @param {number} id The ID of the assessment
         * @param {number} [page] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        assessmentsControllerFindOne: async (id: number, page?: number, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            assertParamExists('assessmentsControllerFindOne', 'id', id)
            const localVarPath = `/assessments/{id}`
                .replace(`{${"id"}}`, encodeURIComponent(String(id)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication bearer required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)

            if (page !== undefined) {
                localVarQueryParameter['page'] = page;
            }


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {AssessmentsControllerGetAllTypeEnum} type ประเภทของแบบประเมิน
         * @param {any} limit Number of items per page
         * @param {any} page Page number to retrieve
         * @param {string} [search] ค้นหาด้วยชื่อแบบประเมิน
         * @param {any} [sortBy] Field to sort by (e.g., id, name, email)
         * @param {any} [order] Sort order (ASC or DESC)
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        assessmentsControllerGetAll: async (type: AssessmentsControllerGetAllTypeEnum, limit: any, page: any, search?: string, sortBy?: any, order?: any, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'type' is not null or undefined
            assertParamExists('assessmentsControllerGetAll', 'type', type)
            // verify required parameter 'limit' is not null or undefined
            assertParamExists('assessmentsControllerGetAll', 'limit', limit)
            // verify required parameter 'page' is not null or undefined
            assertParamExists('assessmentsControllerGetAll', 'page', page)
            const localVarPath = `/assessments`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication bearer required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)

            if (type !== undefined) {
                localVarQueryParameter['type'] = type;
            }

            if (search !== undefined) {
                localVarQueryParameter['search'] = search;
            }

            if (sortBy !== undefined) {
                for (const [key, value] of Object.entries(sortBy)) {
                    localVarQueryParameter[key] = value;
                }
            }

            if (order !== undefined) {
                for (const [key, value] of Object.entries(order)) {
                    localVarQueryParameter[key] = value;
                }
            }

            if (limit !== undefined) {
                for (const [key, value] of Object.entries(limit)) {
                    localVarQueryParameter[key] = value;
                }
            }

            if (page !== undefined) {
                for (const [key, value] of Object.entries(page)) {
                    localVarQueryParameter[key] = value;
                }
            }


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {any} limit Number of items per page
         * @param {any} page Page number to retrieve
         * @param {AssessmentsControllerGetAllEditorTypeEnum} [type] 
         * @param {any} [sortBy] Field to sort by (e.g., id, name, email)
         * @param {any} [order] Sort order (ASC or DESC)
         * @param {any} [search] Search term to filter results
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        assessmentsControllerGetAllEditor: async (limit: any, page: any, type?: AssessmentsControllerGetAllEditorTypeEnum, sortBy?: any, order?: any, search?: any, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'limit' is not null or undefined
            assertParamExists('assessmentsControllerGetAllEditor', 'limit', limit)
            // verify required parameter 'page' is not null or undefined
            assertParamExists('assessmentsControllerGetAllEditor', 'page', page)
            const localVarPath = `/assessments/editor/view-assessment`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication bearer required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)

            if (type !== undefined) {
                localVarQueryParameter['type'] = type;
            }

            if (sortBy !== undefined) {
                for (const [key, value] of Object.entries(sortBy)) {
                    localVarQueryParameter[key] = value;
                }
            }

            if (order !== undefined) {
                for (const [key, value] of Object.entries(order)) {
                    localVarQueryParameter[key] = value;
                }
            }

            if (limit !== undefined) {
                for (const [key, value] of Object.entries(limit)) {
                    localVarQueryParameter[key] = value;
                }
            }

            if (page !== undefined) {
                for (const [key, value] of Object.entries(page)) {
                    localVarQueryParameter[key] = value;
                }
            }

            if (search !== undefined) {
                for (const [key, value] of Object.entries(search)) {
                    localVarQueryParameter[key] = value;
                }
            }


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary Get all assessments that are prototypes (isPrototype=true)
         * @param {AssessmentsControllerGetAllPrototypesTypeEnum} type 
         * @param {any} limit Number of items per page
         * @param {any} page Page number to retrieve
         * @param {any} [sortBy] Field to sort by (e.g., id, name, email)
         * @param {any} [order] Sort order (ASC or DESC)
         * @param {any} [search] Search term to filter results
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        assessmentsControllerGetAllPrototypes: async (type: AssessmentsControllerGetAllPrototypesTypeEnum, limit: any, page: any, sortBy?: any, order?: any, search?: any, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'type' is not null or undefined
            assertParamExists('assessmentsControllerGetAllPrototypes', 'type', type)
            // verify required parameter 'limit' is not null or undefined
            assertParamExists('assessmentsControllerGetAllPrototypes', 'limit', limit)
            // verify required parameter 'page' is not null or undefined
            assertParamExists('assessmentsControllerGetAllPrototypes', 'page', page)
            const localVarPath = `/assessments/prototypes`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication bearer required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)

            if (type !== undefined) {
                localVarQueryParameter['type'] = type;
            }

            if (sortBy !== undefined) {
                for (const [key, value] of Object.entries(sortBy)) {
                    localVarQueryParameter[key] = value;
                }
            }

            if (order !== undefined) {
                for (const [key, value] of Object.entries(order)) {
                    localVarQueryParameter[key] = value;
                }
            }

            if (limit !== undefined) {
                for (const [key, value] of Object.entries(limit)) {
                    localVarQueryParameter[key] = value;
                }
            }

            if (page !== undefined) {
                for (const [key, value] of Object.entries(page)) {
                    localVarQueryParameter[key] = value;
                }
            }

            if (search !== undefined) {
                for (const [key, value] of Object.entries(search)) {
                    localVarQueryParameter[key] = value;
                }
            }


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {any} limit Number of items per page
         * @param {any} page Page number to retrieve
         * @param {AssessmentsControllerGetAllUserTypeEnum} [type] 
         * @param {any} [sortBy] Field to sort by (e.g., id, name, email)
         * @param {any} [order] Sort order (ASC or DESC)
         * @param {any} [search] Search term to filter results
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        assessmentsControllerGetAllUser: async (limit: any, page: any, type?: AssessmentsControllerGetAllUserTypeEnum, sortBy?: any, order?: any, search?: any, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'limit' is not null or undefined
            assertParamExists('assessmentsControllerGetAllUser', 'limit', limit)
            // verify required parameter 'page' is not null or undefined
            assertParamExists('assessmentsControllerGetAllUser', 'page', page)
            const localVarPath = `/assessments/standardUser/view-assessment`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication bearer required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)

            if (type !== undefined) {
                localVarQueryParameter['type'] = type;
            }

            if (sortBy !== undefined) {
                for (const [key, value] of Object.entries(sortBy)) {
                    localVarQueryParameter[key] = value;
                }
            }

            if (order !== undefined) {
                for (const [key, value] of Object.entries(order)) {
                    localVarQueryParameter[key] = value;
                }
            }

            if (limit !== undefined) {
                for (const [key, value] of Object.entries(limit)) {
                    localVarQueryParameter[key] = value;
                }
            }

            if (page !== undefined) {
                for (const [key, value] of Object.entries(page)) {
                    localVarQueryParameter[key] = value;
                }
            }

            if (search !== undefined) {
                for (const [key, value] of Object.entries(search)) {
                    localVarQueryParameter[key] = value;
                }
            }


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary Get assessment by URL
         * @param {string} url 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        assessmentsControllerGetByURL: async (url: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'url' is not null or undefined
            assertParamExists('assessmentsControllerGetByURL', 'url', url)
            const localVarPath = `/assessments/url/{url}`
                .replace(`{${"url"}}`, encodeURIComponent(String(url)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication bearer required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary ดึงข่อมูลสำหรับโชว์ Graph
         * @param {number} assessmentId 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        assessmentsControllerGetChartData: async (assessmentId: number, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'assessmentId' is not null or undefined
            assertParamExists('assessmentsControllerGetChartData', 'assessmentId', assessmentId)
            const localVarPath = `/assessments/dashboard/evaluate/{assessmentId}`
                .replace(`{${"assessmentId"}}`, encodeURIComponent(String(assessmentId)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication bearer required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary ดึงข่อมูลจำนวนของResponses ทั้งหมด
         * @param {number} assessmentId 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        assessmentsControllerGetNumberOfResponses: async (assessmentId: number, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'assessmentId' is not null or undefined
            assertParamExists('assessmentsControllerGetNumberOfResponses', 'assessmentId', assessmentId)
            const localVarPath = `/assessments/header/{assessmentId}`
                .replace(`{${"assessmentId"}}`, encodeURIComponent(String(assessmentId)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication bearer required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary Get assessment by section
         * @param {number} id 
         * @param {number} section 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        assessmentsControllerGetOne: async (id: number, section: number, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            assertParamExists('assessmentsControllerGetOne', 'id', id)
            // verify required parameter 'section' is not null or undefined
            assertParamExists('assessmentsControllerGetOne', 'section', section)
            const localVarPath = `/assessments/preview/{id}/{section}`
                .replace(`{${"id"}}`, encodeURIComponent(String(id)))
                .replace(`{${"section"}}`, encodeURIComponent(String(section)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication bearer required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary ดึงข้อมูลแดชบอร์ดของแบบทดสอบ
         * @param {number} id รหัสของแบบทดสอบ
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        assessmentsControllerGetQuizDashboardMeta: async (id: number, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            assertParamExists('assessmentsControllerGetQuizDashboardMeta', 'id', id)
            const localVarPath = `/assessments/dashboard/quiz/{id}`
                .replace(`{${"id"}}`, encodeURIComponent(String(id)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication bearer required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary Get quiz header with user submission history
         * @param {string} linkUrl 
         * @param {number} userId 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        assessmentsControllerGetQuizHeaderWithUserSubmissions: async (linkUrl: string, userId: number, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'linkUrl' is not null or undefined
            assertParamExists('assessmentsControllerGetQuizHeaderWithUserSubmissions', 'linkUrl', linkUrl)
            // verify required parameter 'userId' is not null or undefined
            assertParamExists('assessmentsControllerGetQuizHeaderWithUserSubmissions', 'userId', userId)
            const localVarPath = `/assessments/header-with-submissions/url/{linkUrl}`
                .replace(`{${"linkUrl"}}`, encodeURIComponent(String(linkUrl)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication bearer required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)

            if (userId !== undefined) {
                localVarQueryParameter['userId'] = userId;
            }


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary ดึงข้อมูลรายละเอียดของผู้เข้าร่วมทำแบบทดสอบเฉพาะราย
         * @param {number} id รหัสของผู้เข้าร่วม
         * @param {any} limit Number of items per page
         * @param {any} page Page number to retrieve
         * @param {any} [sortBy] Field to sort by (e.g., id, name, email)
         * @param {any} [order] Sort order (ASC or DESC)
         * @param {any} [search] Search term to filter results
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        assessmentsControllerGetQuizParticipantDetails: async (id: number, limit: any, page: any, sortBy?: any, order?: any, search?: any, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            assertParamExists('assessmentsControllerGetQuizParticipantDetails', 'id', id)
            // verify required parameter 'limit' is not null or undefined
            assertParamExists('assessmentsControllerGetQuizParticipantDetails', 'limit', limit)
            // verify required parameter 'page' is not null or undefined
            assertParamExists('assessmentsControllerGetQuizParticipantDetails', 'page', page)
            const localVarPath = `/assessments/dashboard/quiz/participant/{id}`
                .replace(`{${"id"}}`, encodeURIComponent(String(id)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication bearer required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)

            if (sortBy !== undefined) {
                for (const [key, value] of Object.entries(sortBy)) {
                    localVarQueryParameter[key] = value;
                }
            }

            if (order !== undefined) {
                for (const [key, value] of Object.entries(order)) {
                    localVarQueryParameter[key] = value;
                }
            }

            if (limit !== undefined) {
                for (const [key, value] of Object.entries(limit)) {
                    localVarQueryParameter[key] = value;
                }
            }

            if (page !== undefined) {
                for (const [key, value] of Object.entries(page)) {
                    localVarQueryParameter[key] = value;
                }
            }

            if (search !== undefined) {
                for (const [key, value] of Object.entries(search)) {
                    localVarQueryParameter[key] = value;
                }
            }


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary ดึงข้อมูลรายละเอียดของผู้เข้าร่วมทำแบบทดสอบสำหรับการให้คะแนน TEXTFIELD
         * @param {number} id รหัสของผู้เข้าร่วม
         * @param {any} limit Number of items per page
         * @param {any} page Page number to retrieve
         * @param {any} [sortBy] Field to sort by (e.g., id, name, email)
         * @param {any} [order] Sort order (ASC or DESC)
         * @param {any} [search] Search term to filter results
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        assessmentsControllerGetQuizParticipantTextFieldGrading: async (id: number, limit: any, page: any, sortBy?: any, order?: any, search?: any, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            assertParamExists('assessmentsControllerGetQuizParticipantTextFieldGrading', 'id', id)
            // verify required parameter 'limit' is not null or undefined
            assertParamExists('assessmentsControllerGetQuizParticipantTextFieldGrading', 'limit', limit)
            // verify required parameter 'page' is not null or undefined
            assertParamExists('assessmentsControllerGetQuizParticipantTextFieldGrading', 'page', page)
            const localVarPath = `/assessments/dashboard/quiz/participant/{id}/textfield-grading`
                .replace(`{${"id"}}`, encodeURIComponent(String(id)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication bearer required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)

            if (sortBy !== undefined) {
                for (const [key, value] of Object.entries(sortBy)) {
                    localVarQueryParameter[key] = value;
                }
            }

            if (order !== undefined) {
                for (const [key, value] of Object.entries(order)) {
                    localVarQueryParameter[key] = value;
                }
            }

            if (limit !== undefined) {
                for (const [key, value] of Object.entries(limit)) {
                    localVarQueryParameter[key] = value;
                }
            }

            if (page !== undefined) {
                for (const [key, value] of Object.entries(page)) {
                    localVarQueryParameter[key] = value;
                }
            }

            if (search !== undefined) {
                for (const [key, value] of Object.entries(search)) {
                    localVarQueryParameter[key] = value;
                }
            }


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary ดึงข้อมูลผู้เข้าร่วมทำแบบทดสอบทั้งหมด
         * @param {number} id รหัสของแบบทดสอบ
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        assessmentsControllerGetQuizParticipants: async (id: number, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            assertParamExists('assessmentsControllerGetQuizParticipants', 'id', id)
            const localVarPath = `/assessments/dashboard/quiz/{id}/participants`
                .replace(`{${"id"}}`, encodeURIComponent(String(id)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication bearer required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary ดึงข้อมูลการตอบคำถามทั้งหมดของแบบทดสอบ
         * @param {number} id รหัสของแบบทดสอบ
         * @param {any} limit Number of items per page
         * @param {any} page Page number to retrieve
         * @param {any} [sortBy] Field to sort by (e.g., id, name, email)
         * @param {any} [order] Sort order (ASC or DESC)
         * @param {any} [search] Search term to filter results
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        assessmentsControllerGetQuizQuestionResponses: async (id: number, limit: any, page: any, sortBy?: any, order?: any, search?: any, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            assertParamExists('assessmentsControllerGetQuizQuestionResponses', 'id', id)
            // verify required parameter 'limit' is not null or undefined
            assertParamExists('assessmentsControllerGetQuizQuestionResponses', 'limit', limit)
            // verify required parameter 'page' is not null or undefined
            assertParamExists('assessmentsControllerGetQuizQuestionResponses', 'page', page)
            const localVarPath = `/assessments/dashboard/quiz/{id}/questions`
                .replace(`{${"id"}}`, encodeURIComponent(String(id)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication bearer required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)

            if (sortBy !== undefined) {
                for (const [key, value] of Object.entries(sortBy)) {
                    localVarQueryParameter[key] = value;
                }
            }

            if (order !== undefined) {
                for (const [key, value] of Object.entries(order)) {
                    localVarQueryParameter[key] = value;
                }
            }

            if (limit !== undefined) {
                for (const [key, value] of Object.entries(limit)) {
                    localVarQueryParameter[key] = value;
                }
            }

            if (page !== undefined) {
                for (const [key, value] of Object.entries(page)) {
                    localVarQueryParameter[key] = value;
                }
            }

            if (search !== undefined) {
                for (const [key, value] of Object.entries(search)) {
                    localVarQueryParameter[key] = value;
                }
            }


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary Delete an assessment
         * @param {string} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        assessmentsControllerRemove: async (id: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            assertParamExists('assessmentsControllerRemove', 'id', id)
            const localVarPath = `/assessments/{id}`
                .replace(`{${"id"}}`, encodeURIComponent(String(id)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'DELETE', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication bearer required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary Save custom score for TEXTFIELD question
         * @param {number} submissionId Submission ID
         * @param {object} body 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        assessmentsControllerSaveTextFieldScore: async (submissionId: number, body: object, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'submissionId' is not null or undefined
            assertParamExists('assessmentsControllerSaveTextFieldScore', 'submissionId', submissionId)
            // verify required parameter 'body' is not null or undefined
            assertParamExists('assessmentsControllerSaveTextFieldScore', 'body', body)
            const localVarPath = `/assessments/dashboard/quiz/participant/{submissionId}/textfield-score`
                .replace(`{${"submissionId"}}`, encodeURIComponent(String(submissionId)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication bearer required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(body, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary Update an existing assessment
         * @param {number} id 
         * @param {number} [creatorUserId] รหัสผู้สร้างแบบประเมิน
         * @param {AssessmentType} [type] ประเภทของแบบประเมิน (QUIZ หรือ FORM)
         * @param {number} [programId] รหัสโปรแกรม
         * @param {string} [name] ชื่อแบบประเมิน
         * @param {number} [submitLimit] จำนวนครั้งที่อนุญาตให้ส่งแบบประเมิน
         * @param {number} [timeout] เวลาที่อนุญาต (วินาที)
         * @param {string} [startAt] วันที่เริ่มต้น
         * @param {string} [endAt] วันที่สิ้นสุด
         * @param {boolean} [status] สถานะการเปิดใช้งาน
         * @param {number} [totalScore] คะแนนรวม (สำหรับ Quiz เท่านั้น)
         * @param {number} [passRatio] อัตราส่วนการผ่าน (เช่น 0.5, 1.5)
         * @param {boolean} [isPrototype] เป็นต้นแบบหรือไม่
         * @param {boolean} [responseEdit] อนุญาตให้แก้ไขคำตอบหรือไม่
         * @param {string} [linkURL] URL ลิงก์ของแบบประเมิน
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        assessmentsControllerUpdate: async (id: number, creatorUserId?: number, type?: AssessmentType, programId?: number, name?: string, submitLimit?: number, timeout?: number, startAt?: string, endAt?: string, status?: boolean, totalScore?: number, passRatio?: number, isPrototype?: boolean, responseEdit?: boolean, linkURL?: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            assertParamExists('assessmentsControllerUpdate', 'id', id)
            const localVarPath = `/assessments/{id}`
                .replace(`{${"id"}}`, encodeURIComponent(String(id)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'PATCH', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;
            const localVarFormParams = new ((configuration && configuration.formDataCtor) || FormData)();

            // authentication bearer required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)


            if (creatorUserId !== undefined) { 
                localVarFormParams.append('creatorUserId', creatorUserId as any);
            }
    
            if (type !== undefined) { 
                localVarFormParams.append('type', type as any);
            }
    
            if (programId !== undefined) { 
                localVarFormParams.append('programId', programId as any);
            }
    
            if (name !== undefined) { 
                localVarFormParams.append('name', name as any);
            }
    
            if (submitLimit !== undefined) { 
                localVarFormParams.append('submitLimit', submitLimit as any);
            }
    
            if (timeout !== undefined) { 
                localVarFormParams.append('timeout', timeout as any);
            }
    
            if (startAt !== undefined) { 
                localVarFormParams.append('startAt', startAt as any);
            }
    
            if (endAt !== undefined) { 
                localVarFormParams.append('endAt', endAt as any);
            }
    
            if (status !== undefined) { 
                localVarFormParams.append('status', String(status) as any);
            }
    
            if (totalScore !== undefined) { 
                localVarFormParams.append('totalScore', totalScore as any);
            }
    
            if (passRatio !== undefined) { 
                localVarFormParams.append('passRatio', passRatio as any);
            }
    
            if (isPrototype !== undefined) { 
                localVarFormParams.append('isPrototype', String(isPrototype) as any);
            }
    
            if (responseEdit !== undefined) { 
                localVarFormParams.append('responseEdit', String(responseEdit) as any);
            }
    
            if (linkURL !== undefined) { 
                localVarFormParams.append('linkURL', linkURL as any);
            }
    
    
            localVarHeaderParameter['Content-Type'] = 'multipart/form-data';
    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = localVarFormParams;

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * AssessmentApi - functional programming interface
 * @export
 */
export const AssessmentApiFp = function(configuration?: Configuration) {
    const localVarAxiosParamCreator = AssessmentApiAxiosParamCreator(configuration)
    return {
        /**
         * 
         * @summary Copy content from one assessment to another
         * @param {number} sourceId Source assessment ID
         * @param {number} targetId Target assessment ID
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async assessmentsControllerCopyAssessmentContent(sourceId: number, targetId: number, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.assessmentsControllerCopyAssessmentContent(sourceId, targetId, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['AssessmentApi.assessmentsControllerCopyAssessmentContent']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @summary New Assessment (Quiz/Evaluate)
         * @param {number} creatorUserId รหัสผู้สร้างแบบประเมิน
         * @param {AssessmentType} type ประเภทของแบบประเมิน (QUIZ หรือ FORM)
         * @param {number} programId รหัสโปรแกรม
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async assessmentsControllerCreate(creatorUserId: number, type: AssessmentType, programId: number, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.assessmentsControllerCreate(creatorUserId, type, programId, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['AssessmentApi.assessmentsControllerCreate']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * Creates a complete assessment with customizable questions and submissions for dashboard testing
         * @summary Create Complete Mockup Data (Assessment + Submissions)
         * @param {number} [totalQuestions] Total number of questions (default: 100)
         * @param {number} [submissionCount] Number of submissions (default: 50)
         * @param {number} [totalScore] Total score (auto-calculated if not provided)
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async assessmentsControllerCreateCompleteAssessmentMockup(totalQuestions?: number, submissionCount?: number, totalScore?: number, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.assessmentsControllerCreateCompleteAssessmentMockup(totalQuestions, submissionCount, totalScore, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['AssessmentApi.assessmentsControllerCreateCompleteAssessmentMockup']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {number} [totalQuestions] Total number of questions (default: 100)
         * @param {number} [totalScore] Total score (auto-calculated if not provided)
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async assessmentsControllerCreateMockupAssessment(totalQuestions?: number, totalScore?: number, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.assessmentsControllerCreateMockupAssessment(totalQuestions, totalScore, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['AssessmentApi.assessmentsControllerCreateMockupAssessment']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * Creates 50 mockup submissions with realistic response patterns for the specified assessment
         * @summary Create Mockup Submissions for Assessment
         * @param {number} id Assessment ID to create submissions for
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async assessmentsControllerCreateMockupSubmissions(id: number, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.assessmentsControllerCreateMockupSubmissions(id, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['AssessmentApi.assessmentsControllerCreateMockupSubmissions']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * Creates a quick assessment with fewer questions for testing
         * @summary Create Quick Mockup Assessment
         * @param {number} [totalQuestions] Total number of questions (default: 20)
         * @param {number} [submissionCount] Number of submissions (default: 20)
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async assessmentsControllerCreateQuickMockup(totalQuestions?: number, submissionCount?: number, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.assessmentsControllerCreateQuickMockup(totalQuestions, submissionCount, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['AssessmentApi.assessmentsControllerCreateQuickMockup']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @summary Export assessment questions to Excel file
         * @param {number} id Assessment ID to export
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async assessmentsControllerExportAssessmentToExcel(id: number, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.assessmentsControllerExportAssessmentToExcel(id, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['AssessmentApi.assessmentsControllerExportAssessmentToExcel']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @summary Get a single assessment by ID with paginated itemBlocks
         * @param {number} id The ID of the assessment
         * @param {number} [page] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async assessmentsControllerFindOne(id: number, page?: number, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.assessmentsControllerFindOne(id, page, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['AssessmentApi.assessmentsControllerFindOne']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {AssessmentsControllerGetAllTypeEnum} type ประเภทของแบบประเมิน
         * @param {any} limit Number of items per page
         * @param {any} page Page number to retrieve
         * @param {string} [search] ค้นหาด้วยชื่อแบบประเมิน
         * @param {any} [sortBy] Field to sort by (e.g., id, name, email)
         * @param {any} [order] Sort order (ASC or DESC)
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async assessmentsControllerGetAll(type: AssessmentsControllerGetAllTypeEnum, limit: any, page: any, search?: string, sortBy?: any, order?: any, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.assessmentsControllerGetAll(type, limit, page, search, sortBy, order, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['AssessmentApi.assessmentsControllerGetAll']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {any} limit Number of items per page
         * @param {any} page Page number to retrieve
         * @param {AssessmentsControllerGetAllEditorTypeEnum} [type] 
         * @param {any} [sortBy] Field to sort by (e.g., id, name, email)
         * @param {any} [order] Sort order (ASC or DESC)
         * @param {any} [search] Search term to filter results
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async assessmentsControllerGetAllEditor(limit: any, page: any, type?: AssessmentsControllerGetAllEditorTypeEnum, sortBy?: any, order?: any, search?: any, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.assessmentsControllerGetAllEditor(limit, page, type, sortBy, order, search, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['AssessmentApi.assessmentsControllerGetAllEditor']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @summary Get all assessments that are prototypes (isPrototype=true)
         * @param {AssessmentsControllerGetAllPrototypesTypeEnum} type 
         * @param {any} limit Number of items per page
         * @param {any} page Page number to retrieve
         * @param {any} [sortBy] Field to sort by (e.g., id, name, email)
         * @param {any} [order] Sort order (ASC or DESC)
         * @param {any} [search] Search term to filter results
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async assessmentsControllerGetAllPrototypes(type: AssessmentsControllerGetAllPrototypesTypeEnum, limit: any, page: any, sortBy?: any, order?: any, search?: any, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.assessmentsControllerGetAllPrototypes(type, limit, page, sortBy, order, search, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['AssessmentApi.assessmentsControllerGetAllPrototypes']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {any} limit Number of items per page
         * @param {any} page Page number to retrieve
         * @param {AssessmentsControllerGetAllUserTypeEnum} [type] 
         * @param {any} [sortBy] Field to sort by (e.g., id, name, email)
         * @param {any} [order] Sort order (ASC or DESC)
         * @param {any} [search] Search term to filter results
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async assessmentsControllerGetAllUser(limit: any, page: any, type?: AssessmentsControllerGetAllUserTypeEnum, sortBy?: any, order?: any, search?: any, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.assessmentsControllerGetAllUser(limit, page, type, sortBy, order, search, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['AssessmentApi.assessmentsControllerGetAllUser']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @summary Get assessment by URL
         * @param {string} url 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async assessmentsControllerGetByURL(url: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.assessmentsControllerGetByURL(url, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['AssessmentApi.assessmentsControllerGetByURL']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @summary ดึงข่อมูลสำหรับโชว์ Graph
         * @param {number} assessmentId 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async assessmentsControllerGetChartData(assessmentId: number, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.assessmentsControllerGetChartData(assessmentId, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['AssessmentApi.assessmentsControllerGetChartData']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @summary ดึงข่อมูลจำนวนของResponses ทั้งหมด
         * @param {number} assessmentId 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async assessmentsControllerGetNumberOfResponses(assessmentId: number, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.assessmentsControllerGetNumberOfResponses(assessmentId, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['AssessmentApi.assessmentsControllerGetNumberOfResponses']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @summary Get assessment by section
         * @param {number} id 
         * @param {number} section 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async assessmentsControllerGetOne(id: number, section: number, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.assessmentsControllerGetOne(id, section, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['AssessmentApi.assessmentsControllerGetOne']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @summary ดึงข้อมูลแดชบอร์ดของแบบทดสอบ
         * @param {number} id รหัสของแบบทดสอบ
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async assessmentsControllerGetQuizDashboardMeta(id: number, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.assessmentsControllerGetQuizDashboardMeta(id, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['AssessmentApi.assessmentsControllerGetQuizDashboardMeta']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @summary Get quiz header with user submission history
         * @param {string} linkUrl 
         * @param {number} userId 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async assessmentsControllerGetQuizHeaderWithUserSubmissions(linkUrl: string, userId: number, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.assessmentsControllerGetQuizHeaderWithUserSubmissions(linkUrl, userId, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['AssessmentApi.assessmentsControllerGetQuizHeaderWithUserSubmissions']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @summary ดึงข้อมูลรายละเอียดของผู้เข้าร่วมทำแบบทดสอบเฉพาะราย
         * @param {number} id รหัสของผู้เข้าร่วม
         * @param {any} limit Number of items per page
         * @param {any} page Page number to retrieve
         * @param {any} [sortBy] Field to sort by (e.g., id, name, email)
         * @param {any} [order] Sort order (ASC or DESC)
         * @param {any} [search] Search term to filter results
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async assessmentsControllerGetQuizParticipantDetails(id: number, limit: any, page: any, sortBy?: any, order?: any, search?: any, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.assessmentsControllerGetQuizParticipantDetails(id, limit, page, sortBy, order, search, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['AssessmentApi.assessmentsControllerGetQuizParticipantDetails']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @summary ดึงข้อมูลรายละเอียดของผู้เข้าร่วมทำแบบทดสอบสำหรับการให้คะแนน TEXTFIELD
         * @param {number} id รหัสของผู้เข้าร่วม
         * @param {any} limit Number of items per page
         * @param {any} page Page number to retrieve
         * @param {any} [sortBy] Field to sort by (e.g., id, name, email)
         * @param {any} [order] Sort order (ASC or DESC)
         * @param {any} [search] Search term to filter results
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async assessmentsControllerGetQuizParticipantTextFieldGrading(id: number, limit: any, page: any, sortBy?: any, order?: any, search?: any, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.assessmentsControllerGetQuizParticipantTextFieldGrading(id, limit, page, sortBy, order, search, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['AssessmentApi.assessmentsControllerGetQuizParticipantTextFieldGrading']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @summary ดึงข้อมูลผู้เข้าร่วมทำแบบทดสอบทั้งหมด
         * @param {number} id รหัสของแบบทดสอบ
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async assessmentsControllerGetQuizParticipants(id: number, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.assessmentsControllerGetQuizParticipants(id, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['AssessmentApi.assessmentsControllerGetQuizParticipants']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @summary ดึงข้อมูลการตอบคำถามทั้งหมดของแบบทดสอบ
         * @param {number} id รหัสของแบบทดสอบ
         * @param {any} limit Number of items per page
         * @param {any} page Page number to retrieve
         * @param {any} [sortBy] Field to sort by (e.g., id, name, email)
         * @param {any} [order] Sort order (ASC or DESC)
         * @param {any} [search] Search term to filter results
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async assessmentsControllerGetQuizQuestionResponses(id: number, limit: any, page: any, sortBy?: any, order?: any, search?: any, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.assessmentsControllerGetQuizQuestionResponses(id, limit, page, sortBy, order, search, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['AssessmentApi.assessmentsControllerGetQuizQuestionResponses']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @summary Delete an assessment
         * @param {string} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async assessmentsControllerRemove(id: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.assessmentsControllerRemove(id, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['AssessmentApi.assessmentsControllerRemove']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @summary Save custom score for TEXTFIELD question
         * @param {number} submissionId Submission ID
         * @param {object} body 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async assessmentsControllerSaveTextFieldScore(submissionId: number, body: object, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<AssessmentsControllerSaveTextFieldScore200Response>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.assessmentsControllerSaveTextFieldScore(submissionId, body, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['AssessmentApi.assessmentsControllerSaveTextFieldScore']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @summary Update an existing assessment
         * @param {number} id 
         * @param {number} [creatorUserId] รหัสผู้สร้างแบบประเมิน
         * @param {AssessmentType} [type] ประเภทของแบบประเมิน (QUIZ หรือ FORM)
         * @param {number} [programId] รหัสโปรแกรม
         * @param {string} [name] ชื่อแบบประเมิน
         * @param {number} [submitLimit] จำนวนครั้งที่อนุญาตให้ส่งแบบประเมิน
         * @param {number} [timeout] เวลาที่อนุญาต (วินาที)
         * @param {string} [startAt] วันที่เริ่มต้น
         * @param {string} [endAt] วันที่สิ้นสุด
         * @param {boolean} [status] สถานะการเปิดใช้งาน
         * @param {number} [totalScore] คะแนนรวม (สำหรับ Quiz เท่านั้น)
         * @param {number} [passRatio] อัตราส่วนการผ่าน (เช่น 0.5, 1.5)
         * @param {boolean} [isPrototype] เป็นต้นแบบหรือไม่
         * @param {boolean} [responseEdit] อนุญาตให้แก้ไขคำตอบหรือไม่
         * @param {string} [linkURL] URL ลิงก์ของแบบประเมิน
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async assessmentsControllerUpdate(id: number, creatorUserId?: number, type?: AssessmentType, programId?: number, name?: string, submitLimit?: number, timeout?: number, startAt?: string, endAt?: string, status?: boolean, totalScore?: number, passRatio?: number, isPrototype?: boolean, responseEdit?: boolean, linkURL?: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.assessmentsControllerUpdate(id, creatorUserId, type, programId, name, submitLimit, timeout, startAt, endAt, status, totalScore, passRatio, isPrototype, responseEdit, linkURL, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['AssessmentApi.assessmentsControllerUpdate']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
    }
};

/**
 * AssessmentApi - factory interface
 * @export
 */
export const AssessmentApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    const localVarFp = AssessmentApiFp(configuration)
    return {
        /**
         * 
         * @summary Copy content from one assessment to another
         * @param {number} sourceId Source assessment ID
         * @param {number} targetId Target assessment ID
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        assessmentsControllerCopyAssessmentContent(sourceId: number, targetId: number, options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.assessmentsControllerCopyAssessmentContent(sourceId, targetId, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary New Assessment (Quiz/Evaluate)
         * @param {number} creatorUserId รหัสผู้สร้างแบบประเมิน
         * @param {AssessmentType} type ประเภทของแบบประเมิน (QUIZ หรือ FORM)
         * @param {number} programId รหัสโปรแกรม
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        assessmentsControllerCreate(creatorUserId: number, type: AssessmentType, programId: number, options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.assessmentsControllerCreate(creatorUserId, type, programId, options).then((request) => request(axios, basePath));
        },
        /**
         * Creates a complete assessment with customizable questions and submissions for dashboard testing
         * @summary Create Complete Mockup Data (Assessment + Submissions)
         * @param {number} [totalQuestions] Total number of questions (default: 100)
         * @param {number} [submissionCount] Number of submissions (default: 50)
         * @param {number} [totalScore] Total score (auto-calculated if not provided)
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        assessmentsControllerCreateCompleteAssessmentMockup(totalQuestions?: number, submissionCount?: number, totalScore?: number, options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.assessmentsControllerCreateCompleteAssessmentMockup(totalQuestions, submissionCount, totalScore, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {number} [totalQuestions] Total number of questions (default: 100)
         * @param {number} [totalScore] Total score (auto-calculated if not provided)
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        assessmentsControllerCreateMockupAssessment(totalQuestions?: number, totalScore?: number, options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.assessmentsControllerCreateMockupAssessment(totalQuestions, totalScore, options).then((request) => request(axios, basePath));
        },
        /**
         * Creates 50 mockup submissions with realistic response patterns for the specified assessment
         * @summary Create Mockup Submissions for Assessment
         * @param {number} id Assessment ID to create submissions for
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        assessmentsControllerCreateMockupSubmissions(id: number, options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.assessmentsControllerCreateMockupSubmissions(id, options).then((request) => request(axios, basePath));
        },
        /**
         * Creates a quick assessment with fewer questions for testing
         * @summary Create Quick Mockup Assessment
         * @param {number} [totalQuestions] Total number of questions (default: 20)
         * @param {number} [submissionCount] Number of submissions (default: 20)
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        assessmentsControllerCreateQuickMockup(totalQuestions?: number, submissionCount?: number, options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.assessmentsControllerCreateQuickMockup(totalQuestions, submissionCount, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary Export assessment questions to Excel file
         * @param {number} id Assessment ID to export
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        assessmentsControllerExportAssessmentToExcel(id: number, options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.assessmentsControllerExportAssessmentToExcel(id, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary Get a single assessment by ID with paginated itemBlocks
         * @param {number} id The ID of the assessment
         * @param {number} [page] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        assessmentsControllerFindOne(id: number, page?: number, options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.assessmentsControllerFindOne(id, page, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {AssessmentsControllerGetAllTypeEnum} type ประเภทของแบบประเมิน
         * @param {any} limit Number of items per page
         * @param {any} page Page number to retrieve
         * @param {string} [search] ค้นหาด้วยชื่อแบบประเมิน
         * @param {any} [sortBy] Field to sort by (e.g., id, name, email)
         * @param {any} [order] Sort order (ASC or DESC)
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        assessmentsControllerGetAll(type: AssessmentsControllerGetAllTypeEnum, limit: any, page: any, search?: string, sortBy?: any, order?: any, options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.assessmentsControllerGetAll(type, limit, page, search, sortBy, order, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {any} limit Number of items per page
         * @param {any} page Page number to retrieve
         * @param {AssessmentsControllerGetAllEditorTypeEnum} [type] 
         * @param {any} [sortBy] Field to sort by (e.g., id, name, email)
         * @param {any} [order] Sort order (ASC or DESC)
         * @param {any} [search] Search term to filter results
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        assessmentsControllerGetAllEditor(limit: any, page: any, type?: AssessmentsControllerGetAllEditorTypeEnum, sortBy?: any, order?: any, search?: any, options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.assessmentsControllerGetAllEditor(limit, page, type, sortBy, order, search, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary Get all assessments that are prototypes (isPrototype=true)
         * @param {AssessmentsControllerGetAllPrototypesTypeEnum} type 
         * @param {any} limit Number of items per page
         * @param {any} page Page number to retrieve
         * @param {any} [sortBy] Field to sort by (e.g., id, name, email)
         * @param {any} [order] Sort order (ASC or DESC)
         * @param {any} [search] Search term to filter results
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        assessmentsControllerGetAllPrototypes(type: AssessmentsControllerGetAllPrototypesTypeEnum, limit: any, page: any, sortBy?: any, order?: any, search?: any, options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.assessmentsControllerGetAllPrototypes(type, limit, page, sortBy, order, search, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {any} limit Number of items per page
         * @param {any} page Page number to retrieve
         * @param {AssessmentsControllerGetAllUserTypeEnum} [type] 
         * @param {any} [sortBy] Field to sort by (e.g., id, name, email)
         * @param {any} [order] Sort order (ASC or DESC)
         * @param {any} [search] Search term to filter results
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        assessmentsControllerGetAllUser(limit: any, page: any, type?: AssessmentsControllerGetAllUserTypeEnum, sortBy?: any, order?: any, search?: any, options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.assessmentsControllerGetAllUser(limit, page, type, sortBy, order, search, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary Get assessment by URL
         * @param {string} url 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        assessmentsControllerGetByURL(url: string, options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.assessmentsControllerGetByURL(url, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary ดึงข่อมูลสำหรับโชว์ Graph
         * @param {number} assessmentId 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        assessmentsControllerGetChartData(assessmentId: number, options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.assessmentsControllerGetChartData(assessmentId, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary ดึงข่อมูลจำนวนของResponses ทั้งหมด
         * @param {number} assessmentId 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        assessmentsControllerGetNumberOfResponses(assessmentId: number, options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.assessmentsControllerGetNumberOfResponses(assessmentId, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary Get assessment by section
         * @param {number} id 
         * @param {number} section 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        assessmentsControllerGetOne(id: number, section: number, options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.assessmentsControllerGetOne(id, section, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary ดึงข้อมูลแดชบอร์ดของแบบทดสอบ
         * @param {number} id รหัสของแบบทดสอบ
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        assessmentsControllerGetQuizDashboardMeta(id: number, options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.assessmentsControllerGetQuizDashboardMeta(id, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary Get quiz header with user submission history
         * @param {string} linkUrl 
         * @param {number} userId 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        assessmentsControllerGetQuizHeaderWithUserSubmissions(linkUrl: string, userId: number, options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.assessmentsControllerGetQuizHeaderWithUserSubmissions(linkUrl, userId, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary ดึงข้อมูลรายละเอียดของผู้เข้าร่วมทำแบบทดสอบเฉพาะราย
         * @param {number} id รหัสของผู้เข้าร่วม
         * @param {any} limit Number of items per page
         * @param {any} page Page number to retrieve
         * @param {any} [sortBy] Field to sort by (e.g., id, name, email)
         * @param {any} [order] Sort order (ASC or DESC)
         * @param {any} [search] Search term to filter results
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        assessmentsControllerGetQuizParticipantDetails(id: number, limit: any, page: any, sortBy?: any, order?: any, search?: any, options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.assessmentsControllerGetQuizParticipantDetails(id, limit, page, sortBy, order, search, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary ดึงข้อมูลรายละเอียดของผู้เข้าร่วมทำแบบทดสอบสำหรับการให้คะแนน TEXTFIELD
         * @param {number} id รหัสของผู้เข้าร่วม
         * @param {any} limit Number of items per page
         * @param {any} page Page number to retrieve
         * @param {any} [sortBy] Field to sort by (e.g., id, name, email)
         * @param {any} [order] Sort order (ASC or DESC)
         * @param {any} [search] Search term to filter results
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        assessmentsControllerGetQuizParticipantTextFieldGrading(id: number, limit: any, page: any, sortBy?: any, order?: any, search?: any, options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.assessmentsControllerGetQuizParticipantTextFieldGrading(id, limit, page, sortBy, order, search, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary ดึงข้อมูลผู้เข้าร่วมทำแบบทดสอบทั้งหมด
         * @param {number} id รหัสของแบบทดสอบ
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        assessmentsControllerGetQuizParticipants(id: number, options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.assessmentsControllerGetQuizParticipants(id, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary ดึงข้อมูลการตอบคำถามทั้งหมดของแบบทดสอบ
         * @param {number} id รหัสของแบบทดสอบ
         * @param {any} limit Number of items per page
         * @param {any} page Page number to retrieve
         * @param {any} [sortBy] Field to sort by (e.g., id, name, email)
         * @param {any} [order] Sort order (ASC or DESC)
         * @param {any} [search] Search term to filter results
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        assessmentsControllerGetQuizQuestionResponses(id: number, limit: any, page: any, sortBy?: any, order?: any, search?: any, options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.assessmentsControllerGetQuizQuestionResponses(id, limit, page, sortBy, order, search, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary Delete an assessment
         * @param {string} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        assessmentsControllerRemove(id: string, options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.assessmentsControllerRemove(id, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary Save custom score for TEXTFIELD question
         * @param {number} submissionId Submission ID
         * @param {object} body 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        assessmentsControllerSaveTextFieldScore(submissionId: number, body: object, options?: RawAxiosRequestConfig): AxiosPromise<AssessmentsControllerSaveTextFieldScore200Response> {
            return localVarFp.assessmentsControllerSaveTextFieldScore(submissionId, body, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary Update an existing assessment
         * @param {number} id 
         * @param {number} [creatorUserId] รหัสผู้สร้างแบบประเมิน
         * @param {AssessmentType} [type] ประเภทของแบบประเมิน (QUIZ หรือ FORM)
         * @param {number} [programId] รหัสโปรแกรม
         * @param {string} [name] ชื่อแบบประเมิน
         * @param {number} [submitLimit] จำนวนครั้งที่อนุญาตให้ส่งแบบประเมิน
         * @param {number} [timeout] เวลาที่อนุญาต (วินาที)
         * @param {string} [startAt] วันที่เริ่มต้น
         * @param {string} [endAt] วันที่สิ้นสุด
         * @param {boolean} [status] สถานะการเปิดใช้งาน
         * @param {number} [totalScore] คะแนนรวม (สำหรับ Quiz เท่านั้น)
         * @param {number} [passRatio] อัตราส่วนการผ่าน (เช่น 0.5, 1.5)
         * @param {boolean} [isPrototype] เป็นต้นแบบหรือไม่
         * @param {boolean} [responseEdit] อนุญาตให้แก้ไขคำตอบหรือไม่
         * @param {string} [linkURL] URL ลิงก์ของแบบประเมิน
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        assessmentsControllerUpdate(id: number, creatorUserId?: number, type?: AssessmentType, programId?: number, name?: string, submitLimit?: number, timeout?: number, startAt?: string, endAt?: string, status?: boolean, totalScore?: number, passRatio?: number, isPrototype?: boolean, responseEdit?: boolean, linkURL?: string, options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.assessmentsControllerUpdate(id, creatorUserId, type, programId, name, submitLimit, timeout, startAt, endAt, status, totalScore, passRatio, isPrototype, responseEdit, linkURL, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * AssessmentApi - object-oriented interface
 * @export
 * @class AssessmentApi
 * @extends {BaseAPI}
 */
export class AssessmentApi extends BaseAPI {
    /**
     * 
     * @summary Copy content from one assessment to another
     * @param {number} sourceId Source assessment ID
     * @param {number} targetId Target assessment ID
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof AssessmentApi
     */
    public assessmentsControllerCopyAssessmentContent(sourceId: number, targetId: number, options?: RawAxiosRequestConfig) {
        return AssessmentApiFp(this.configuration).assessmentsControllerCopyAssessmentContent(sourceId, targetId, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @summary New Assessment (Quiz/Evaluate)
     * @param {number} creatorUserId รหัสผู้สร้างแบบประเมิน
     * @param {AssessmentType} type ประเภทของแบบประเมิน (QUIZ หรือ FORM)
     * @param {number} programId รหัสโปรแกรม
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof AssessmentApi
     */
    public assessmentsControllerCreate(creatorUserId: number, type: AssessmentType, programId: number, options?: RawAxiosRequestConfig) {
        return AssessmentApiFp(this.configuration).assessmentsControllerCreate(creatorUserId, type, programId, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * Creates a complete assessment with customizable questions and submissions for dashboard testing
     * @summary Create Complete Mockup Data (Assessment + Submissions)
     * @param {number} [totalQuestions] Total number of questions (default: 100)
     * @param {number} [submissionCount] Number of submissions (default: 50)
     * @param {number} [totalScore] Total score (auto-calculated if not provided)
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof AssessmentApi
     */
    public assessmentsControllerCreateCompleteAssessmentMockup(totalQuestions?: number, submissionCount?: number, totalScore?: number, options?: RawAxiosRequestConfig) {
        return AssessmentApiFp(this.configuration).assessmentsControllerCreateCompleteAssessmentMockup(totalQuestions, submissionCount, totalScore, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {number} [totalQuestions] Total number of questions (default: 100)
     * @param {number} [totalScore] Total score (auto-calculated if not provided)
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof AssessmentApi
     */
    public assessmentsControllerCreateMockupAssessment(totalQuestions?: number, totalScore?: number, options?: RawAxiosRequestConfig) {
        return AssessmentApiFp(this.configuration).assessmentsControllerCreateMockupAssessment(totalQuestions, totalScore, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * Creates 50 mockup submissions with realistic response patterns for the specified assessment
     * @summary Create Mockup Submissions for Assessment
     * @param {number} id Assessment ID to create submissions for
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof AssessmentApi
     */
    public assessmentsControllerCreateMockupSubmissions(id: number, options?: RawAxiosRequestConfig) {
        return AssessmentApiFp(this.configuration).assessmentsControllerCreateMockupSubmissions(id, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * Creates a quick assessment with fewer questions for testing
     * @summary Create Quick Mockup Assessment
     * @param {number} [totalQuestions] Total number of questions (default: 20)
     * @param {number} [submissionCount] Number of submissions (default: 20)
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof AssessmentApi
     */
    public assessmentsControllerCreateQuickMockup(totalQuestions?: number, submissionCount?: number, options?: RawAxiosRequestConfig) {
        return AssessmentApiFp(this.configuration).assessmentsControllerCreateQuickMockup(totalQuestions, submissionCount, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @summary Export assessment questions to Excel file
     * @param {number} id Assessment ID to export
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof AssessmentApi
     */
    public assessmentsControllerExportAssessmentToExcel(id: number, options?: RawAxiosRequestConfig) {
        return AssessmentApiFp(this.configuration).assessmentsControllerExportAssessmentToExcel(id, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @summary Get a single assessment by ID with paginated itemBlocks
     * @param {number} id The ID of the assessment
     * @param {number} [page] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof AssessmentApi
     */
    public assessmentsControllerFindOne(id: number, page?: number, options?: RawAxiosRequestConfig) {
        return AssessmentApiFp(this.configuration).assessmentsControllerFindOne(id, page, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {AssessmentsControllerGetAllTypeEnum} type ประเภทของแบบประเมิน
     * @param {any} limit Number of items per page
     * @param {any} page Page number to retrieve
     * @param {string} [search] ค้นหาด้วยชื่อแบบประเมิน
     * @param {any} [sortBy] Field to sort by (e.g., id, name, email)
     * @param {any} [order] Sort order (ASC or DESC)
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof AssessmentApi
     */
    public assessmentsControllerGetAll(type: AssessmentsControllerGetAllTypeEnum, limit: any, page: any, search?: string, sortBy?: any, order?: any, options?: RawAxiosRequestConfig) {
        return AssessmentApiFp(this.configuration).assessmentsControllerGetAll(type, limit, page, search, sortBy, order, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {any} limit Number of items per page
     * @param {any} page Page number to retrieve
     * @param {AssessmentsControllerGetAllEditorTypeEnum} [type] 
     * @param {any} [sortBy] Field to sort by (e.g., id, name, email)
     * @param {any} [order] Sort order (ASC or DESC)
     * @param {any} [search] Search term to filter results
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof AssessmentApi
     */
    public assessmentsControllerGetAllEditor(limit: any, page: any, type?: AssessmentsControllerGetAllEditorTypeEnum, sortBy?: any, order?: any, search?: any, options?: RawAxiosRequestConfig) {
        return AssessmentApiFp(this.configuration).assessmentsControllerGetAllEditor(limit, page, type, sortBy, order, search, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @summary Get all assessments that are prototypes (isPrototype=true)
     * @param {AssessmentsControllerGetAllPrototypesTypeEnum} type 
     * @param {any} limit Number of items per page
     * @param {any} page Page number to retrieve
     * @param {any} [sortBy] Field to sort by (e.g., id, name, email)
     * @param {any} [order] Sort order (ASC or DESC)
     * @param {any} [search] Search term to filter results
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof AssessmentApi
     */
    public assessmentsControllerGetAllPrototypes(type: AssessmentsControllerGetAllPrototypesTypeEnum, limit: any, page: any, sortBy?: any, order?: any, search?: any, options?: RawAxiosRequestConfig) {
        return AssessmentApiFp(this.configuration).assessmentsControllerGetAllPrototypes(type, limit, page, sortBy, order, search, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {any} limit Number of items per page
     * @param {any} page Page number to retrieve
     * @param {AssessmentsControllerGetAllUserTypeEnum} [type] 
     * @param {any} [sortBy] Field to sort by (e.g., id, name, email)
     * @param {any} [order] Sort order (ASC or DESC)
     * @param {any} [search] Search term to filter results
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof AssessmentApi
     */
    public assessmentsControllerGetAllUser(limit: any, page: any, type?: AssessmentsControllerGetAllUserTypeEnum, sortBy?: any, order?: any, search?: any, options?: RawAxiosRequestConfig) {
        return AssessmentApiFp(this.configuration).assessmentsControllerGetAllUser(limit, page, type, sortBy, order, search, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @summary Get assessment by URL
     * @param {string} url 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof AssessmentApi
     */
    public assessmentsControllerGetByURL(url: string, options?: RawAxiosRequestConfig) {
        return AssessmentApiFp(this.configuration).assessmentsControllerGetByURL(url, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @summary ดึงข่อมูลสำหรับโชว์ Graph
     * @param {number} assessmentId 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof AssessmentApi
     */
    public assessmentsControllerGetChartData(assessmentId: number, options?: RawAxiosRequestConfig) {
        return AssessmentApiFp(this.configuration).assessmentsControllerGetChartData(assessmentId, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @summary ดึงข่อมูลจำนวนของResponses ทั้งหมด
     * @param {number} assessmentId 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof AssessmentApi
     */
    public assessmentsControllerGetNumberOfResponses(assessmentId: number, options?: RawAxiosRequestConfig) {
        return AssessmentApiFp(this.configuration).assessmentsControllerGetNumberOfResponses(assessmentId, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @summary Get assessment by section
     * @param {number} id 
     * @param {number} section 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof AssessmentApi
     */
    public assessmentsControllerGetOne(id: number, section: number, options?: RawAxiosRequestConfig) {
        return AssessmentApiFp(this.configuration).assessmentsControllerGetOne(id, section, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @summary ดึงข้อมูลแดชบอร์ดของแบบทดสอบ
     * @param {number} id รหัสของแบบทดสอบ
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof AssessmentApi
     */
    public assessmentsControllerGetQuizDashboardMeta(id: number, options?: RawAxiosRequestConfig) {
        return AssessmentApiFp(this.configuration).assessmentsControllerGetQuizDashboardMeta(id, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @summary Get quiz header with user submission history
     * @param {string} linkUrl 
     * @param {number} userId 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof AssessmentApi
     */
    public assessmentsControllerGetQuizHeaderWithUserSubmissions(linkUrl: string, userId: number, options?: RawAxiosRequestConfig) {
        return AssessmentApiFp(this.configuration).assessmentsControllerGetQuizHeaderWithUserSubmissions(linkUrl, userId, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @summary ดึงข้อมูลรายละเอียดของผู้เข้าร่วมทำแบบทดสอบเฉพาะราย
     * @param {number} id รหัสของผู้เข้าร่วม
     * @param {any} limit Number of items per page
     * @param {any} page Page number to retrieve
     * @param {any} [sortBy] Field to sort by (e.g., id, name, email)
     * @param {any} [order] Sort order (ASC or DESC)
     * @param {any} [search] Search term to filter results
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof AssessmentApi
     */
    public assessmentsControllerGetQuizParticipantDetails(id: number, limit: any, page: any, sortBy?: any, order?: any, search?: any, options?: RawAxiosRequestConfig) {
        return AssessmentApiFp(this.configuration).assessmentsControllerGetQuizParticipantDetails(id, limit, page, sortBy, order, search, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @summary ดึงข้อมูลรายละเอียดของผู้เข้าร่วมทำแบบทดสอบสำหรับการให้คะแนน TEXTFIELD
     * @param {number} id รหัสของผู้เข้าร่วม
     * @param {any} limit Number of items per page
     * @param {any} page Page number to retrieve
     * @param {any} [sortBy] Field to sort by (e.g., id, name, email)
     * @param {any} [order] Sort order (ASC or DESC)
     * @param {any} [search] Search term to filter results
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof AssessmentApi
     */
    public assessmentsControllerGetQuizParticipantTextFieldGrading(id: number, limit: any, page: any, sortBy?: any, order?: any, search?: any, options?: RawAxiosRequestConfig) {
        return AssessmentApiFp(this.configuration).assessmentsControllerGetQuizParticipantTextFieldGrading(id, limit, page, sortBy, order, search, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @summary ดึงข้อมูลผู้เข้าร่วมทำแบบทดสอบทั้งหมด
     * @param {number} id รหัสของแบบทดสอบ
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof AssessmentApi
     */
    public assessmentsControllerGetQuizParticipants(id: number, options?: RawAxiosRequestConfig) {
        return AssessmentApiFp(this.configuration).assessmentsControllerGetQuizParticipants(id, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @summary ดึงข้อมูลการตอบคำถามทั้งหมดของแบบทดสอบ
     * @param {number} id รหัสของแบบทดสอบ
     * @param {any} limit Number of items per page
     * @param {any} page Page number to retrieve
     * @param {any} [sortBy] Field to sort by (e.g., id, name, email)
     * @param {any} [order] Sort order (ASC or DESC)
     * @param {any} [search] Search term to filter results
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof AssessmentApi
     */
    public assessmentsControllerGetQuizQuestionResponses(id: number, limit: any, page: any, sortBy?: any, order?: any, search?: any, options?: RawAxiosRequestConfig) {
        return AssessmentApiFp(this.configuration).assessmentsControllerGetQuizQuestionResponses(id, limit, page, sortBy, order, search, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @summary Delete an assessment
     * @param {string} id 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof AssessmentApi
     */
    public assessmentsControllerRemove(id: string, options?: RawAxiosRequestConfig) {
        return AssessmentApiFp(this.configuration).assessmentsControllerRemove(id, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @summary Save custom score for TEXTFIELD question
     * @param {number} submissionId Submission ID
     * @param {object} body 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof AssessmentApi
     */
    public assessmentsControllerSaveTextFieldScore(submissionId: number, body: object, options?: RawAxiosRequestConfig) {
        return AssessmentApiFp(this.configuration).assessmentsControllerSaveTextFieldScore(submissionId, body, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @summary Update an existing assessment
     * @param {number} id 
     * @param {number} [creatorUserId] รหัสผู้สร้างแบบประเมิน
     * @param {AssessmentType} [type] ประเภทของแบบประเมิน (QUIZ หรือ FORM)
     * @param {number} [programId] รหัสโปรแกรม
     * @param {string} [name] ชื่อแบบประเมิน
     * @param {number} [submitLimit] จำนวนครั้งที่อนุญาตให้ส่งแบบประเมิน
     * @param {number} [timeout] เวลาที่อนุญาต (วินาที)
     * @param {string} [startAt] วันที่เริ่มต้น
     * @param {string} [endAt] วันที่สิ้นสุด
     * @param {boolean} [status] สถานะการเปิดใช้งาน
     * @param {number} [totalScore] คะแนนรวม (สำหรับ Quiz เท่านั้น)
     * @param {number} [passRatio] อัตราส่วนการผ่าน (เช่น 0.5, 1.5)
     * @param {boolean} [isPrototype] เป็นต้นแบบหรือไม่
     * @param {boolean} [responseEdit] อนุญาตให้แก้ไขคำตอบหรือไม่
     * @param {string} [linkURL] URL ลิงก์ของแบบประเมิน
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof AssessmentApi
     */
    public assessmentsControllerUpdate(id: number, creatorUserId?: number, type?: AssessmentType, programId?: number, name?: string, submitLimit?: number, timeout?: number, startAt?: string, endAt?: string, status?: boolean, totalScore?: number, passRatio?: number, isPrototype?: boolean, responseEdit?: boolean, linkURL?: string, options?: RawAxiosRequestConfig) {
        return AssessmentApiFp(this.configuration).assessmentsControllerUpdate(id, creatorUserId, type, programId, name, submitLimit, timeout, startAt, endAt, status, totalScore, passRatio, isPrototype, responseEdit, linkURL, options).then((request) => request(this.axios, this.basePath));
    }
}

/**
 * @export
 */
export const AssessmentsControllerGetAllTypeEnum = {
    Evaluate: 'EVALUATE',
    Quiz: 'QUIZ'
} as const;
export type AssessmentsControllerGetAllTypeEnum = typeof AssessmentsControllerGetAllTypeEnum[keyof typeof AssessmentsControllerGetAllTypeEnum];
/**
 * @export
 */
export const AssessmentsControllerGetAllEditorTypeEnum = {
    Evaluate: 'EVALUATE',
    Quiz: 'QUIZ'
} as const;
export type AssessmentsControllerGetAllEditorTypeEnum = typeof AssessmentsControllerGetAllEditorTypeEnum[keyof typeof AssessmentsControllerGetAllEditorTypeEnum];
/**
 * @export
 */
export const AssessmentsControllerGetAllPrototypesTypeEnum = {
    Evaluate: 'EVALUATE',
    Quiz: 'QUIZ'
} as const;
export type AssessmentsControllerGetAllPrototypesTypeEnum = typeof AssessmentsControllerGetAllPrototypesTypeEnum[keyof typeof AssessmentsControllerGetAllPrototypesTypeEnum];
/**
 * @export
 */
export const AssessmentsControllerGetAllUserTypeEnum = {
    Evaluate: 'EVALUATE',
    Quiz: 'QUIZ'
} as const;
export type AssessmentsControllerGetAllUserTypeEnum = typeof AssessmentsControllerGetAllUserTypeEnum[keyof typeof AssessmentsControllerGetAllUserTypeEnum];
