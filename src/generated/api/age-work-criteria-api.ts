/* tslint:disable */
/* eslint-disable */
/**
 * HRD Backend API
 * Human Resource Development Backend API Documentation
 *
 * The version of the OpenAPI document: 1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


import type { Configuration } from '../configuration';
import type { AxiosPromise, AxiosInstance, RawAxiosRequestConfig } from 'axios';
import globalAxios from 'axios';
// Some imports not used depending on template conditions
// @ts-ignore
import { DUMMY_BASE_URL, assertParamExists, setApiKeyToObject, setBasicAuthToObject, setBearerAuthToObject, setOAuthToObject, setSearchParams, serializeDataIfNeeded, toPathString, createRequestFunction } from '../common';
// @ts-ignore
import { BASE_PATH, COLLECTION_FORMATS, type RequestArgs, BaseAPI, RequiredError, operationServerMap } from '../base';
// @ts-ignore
import type { CreateAgeWorkCriteriaDto } from '../models';
// @ts-ignore
import type { UpdateAgeWorkCriteriaDto } from '../models';
/**
 * AgeWorkCriteriaApi - axios parameter creator
 * @export
 */
export const AgeWorkCriteriaApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * 
         * @summary Create a new age work criteria
         * @param {CreateAgeWorkCriteriaDto} createAgeWorkCriteriaDto 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        ageWorkCriteriaControllerCreate: async (createAgeWorkCriteriaDto: CreateAgeWorkCriteriaDto, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'createAgeWorkCriteriaDto' is not null or undefined
            assertParamExists('ageWorkCriteriaControllerCreate', 'createAgeWorkCriteriaDto', createAgeWorkCriteriaDto)
            const localVarPath = `/age-work-criteria`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication bearer required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(createAgeWorkCriteriaDto, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary Get all age works by criteria ID
         * @param {number} id Age work criteria ID
         * @param {any} limit Number of items per page
         * @param {any} page Page number to retrieve
         * @param {any} [sortBy] Field to sort by (e.g., id, name, email)
         * @param {any} [order] Sort order (ASC or DESC)
         * @param {any} [search] Search term to filter results
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        ageWorkCriteriaControllerFindAgeWorksByCriteria: async (id: number, limit: any, page: any, sortBy?: any, order?: any, search?: any, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            assertParamExists('ageWorkCriteriaControllerFindAgeWorksByCriteria', 'id', id)
            // verify required parameter 'limit' is not null or undefined
            assertParamExists('ageWorkCriteriaControllerFindAgeWorksByCriteria', 'limit', limit)
            // verify required parameter 'page' is not null or undefined
            assertParamExists('ageWorkCriteriaControllerFindAgeWorksByCriteria', 'page', page)
            const localVarPath = `/age-work-criteria/{id}/age-works`
                .replace(`{${"id"}}`, encodeURIComponent(String(id)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication bearer required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)

            if (sortBy !== undefined) {
                for (const [key, value] of Object.entries(sortBy)) {
                    localVarQueryParameter[key] = value;
                }
            }

            if (order !== undefined) {
                for (const [key, value] of Object.entries(order)) {
                    localVarQueryParameter[key] = value;
                }
            }

            if (limit !== undefined) {
                for (const [key, value] of Object.entries(limit)) {
                    localVarQueryParameter[key] = value;
                }
            }

            if (page !== undefined) {
                for (const [key, value] of Object.entries(page)) {
                    localVarQueryParameter[key] = value;
                }
            }

            if (search !== undefined) {
                for (const [key, value] of Object.entries(search)) {
                    localVarQueryParameter[key] = value;
                }
            }


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary Get all age work criteria
         * @param {any} limit Number of items per page
         * @param {any} page Page number to retrieve
         * @param {any} [sortBy] Field to sort by (e.g., id, name, email)
         * @param {any} [order] Sort order (ASC or DESC)
         * @param {any} [search] Search term to filter results
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        ageWorkCriteriaControllerFindAll: async (limit: any, page: any, sortBy?: any, order?: any, search?: any, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'limit' is not null or undefined
            assertParamExists('ageWorkCriteriaControllerFindAll', 'limit', limit)
            // verify required parameter 'page' is not null or undefined
            assertParamExists('ageWorkCriteriaControllerFindAll', 'page', page)
            const localVarPath = `/age-work-criteria`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication bearer required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)

            if (sortBy !== undefined) {
                for (const [key, value] of Object.entries(sortBy)) {
                    localVarQueryParameter[key] = value;
                }
            }

            if (order !== undefined) {
                for (const [key, value] of Object.entries(order)) {
                    localVarQueryParameter[key] = value;
                }
            }

            if (limit !== undefined) {
                for (const [key, value] of Object.entries(limit)) {
                    localVarQueryParameter[key] = value;
                }
            }

            if (page !== undefined) {
                for (const [key, value] of Object.entries(page)) {
                    localVarQueryParameter[key] = value;
                }
            }

            if (search !== undefined) {
                for (const [key, value] of Object.entries(search)) {
                    localVarQueryParameter[key] = value;
                }
            }


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary Get an age work criteria by ID
         * @param {number} id Age work criteria ID
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        ageWorkCriteriaControllerFindOne: async (id: number, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            assertParamExists('ageWorkCriteriaControllerFindOne', 'id', id)
            const localVarPath = `/age-work-criteria/{id}`
                .replace(`{${"id"}}`, encodeURIComponent(String(id)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication bearer required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary Delete an age work criteria
         * @param {number} id Age work criteria ID
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        ageWorkCriteriaControllerRemove: async (id: number, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            assertParamExists('ageWorkCriteriaControllerRemove', 'id', id)
            const localVarPath = `/age-work-criteria/{id}`
                .replace(`{${"id"}}`, encodeURIComponent(String(id)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'DELETE', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication bearer required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary Update an age work criteria
         * @param {number} id Age work criteria ID
         * @param {UpdateAgeWorkCriteriaDto} updateAgeWorkCriteriaDto 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        ageWorkCriteriaControllerUpdate: async (id: number, updateAgeWorkCriteriaDto: UpdateAgeWorkCriteriaDto, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            assertParamExists('ageWorkCriteriaControllerUpdate', 'id', id)
            // verify required parameter 'updateAgeWorkCriteriaDto' is not null or undefined
            assertParamExists('ageWorkCriteriaControllerUpdate', 'updateAgeWorkCriteriaDto', updateAgeWorkCriteriaDto)
            const localVarPath = `/age-work-criteria/{id}`
                .replace(`{${"id"}}`, encodeURIComponent(String(id)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'PATCH', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication bearer required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(updateAgeWorkCriteriaDto, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * AgeWorkCriteriaApi - functional programming interface
 * @export
 */
export const AgeWorkCriteriaApiFp = function(configuration?: Configuration) {
    const localVarAxiosParamCreator = AgeWorkCriteriaApiAxiosParamCreator(configuration)
    return {
        /**
         * 
         * @summary Create a new age work criteria
         * @param {CreateAgeWorkCriteriaDto} createAgeWorkCriteriaDto 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async ageWorkCriteriaControllerCreate(createAgeWorkCriteriaDto: CreateAgeWorkCriteriaDto, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.ageWorkCriteriaControllerCreate(createAgeWorkCriteriaDto, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['AgeWorkCriteriaApi.ageWorkCriteriaControllerCreate']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @summary Get all age works by criteria ID
         * @param {number} id Age work criteria ID
         * @param {any} limit Number of items per page
         * @param {any} page Page number to retrieve
         * @param {any} [sortBy] Field to sort by (e.g., id, name, email)
         * @param {any} [order] Sort order (ASC or DESC)
         * @param {any} [search] Search term to filter results
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async ageWorkCriteriaControllerFindAgeWorksByCriteria(id: number, limit: any, page: any, sortBy?: any, order?: any, search?: any, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.ageWorkCriteriaControllerFindAgeWorksByCriteria(id, limit, page, sortBy, order, search, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['AgeWorkCriteriaApi.ageWorkCriteriaControllerFindAgeWorksByCriteria']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @summary Get all age work criteria
         * @param {any} limit Number of items per page
         * @param {any} page Page number to retrieve
         * @param {any} [sortBy] Field to sort by (e.g., id, name, email)
         * @param {any} [order] Sort order (ASC or DESC)
         * @param {any} [search] Search term to filter results
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async ageWorkCriteriaControllerFindAll(limit: any, page: any, sortBy?: any, order?: any, search?: any, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.ageWorkCriteriaControllerFindAll(limit, page, sortBy, order, search, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['AgeWorkCriteriaApi.ageWorkCriteriaControllerFindAll']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @summary Get an age work criteria by ID
         * @param {number} id Age work criteria ID
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async ageWorkCriteriaControllerFindOne(id: number, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.ageWorkCriteriaControllerFindOne(id, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['AgeWorkCriteriaApi.ageWorkCriteriaControllerFindOne']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @summary Delete an age work criteria
         * @param {number} id Age work criteria ID
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async ageWorkCriteriaControllerRemove(id: number, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.ageWorkCriteriaControllerRemove(id, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['AgeWorkCriteriaApi.ageWorkCriteriaControllerRemove']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @summary Update an age work criteria
         * @param {number} id Age work criteria ID
         * @param {UpdateAgeWorkCriteriaDto} updateAgeWorkCriteriaDto 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async ageWorkCriteriaControllerUpdate(id: number, updateAgeWorkCriteriaDto: UpdateAgeWorkCriteriaDto, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.ageWorkCriteriaControllerUpdate(id, updateAgeWorkCriteriaDto, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['AgeWorkCriteriaApi.ageWorkCriteriaControllerUpdate']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
    }
};

/**
 * AgeWorkCriteriaApi - factory interface
 * @export
 */
export const AgeWorkCriteriaApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    const localVarFp = AgeWorkCriteriaApiFp(configuration)
    return {
        /**
         * 
         * @summary Create a new age work criteria
         * @param {CreateAgeWorkCriteriaDto} createAgeWorkCriteriaDto 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        ageWorkCriteriaControllerCreate(createAgeWorkCriteriaDto: CreateAgeWorkCriteriaDto, options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.ageWorkCriteriaControllerCreate(createAgeWorkCriteriaDto, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary Get all age works by criteria ID
         * @param {number} id Age work criteria ID
         * @param {any} limit Number of items per page
         * @param {any} page Page number to retrieve
         * @param {any} [sortBy] Field to sort by (e.g., id, name, email)
         * @param {any} [order] Sort order (ASC or DESC)
         * @param {any} [search] Search term to filter results
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        ageWorkCriteriaControllerFindAgeWorksByCriteria(id: number, limit: any, page: any, sortBy?: any, order?: any, search?: any, options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.ageWorkCriteriaControllerFindAgeWorksByCriteria(id, limit, page, sortBy, order, search, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary Get all age work criteria
         * @param {any} limit Number of items per page
         * @param {any} page Page number to retrieve
         * @param {any} [sortBy] Field to sort by (e.g., id, name, email)
         * @param {any} [order] Sort order (ASC or DESC)
         * @param {any} [search] Search term to filter results
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        ageWorkCriteriaControllerFindAll(limit: any, page: any, sortBy?: any, order?: any, search?: any, options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.ageWorkCriteriaControllerFindAll(limit, page, sortBy, order, search, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary Get an age work criteria by ID
         * @param {number} id Age work criteria ID
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        ageWorkCriteriaControllerFindOne(id: number, options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.ageWorkCriteriaControllerFindOne(id, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary Delete an age work criteria
         * @param {number} id Age work criteria ID
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        ageWorkCriteriaControllerRemove(id: number, options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.ageWorkCriteriaControllerRemove(id, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary Update an age work criteria
         * @param {number} id Age work criteria ID
         * @param {UpdateAgeWorkCriteriaDto} updateAgeWorkCriteriaDto 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        ageWorkCriteriaControllerUpdate(id: number, updateAgeWorkCriteriaDto: UpdateAgeWorkCriteriaDto, options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.ageWorkCriteriaControllerUpdate(id, updateAgeWorkCriteriaDto, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * AgeWorkCriteriaApi - object-oriented interface
 * @export
 * @class AgeWorkCriteriaApi
 * @extends {BaseAPI}
 */
export class AgeWorkCriteriaApi extends BaseAPI {
    /**
     * 
     * @summary Create a new age work criteria
     * @param {CreateAgeWorkCriteriaDto} createAgeWorkCriteriaDto 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof AgeWorkCriteriaApi
     */
    public ageWorkCriteriaControllerCreate(createAgeWorkCriteriaDto: CreateAgeWorkCriteriaDto, options?: RawAxiosRequestConfig) {
        return AgeWorkCriteriaApiFp(this.configuration).ageWorkCriteriaControllerCreate(createAgeWorkCriteriaDto, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @summary Get all age works by criteria ID
     * @param {number} id Age work criteria ID
     * @param {any} limit Number of items per page
     * @param {any} page Page number to retrieve
     * @param {any} [sortBy] Field to sort by (e.g., id, name, email)
     * @param {any} [order] Sort order (ASC or DESC)
     * @param {any} [search] Search term to filter results
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof AgeWorkCriteriaApi
     */
    public ageWorkCriteriaControllerFindAgeWorksByCriteria(id: number, limit: any, page: any, sortBy?: any, order?: any, search?: any, options?: RawAxiosRequestConfig) {
        return AgeWorkCriteriaApiFp(this.configuration).ageWorkCriteriaControllerFindAgeWorksByCriteria(id, limit, page, sortBy, order, search, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @summary Get all age work criteria
     * @param {any} limit Number of items per page
     * @param {any} page Page number to retrieve
     * @param {any} [sortBy] Field to sort by (e.g., id, name, email)
     * @param {any} [order] Sort order (ASC or DESC)
     * @param {any} [search] Search term to filter results
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof AgeWorkCriteriaApi
     */
    public ageWorkCriteriaControllerFindAll(limit: any, page: any, sortBy?: any, order?: any, search?: any, options?: RawAxiosRequestConfig) {
        return AgeWorkCriteriaApiFp(this.configuration).ageWorkCriteriaControllerFindAll(limit, page, sortBy, order, search, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @summary Get an age work criteria by ID
     * @param {number} id Age work criteria ID
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof AgeWorkCriteriaApi
     */
    public ageWorkCriteriaControllerFindOne(id: number, options?: RawAxiosRequestConfig) {
        return AgeWorkCriteriaApiFp(this.configuration).ageWorkCriteriaControllerFindOne(id, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @summary Delete an age work criteria
     * @param {number} id Age work criteria ID
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof AgeWorkCriteriaApi
     */
    public ageWorkCriteriaControllerRemove(id: number, options?: RawAxiosRequestConfig) {
        return AgeWorkCriteriaApiFp(this.configuration).ageWorkCriteriaControllerRemove(id, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @summary Update an age work criteria
     * @param {number} id Age work criteria ID
     * @param {UpdateAgeWorkCriteriaDto} updateAgeWorkCriteriaDto 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof AgeWorkCriteriaApi
     */
    public ageWorkCriteriaControllerUpdate(id: number, updateAgeWorkCriteriaDto: UpdateAgeWorkCriteriaDto, options?: RawAxiosRequestConfig) {
        return AgeWorkCriteriaApiFp(this.configuration).ageWorkCriteriaControllerUpdate(id, updateAgeWorkCriteriaDto, options).then((request) => request(this.axios, this.basePath));
    }
}

