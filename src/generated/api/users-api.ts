/* tslint:disable */
/* eslint-disable */
/**
 * HRD Backend API
 * Human Resource Development Backend API Documentation
 *
 * The version of the OpenAPI document: 1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


import type { Configuration } from '../configuration';
import type { AxiosPromise, AxiosInstance, RawAxiosRequestConfig } from 'axios';
import globalAxios from 'axios';
// Some imports not used depending on template conditions
// @ts-ignore
import { DUMMY_BASE_URL, assertParamExists, setApiKeyToObject, setBasicAuthToObject, setBearerAuthToObject, setOAuthToObject, setSearchParams, serializeDataIfNeeded, toPathString, createRequestFunction } from '../common';
// @ts-ignore
import { BASE_PATH, COLLECTION_FORMATS, type RequestArgs, BaseAPI, RequiredError, operationServerMap } from '../base';
// @ts-ignore
import type { UsersControllerUpdateRequest } from '../models';
/**
 * UsersApi - axios parameter creator
 * @export
 */
export const UsersApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * 
         * @summary Create a new user
         * @param {object} body 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        usersControllerCreate: async (body: object, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'body' is not null or undefined
            assertParamExists('usersControllerCreate', 'body', body)
            const localVarPath = `/users`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication bearer required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(body, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary Get all users
         * @param {any} limit Number of items per page
         * @param {any} page Page number to retrieve
         * @param {string} [facultyFilter] Filter users by faculty ID
         * @param {string} [roleFilter] Filter users by role ID
         * @param {any} [sortBy] Field to sort by (e.g., id, name, email)
         * @param {any} [order] Sort order (ASC or DESC)
         * @param {any} [search] Search term to filter results
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        usersControllerFindAll: async (limit: any, page: any, facultyFilter?: string, roleFilter?: string, sortBy?: any, order?: any, search?: any, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'limit' is not null or undefined
            assertParamExists('usersControllerFindAll', 'limit', limit)
            // verify required parameter 'page' is not null or undefined
            assertParamExists('usersControllerFindAll', 'page', page)
            const localVarPath = `/users`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication bearer required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)

            if (facultyFilter !== undefined) {
                localVarQueryParameter['facultyFilter'] = facultyFilter;
            }

            if (roleFilter !== undefined) {
                localVarQueryParameter['roleFilter'] = roleFilter;
            }

            if (sortBy !== undefined) {
                for (const [key, value] of Object.entries(sortBy)) {
                    localVarQueryParameter[key] = value;
                }
            }

            if (order !== undefined) {
                for (const [key, value] of Object.entries(order)) {
                    localVarQueryParameter[key] = value;
                }
            }

            if (limit !== undefined) {
                for (const [key, value] of Object.entries(limit)) {
                    localVarQueryParameter[key] = value;
                }
            }

            if (page !== undefined) {
                for (const [key, value] of Object.entries(page)) {
                    localVarQueryParameter[key] = value;
                }
            }

            if (search !== undefined) {
                for (const [key, value] of Object.entries(search)) {
                    localVarQueryParameter[key] = value;
                }
            }


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary Get a user by ID
         * @param {string} id User ID
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        usersControllerFindOne: async (id: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            assertParamExists('usersControllerFindOne', 'id', id)
            const localVarPath = `/users/{id}`
                .replace(`{${"id"}}`, encodeURIComponent(String(id)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication bearer required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary Get all faculties of User
         * @param {string} id User ID
         * @param {any} limit Number of items per page
         * @param {any} page Page number to retrieve
         * @param {any} [sortBy] Field to sort by (e.g., id, name, email)
         * @param {any} [order] Sort order (ASC or DESC)
         * @param {any} [search] Search term to filter results
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        usersControllerGetFacultiesUser: async (id: string, limit: any, page: any, sortBy?: any, order?: any, search?: any, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            assertParamExists('usersControllerGetFacultiesUser', 'id', id)
            // verify required parameter 'limit' is not null or undefined
            assertParamExists('usersControllerGetFacultiesUser', 'limit', limit)
            // verify required parameter 'page' is not null or undefined
            assertParamExists('usersControllerGetFacultiesUser', 'page', page)
            const localVarPath = `/users/{id}/facultiesUser`
                .replace(`{${"id"}}`, encodeURIComponent(String(id)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication bearer required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)

            if (sortBy !== undefined) {
                for (const [key, value] of Object.entries(sortBy)) {
                    localVarQueryParameter[key] = value;
                }
            }

            if (order !== undefined) {
                for (const [key, value] of Object.entries(order)) {
                    localVarQueryParameter[key] = value;
                }
            }

            if (limit !== undefined) {
                for (const [key, value] of Object.entries(limit)) {
                    localVarQueryParameter[key] = value;
                }
            }

            if (page !== undefined) {
                for (const [key, value] of Object.entries(page)) {
                    localVarQueryParameter[key] = value;
                }
            }

            if (search !== undefined) {
                for (const [key, value] of Object.entries(search)) {
                    localVarQueryParameter[key] = value;
                }
            }


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary Get all faculties with user status for dialog
         * @param {string} id User ID
         * @param {any} limit Number of items per page
         * @param {any} page Page number to retrieve
         * @param {any} [sortBy] Field to sort by (e.g., id, name, email)
         * @param {any} [order] Sort order (ASC or DESC)
         * @param {any} [search] Search term to filter results
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        usersControllerGetFacultiesWithUserStatus: async (id: string, limit: any, page: any, sortBy?: any, order?: any, search?: any, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            assertParamExists('usersControllerGetFacultiesWithUserStatus', 'id', id)
            // verify required parameter 'limit' is not null or undefined
            assertParamExists('usersControllerGetFacultiesWithUserStatus', 'limit', limit)
            // verify required parameter 'page' is not null or undefined
            assertParamExists('usersControllerGetFacultiesWithUserStatus', 'page', page)
            const localVarPath = `/users/{id}/faculties`
                .replace(`{${"id"}}`, encodeURIComponent(String(id)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication bearer required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)

            if (sortBy !== undefined) {
                for (const [key, value] of Object.entries(sortBy)) {
                    localVarQueryParameter[key] = value;
                }
            }

            if (order !== undefined) {
                for (const [key, value] of Object.entries(order)) {
                    localVarQueryParameter[key] = value;
                }
            }

            if (limit !== undefined) {
                for (const [key, value] of Object.entries(limit)) {
                    localVarQueryParameter[key] = value;
                }
            }

            if (page !== undefined) {
                for (const [key, value] of Object.entries(page)) {
                    localVarQueryParameter[key] = value;
                }
            }

            if (search !== undefined) {
                for (const [key, value] of Object.entries(search)) {
                    localVarQueryParameter[key] = value;
                }
            }


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary Delete a user
         * @param {string} id User ID
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        usersControllerRemove: async (id: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            assertParamExists('usersControllerRemove', 'id', id)
            const localVarPath = `/users/{id}`
                .replace(`{${"id"}}`, encodeURIComponent(String(id)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'DELETE', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication bearer required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary Update user information, password, or roles
         * @param {string} id User ID
         * @param {UsersControllerUpdateRequest} usersControllerUpdateRequest 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        usersControllerUpdate: async (id: string, usersControllerUpdateRequest: UsersControllerUpdateRequest, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            assertParamExists('usersControllerUpdate', 'id', id)
            // verify required parameter 'usersControllerUpdateRequest' is not null or undefined
            assertParamExists('usersControllerUpdate', 'usersControllerUpdateRequest', usersControllerUpdateRequest)
            const localVarPath = `/users/{id}`
                .replace(`{${"id"}}`, encodeURIComponent(String(id)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'PATCH', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication bearer required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(usersControllerUpdateRequest, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary Update user faculties based on checkbox selection
         * @param {string} id User ID
         * @param {object} body 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        usersControllerUpdateUserFaculties: async (id: string, body: object, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            assertParamExists('usersControllerUpdateUserFaculties', 'id', id)
            // verify required parameter 'body' is not null or undefined
            assertParamExists('usersControllerUpdateUserFaculties', 'body', body)
            const localVarPath = `/users/{id}/faculties`
                .replace(`{${"id"}}`, encodeURIComponent(String(id)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'PATCH', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication bearer required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(body, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary Update user roles based on checkbox selection
         * @param {string} id User ID
         * @param {object} body 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        usersControllerUpdateUserFacultiesRoles: async (id: string, body: object, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            assertParamExists('usersControllerUpdateUserFacultiesRoles', 'id', id)
            // verify required parameter 'body' is not null or undefined
            assertParamExists('usersControllerUpdateUserFacultiesRoles', 'body', body)
            const localVarPath = `/users/{id}/roles`
                .replace(`{${"id"}}`, encodeURIComponent(String(id)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'PATCH', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication bearer required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(body, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * UsersApi - functional programming interface
 * @export
 */
export const UsersApiFp = function(configuration?: Configuration) {
    const localVarAxiosParamCreator = UsersApiAxiosParamCreator(configuration)
    return {
        /**
         * 
         * @summary Create a new user
         * @param {object} body 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async usersControllerCreate(body: object, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.usersControllerCreate(body, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['UsersApi.usersControllerCreate']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @summary Get all users
         * @param {any} limit Number of items per page
         * @param {any} page Page number to retrieve
         * @param {string} [facultyFilter] Filter users by faculty ID
         * @param {string} [roleFilter] Filter users by role ID
         * @param {any} [sortBy] Field to sort by (e.g., id, name, email)
         * @param {any} [order] Sort order (ASC or DESC)
         * @param {any} [search] Search term to filter results
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async usersControllerFindAll(limit: any, page: any, facultyFilter?: string, roleFilter?: string, sortBy?: any, order?: any, search?: any, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.usersControllerFindAll(limit, page, facultyFilter, roleFilter, sortBy, order, search, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['UsersApi.usersControllerFindAll']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @summary Get a user by ID
         * @param {string} id User ID
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async usersControllerFindOne(id: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.usersControllerFindOne(id, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['UsersApi.usersControllerFindOne']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @summary Get all faculties of User
         * @param {string} id User ID
         * @param {any} limit Number of items per page
         * @param {any} page Page number to retrieve
         * @param {any} [sortBy] Field to sort by (e.g., id, name, email)
         * @param {any} [order] Sort order (ASC or DESC)
         * @param {any} [search] Search term to filter results
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async usersControllerGetFacultiesUser(id: string, limit: any, page: any, sortBy?: any, order?: any, search?: any, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.usersControllerGetFacultiesUser(id, limit, page, sortBy, order, search, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['UsersApi.usersControllerGetFacultiesUser']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @summary Get all faculties with user status for dialog
         * @param {string} id User ID
         * @param {any} limit Number of items per page
         * @param {any} page Page number to retrieve
         * @param {any} [sortBy] Field to sort by (e.g., id, name, email)
         * @param {any} [order] Sort order (ASC or DESC)
         * @param {any} [search] Search term to filter results
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async usersControllerGetFacultiesWithUserStatus(id: string, limit: any, page: any, sortBy?: any, order?: any, search?: any, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.usersControllerGetFacultiesWithUserStatus(id, limit, page, sortBy, order, search, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['UsersApi.usersControllerGetFacultiesWithUserStatus']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @summary Delete a user
         * @param {string} id User ID
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async usersControllerRemove(id: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.usersControllerRemove(id, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['UsersApi.usersControllerRemove']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @summary Update user information, password, or roles
         * @param {string} id User ID
         * @param {UsersControllerUpdateRequest} usersControllerUpdateRequest 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async usersControllerUpdate(id: string, usersControllerUpdateRequest: UsersControllerUpdateRequest, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.usersControllerUpdate(id, usersControllerUpdateRequest, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['UsersApi.usersControllerUpdate']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @summary Update user faculties based on checkbox selection
         * @param {string} id User ID
         * @param {object} body 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async usersControllerUpdateUserFaculties(id: string, body: object, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.usersControllerUpdateUserFaculties(id, body, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['UsersApi.usersControllerUpdateUserFaculties']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @summary Update user roles based on checkbox selection
         * @param {string} id User ID
         * @param {object} body 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async usersControllerUpdateUserFacultiesRoles(id: string, body: object, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.usersControllerUpdateUserFacultiesRoles(id, body, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['UsersApi.usersControllerUpdateUserFacultiesRoles']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
    }
};

/**
 * UsersApi - factory interface
 * @export
 */
export const UsersApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    const localVarFp = UsersApiFp(configuration)
    return {
        /**
         * 
         * @summary Create a new user
         * @param {object} body 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        usersControllerCreate(body: object, options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.usersControllerCreate(body, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary Get all users
         * @param {any} limit Number of items per page
         * @param {any} page Page number to retrieve
         * @param {string} [facultyFilter] Filter users by faculty ID
         * @param {string} [roleFilter] Filter users by role ID
         * @param {any} [sortBy] Field to sort by (e.g., id, name, email)
         * @param {any} [order] Sort order (ASC or DESC)
         * @param {any} [search] Search term to filter results
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        usersControllerFindAll(limit: any, page: any, facultyFilter?: string, roleFilter?: string, sortBy?: any, order?: any, search?: any, options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.usersControllerFindAll(limit, page, facultyFilter, roleFilter, sortBy, order, search, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary Get a user by ID
         * @param {string} id User ID
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        usersControllerFindOne(id: string, options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.usersControllerFindOne(id, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary Get all faculties of User
         * @param {string} id User ID
         * @param {any} limit Number of items per page
         * @param {any} page Page number to retrieve
         * @param {any} [sortBy] Field to sort by (e.g., id, name, email)
         * @param {any} [order] Sort order (ASC or DESC)
         * @param {any} [search] Search term to filter results
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        usersControllerGetFacultiesUser(id: string, limit: any, page: any, sortBy?: any, order?: any, search?: any, options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.usersControllerGetFacultiesUser(id, limit, page, sortBy, order, search, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary Get all faculties with user status for dialog
         * @param {string} id User ID
         * @param {any} limit Number of items per page
         * @param {any} page Page number to retrieve
         * @param {any} [sortBy] Field to sort by (e.g., id, name, email)
         * @param {any} [order] Sort order (ASC or DESC)
         * @param {any} [search] Search term to filter results
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        usersControllerGetFacultiesWithUserStatus(id: string, limit: any, page: any, sortBy?: any, order?: any, search?: any, options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.usersControllerGetFacultiesWithUserStatus(id, limit, page, sortBy, order, search, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary Delete a user
         * @param {string} id User ID
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        usersControllerRemove(id: string, options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.usersControllerRemove(id, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary Update user information, password, or roles
         * @param {string} id User ID
         * @param {UsersControllerUpdateRequest} usersControllerUpdateRequest 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        usersControllerUpdate(id: string, usersControllerUpdateRequest: UsersControllerUpdateRequest, options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.usersControllerUpdate(id, usersControllerUpdateRequest, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary Update user faculties based on checkbox selection
         * @param {string} id User ID
         * @param {object} body 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        usersControllerUpdateUserFaculties(id: string, body: object, options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.usersControllerUpdateUserFaculties(id, body, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary Update user roles based on checkbox selection
         * @param {string} id User ID
         * @param {object} body 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        usersControllerUpdateUserFacultiesRoles(id: string, body: object, options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.usersControllerUpdateUserFacultiesRoles(id, body, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * UsersApi - object-oriented interface
 * @export
 * @class UsersApi
 * @extends {BaseAPI}
 */
export class UsersApi extends BaseAPI {
    /**
     * 
     * @summary Create a new user
     * @param {object} body 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof UsersApi
     */
    public usersControllerCreate(body: object, options?: RawAxiosRequestConfig) {
        return UsersApiFp(this.configuration).usersControllerCreate(body, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @summary Get all users
     * @param {any} limit Number of items per page
     * @param {any} page Page number to retrieve
     * @param {string} [facultyFilter] Filter users by faculty ID
     * @param {string} [roleFilter] Filter users by role ID
     * @param {any} [sortBy] Field to sort by (e.g., id, name, email)
     * @param {any} [order] Sort order (ASC or DESC)
     * @param {any} [search] Search term to filter results
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof UsersApi
     */
    public usersControllerFindAll(limit: any, page: any, facultyFilter?: string, roleFilter?: string, sortBy?: any, order?: any, search?: any, options?: RawAxiosRequestConfig) {
        return UsersApiFp(this.configuration).usersControllerFindAll(limit, page, facultyFilter, roleFilter, sortBy, order, search, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @summary Get a user by ID
     * @param {string} id User ID
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof UsersApi
     */
    public usersControllerFindOne(id: string, options?: RawAxiosRequestConfig) {
        return UsersApiFp(this.configuration).usersControllerFindOne(id, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @summary Get all faculties of User
     * @param {string} id User ID
     * @param {any} limit Number of items per page
     * @param {any} page Page number to retrieve
     * @param {any} [sortBy] Field to sort by (e.g., id, name, email)
     * @param {any} [order] Sort order (ASC or DESC)
     * @param {any} [search] Search term to filter results
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof UsersApi
     */
    public usersControllerGetFacultiesUser(id: string, limit: any, page: any, sortBy?: any, order?: any, search?: any, options?: RawAxiosRequestConfig) {
        return UsersApiFp(this.configuration).usersControllerGetFacultiesUser(id, limit, page, sortBy, order, search, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @summary Get all faculties with user status for dialog
     * @param {string} id User ID
     * @param {any} limit Number of items per page
     * @param {any} page Page number to retrieve
     * @param {any} [sortBy] Field to sort by (e.g., id, name, email)
     * @param {any} [order] Sort order (ASC or DESC)
     * @param {any} [search] Search term to filter results
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof UsersApi
     */
    public usersControllerGetFacultiesWithUserStatus(id: string, limit: any, page: any, sortBy?: any, order?: any, search?: any, options?: RawAxiosRequestConfig) {
        return UsersApiFp(this.configuration).usersControllerGetFacultiesWithUserStatus(id, limit, page, sortBy, order, search, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @summary Delete a user
     * @param {string} id User ID
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof UsersApi
     */
    public usersControllerRemove(id: string, options?: RawAxiosRequestConfig) {
        return UsersApiFp(this.configuration).usersControllerRemove(id, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @summary Update user information, password, or roles
     * @param {string} id User ID
     * @param {UsersControllerUpdateRequest} usersControllerUpdateRequest 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof UsersApi
     */
    public usersControllerUpdate(id: string, usersControllerUpdateRequest: UsersControllerUpdateRequest, options?: RawAxiosRequestConfig) {
        return UsersApiFp(this.configuration).usersControllerUpdate(id, usersControllerUpdateRequest, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @summary Update user faculties based on checkbox selection
     * @param {string} id User ID
     * @param {object} body 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof UsersApi
     */
    public usersControllerUpdateUserFaculties(id: string, body: object, options?: RawAxiosRequestConfig) {
        return UsersApiFp(this.configuration).usersControllerUpdateUserFaculties(id, body, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @summary Update user roles based on checkbox selection
     * @param {string} id User ID
     * @param {object} body 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof UsersApi
     */
    public usersControllerUpdateUserFacultiesRoles(id: string, body: object, options?: RawAxiosRequestConfig) {
        return UsersApiFp(this.configuration).usersControllerUpdateUserFacultiesRoles(id, body, options).then((request) => request(this.axios, this.basePath));
    }
}

