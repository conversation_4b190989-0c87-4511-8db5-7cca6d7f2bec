/* tslint:disable */
/* eslint-disable */
/**
 * HRD Backend API
 * Human Resource Development Backend API Documentation
 *
 * The version of the OpenAPI document: 1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


import type { Configuration } from '../configuration';
import type { AxiosPromise, AxiosInstance, RawAxiosRequestConfig } from 'axios';
import globalAxios from 'axios';
// Some imports not used depending on template conditions
// @ts-ignore
import { DUMMY_BASE_URL, assertParamExists, setApiKeyToObject, setBasicAuthToObject, setBearerAuthToObject, setOAuthToObject, setSearchParams, serializeDataIfNeeded, toPathString, createRequestFunction } from '../common';
// @ts-ignore
import { BASE_PATH, COLLECTION_FORMATS, type RequestArgs, BaseAPI, RequiredError, operationServerMap } from '../base';
/**
 * CreateQuestionApi - axios parameter creator
 * @export
 */
export const CreateQuestionApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * สร้างคำถาม (Evaluate) ใหม่ตาม template ที่กำหนด
         * @summary สร้างคำถามใหม่
         * @param {string} questionText The text content of the question
         * @param {number} sequence The order/position of this question in the assessment
         * @param {File} [imagePath] Image file for the question
         * @param {boolean} [isHeader] Whether this question serves as a header/section title
         * @param {number} [sizeLimit] Maximum file size limit in bytes for file upload questions
         * @param {string} [acceptFile] Accepted file types for file upload questions (comma-separated MIME types or extensions)
         * @param {number} [uploadLimit] Maximum number of files that can be uploaded for this question
         * @param {number} [itemBlockId] ID of the item block this question belongs to
         * @param {number} [score] Score for the question
         * @param {number} [imageWidth] Width of the image in pixels
         * @param {number} [imageHeight] Height of the image in pixels
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        questionsControllerCreate: async (questionText: string, sequence: number, imagePath?: File, isHeader?: boolean, sizeLimit?: number, acceptFile?: string, uploadLimit?: number, itemBlockId?: number, score?: number, imageWidth?: number, imageHeight?: number, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'questionText' is not null or undefined
            assertParamExists('questionsControllerCreate', 'questionText', questionText)
            // verify required parameter 'sequence' is not null or undefined
            assertParamExists('questionsControllerCreate', 'sequence', sequence)
            const localVarPath = `/questions`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;
            const localVarFormParams = new ((configuration && configuration.formDataCtor) || FormData)();

            // authentication bearer required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)


            if (questionText !== undefined) { 
                localVarFormParams.append('questionText', questionText as any);
            }
    
            if (imagePath !== undefined) { 
                localVarFormParams.append('imagePath', imagePath as any);
            }
    
            if (isHeader !== undefined) { 
                localVarFormParams.append('isHeader', String(isHeader) as any);
            }
    
            if (sequence !== undefined) { 
                localVarFormParams.append('sequence', sequence as any);
            }
    
            if (sizeLimit !== undefined) { 
                localVarFormParams.append('sizeLimit', sizeLimit as any);
            }
    
            if (acceptFile !== undefined) { 
                localVarFormParams.append('acceptFile', acceptFile as any);
            }
    
            if (uploadLimit !== undefined) { 
                localVarFormParams.append('uploadLimit', uploadLimit as any);
            }
    
            if (itemBlockId !== undefined) { 
                localVarFormParams.append('itemBlockId', itemBlockId as any);
            }
    
            if (score !== undefined) { 
                localVarFormParams.append('score', score as any);
            }
    
            if (imageWidth !== undefined) { 
                localVarFormParams.append('imageWidth', imageWidth as any);
            }
    
            if (imageHeight !== undefined) { 
                localVarFormParams.append('imageHeight', imageHeight as any);
            }
    
    
            localVarHeaderParameter['Content-Type'] = 'multipart/form-data';
    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = localVarFormParams;

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * CreateQuestionApi - functional programming interface
 * @export
 */
export const CreateQuestionApiFp = function(configuration?: Configuration) {
    const localVarAxiosParamCreator = CreateQuestionApiAxiosParamCreator(configuration)
    return {
        /**
         * สร้างคำถาม (Evaluate) ใหม่ตาม template ที่กำหนด
         * @summary สร้างคำถามใหม่
         * @param {string} questionText The text content of the question
         * @param {number} sequence The order/position of this question in the assessment
         * @param {File} [imagePath] Image file for the question
         * @param {boolean} [isHeader] Whether this question serves as a header/section title
         * @param {number} [sizeLimit] Maximum file size limit in bytes for file upload questions
         * @param {string} [acceptFile] Accepted file types for file upload questions (comma-separated MIME types or extensions)
         * @param {number} [uploadLimit] Maximum number of files that can be uploaded for this question
         * @param {number} [itemBlockId] ID of the item block this question belongs to
         * @param {number} [score] Score for the question
         * @param {number} [imageWidth] Width of the image in pixels
         * @param {number} [imageHeight] Height of the image in pixels
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async questionsControllerCreate(questionText: string, sequence: number, imagePath?: File, isHeader?: boolean, sizeLimit?: number, acceptFile?: string, uploadLimit?: number, itemBlockId?: number, score?: number, imageWidth?: number, imageHeight?: number, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.questionsControllerCreate(questionText, sequence, imagePath, isHeader, sizeLimit, acceptFile, uploadLimit, itemBlockId, score, imageWidth, imageHeight, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['CreateQuestionApi.questionsControllerCreate']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
    }
};

/**
 * CreateQuestionApi - factory interface
 * @export
 */
export const CreateQuestionApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    const localVarFp = CreateQuestionApiFp(configuration)
    return {
        /**
         * สร้างคำถาม (Evaluate) ใหม่ตาม template ที่กำหนด
         * @summary สร้างคำถามใหม่
         * @param {string} questionText The text content of the question
         * @param {number} sequence The order/position of this question in the assessment
         * @param {File} [imagePath] Image file for the question
         * @param {boolean} [isHeader] Whether this question serves as a header/section title
         * @param {number} [sizeLimit] Maximum file size limit in bytes for file upload questions
         * @param {string} [acceptFile] Accepted file types for file upload questions (comma-separated MIME types or extensions)
         * @param {number} [uploadLimit] Maximum number of files that can be uploaded for this question
         * @param {number} [itemBlockId] ID of the item block this question belongs to
         * @param {number} [score] Score for the question
         * @param {number} [imageWidth] Width of the image in pixels
         * @param {number} [imageHeight] Height of the image in pixels
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        questionsControllerCreate(questionText: string, sequence: number, imagePath?: File, isHeader?: boolean, sizeLimit?: number, acceptFile?: string, uploadLimit?: number, itemBlockId?: number, score?: number, imageWidth?: number, imageHeight?: number, options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.questionsControllerCreate(questionText, sequence, imagePath, isHeader, sizeLimit, acceptFile, uploadLimit, itemBlockId, score, imageWidth, imageHeight, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * CreateQuestionApi - object-oriented interface
 * @export
 * @class CreateQuestionApi
 * @extends {BaseAPI}
 */
export class CreateQuestionApi extends BaseAPI {
    /**
     * สร้างคำถาม (Evaluate) ใหม่ตาม template ที่กำหนด
     * @summary สร้างคำถามใหม่
     * @param {string} questionText The text content of the question
     * @param {number} sequence The order/position of this question in the assessment
     * @param {File} [imagePath] Image file for the question
     * @param {boolean} [isHeader] Whether this question serves as a header/section title
     * @param {number} [sizeLimit] Maximum file size limit in bytes for file upload questions
     * @param {string} [acceptFile] Accepted file types for file upload questions (comma-separated MIME types or extensions)
     * @param {number} [uploadLimit] Maximum number of files that can be uploaded for this question
     * @param {number} [itemBlockId] ID of the item block this question belongs to
     * @param {number} [score] Score for the question
     * @param {number} [imageWidth] Width of the image in pixels
     * @param {number} [imageHeight] Height of the image in pixels
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof CreateQuestionApi
     */
    public questionsControllerCreate(questionText: string, sequence: number, imagePath?: File, isHeader?: boolean, sizeLimit?: number, acceptFile?: string, uploadLimit?: number, itemBlockId?: number, score?: number, imageWidth?: number, imageHeight?: number, options?: RawAxiosRequestConfig) {
        return CreateQuestionApiFp(this.configuration).questionsControllerCreate(questionText, sequence, imagePath, isHeader, sizeLimit, acceptFile, uploadLimit, itemBlockId, score, imageWidth, imageHeight, options).then((request) => request(this.axios, this.basePath));
    }
}

