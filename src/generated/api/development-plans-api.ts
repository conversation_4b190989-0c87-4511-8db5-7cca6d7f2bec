/* tslint:disable */
/* eslint-disable */
/**
 * HRD Backend API
 * Human Resource Development Backend API Documentation
 *
 * The version of the OpenAPI document: 1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


import type { Configuration } from '../configuration';
import type { AxiosPromise, AxiosInstance, RawAxiosRequestConfig } from 'axios';
import globalAxios from 'axios';
// Some imports not used depending on template conditions
// @ts-ignore
import { DUMMY_BASE_URL, assertParamExists, setApiKeyToObject, setBasicAuthToObject, setBearerAuthToObject, setOAuthToObject, setSearchParams, serializeDataIfNeeded, toPathString, createRequestFunction } from '../common';
// @ts-ignore
import { BASE_PATH, COLLECTION_FORMATS, type RequestArgs, BaseAPI, RequiredError, operationServerMap } from '../base';
// @ts-ignore
import type { CreateDevelopmentPlanDto } from '../models';
// @ts-ignore
import type { DevelopmentPlanResponseDto } from '../models';
// @ts-ignore
import type { PersonalPlansResponseDto } from '../models';
// @ts-ignore
import type { UpdateDevelopmentPlanDto } from '../models';
/**
 * DevelopmentPlansApi - axios parameter creator
 * @export
 */
export const DevelopmentPlansApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * 
         * @summary Create a new development plan
         * @param {CreateDevelopmentPlanDto} createDevelopmentPlanDto 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        developmentPlansControllerCreate: async (createDevelopmentPlanDto: CreateDevelopmentPlanDto, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'createDevelopmentPlanDto' is not null or undefined
            assertParamExists('developmentPlansControllerCreate', 'createDevelopmentPlanDto', createDevelopmentPlanDto)
            const localVarPath = `/development-plans`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication bearer required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(createDevelopmentPlanDto, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary Get all development plans with pagination
         * @param {number} [limit] Number of items per page
         * @param {number} [page] Page number
         * @param {string} [sortBy] Field to sort by
         * @param {DevelopmentPlansControllerFindAllOrderEnum} [order] Sort order
         * @param {string} [careerType] Filter by career type
         * @param {string} [search] Search term for name or description
         * @param {string} [isCentral] Filter by isCentral
         * @param {number} [parentId] Filter by parent ID
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        developmentPlansControllerFindAll: async (limit?: number, page?: number, sortBy?: string, order?: DevelopmentPlansControllerFindAllOrderEnum, careerType?: string, search?: string, isCentral?: string, parentId?: number, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/development-plans`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication bearer required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)

            if (limit !== undefined) {
                localVarQueryParameter['limit'] = limit;
            }

            if (page !== undefined) {
                localVarQueryParameter['page'] = page;
            }

            if (sortBy !== undefined) {
                localVarQueryParameter['sortBy'] = sortBy;
            }

            if (order !== undefined) {
                localVarQueryParameter['order'] = order;
            }

            if (careerType !== undefined) {
                localVarQueryParameter['career_type'] = careerType;
            }

            if (search !== undefined) {
                localVarQueryParameter['search'] = search;
            }

            if (isCentral !== undefined) {
                localVarQueryParameter['isCentral'] = isCentral;
            }

            if (parentId !== undefined) {
                localVarQueryParameter['parentId'] = parentId;
            }


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary Get development plan by ID
         * @param {string} id 
         * @param {boolean} isCentral 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        developmentPlansControllerFindOne: async (id: string, isCentral: boolean, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            assertParamExists('developmentPlansControllerFindOne', 'id', id)
            // verify required parameter 'isCentral' is not null or undefined
            assertParamExists('developmentPlansControllerFindOne', 'isCentral', isCentral)
            const localVarPath = `/development-plans/{id}`
                .replace(`{${"id"}}`, encodeURIComponent(String(id)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication bearer required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)

            if (isCentral !== undefined) {
                localVarQueryParameter['isCentral'] = isCentral;
            }


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * Retrieve personal development plans with pagination and filtering by career, faculty, and role
         * @summary Get personal development plans with filters
         * @param {number} [page] Page number
         * @param {number} [limit] Number of items per page
         * @param {string} [search] Search term
         * @param {string} [sortBy] Sort by field
         * @param {DevelopmentPlansControllerGetPersonalPlansOrderEnum} [order] Sort order
         * @param {number} [careerFilter] Filter by career ID
         * @param {number} [facultyFilter] Filter by faculty ID
         * @param {number} [roleFilter] Filter by role ID
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        developmentPlansControllerGetPersonalPlans: async (page?: number, limit?: number, search?: string, sortBy?: string, order?: DevelopmentPlansControllerGetPersonalPlansOrderEnum, careerFilter?: number, facultyFilter?: number, roleFilter?: number, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/development-plans/personal-plans`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication bearer required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)

            if (page !== undefined) {
                localVarQueryParameter['page'] = page;
            }

            if (limit !== undefined) {
                localVarQueryParameter['limit'] = limit;
            }

            if (search !== undefined) {
                localVarQueryParameter['search'] = search;
            }

            if (sortBy !== undefined) {
                localVarQueryParameter['sortBy'] = sortBy;
            }

            if (order !== undefined) {
                localVarQueryParameter['order'] = order;
            }

            if (careerFilter !== undefined) {
                localVarQueryParameter['careerFilter'] = careerFilter;
            }

            if (facultyFilter !== undefined) {
                localVarQueryParameter['facultyFilter'] = facultyFilter;
            }

            if (roleFilter !== undefined) {
                localVarQueryParameter['roleFilter'] = roleFilter;
            }


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary Delete a development plan
         * @param {string} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        developmentPlansControllerRemove: async (id: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            assertParamExists('developmentPlansControllerRemove', 'id', id)
            const localVarPath = `/development-plans/{id}`
                .replace(`{${"id"}}`, encodeURIComponent(String(id)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'DELETE', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication bearer required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary Update a development plan
         * @param {string} id 
         * @param {UpdateDevelopmentPlanDto} updateDevelopmentPlanDto 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        developmentPlansControllerUpdate: async (id: string, updateDevelopmentPlanDto: UpdateDevelopmentPlanDto, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            assertParamExists('developmentPlansControllerUpdate', 'id', id)
            // verify required parameter 'updateDevelopmentPlanDto' is not null or undefined
            assertParamExists('developmentPlansControllerUpdate', 'updateDevelopmentPlanDto', updateDevelopmentPlanDto)
            const localVarPath = `/development-plans/{id}`
                .replace(`{${"id"}}`, encodeURIComponent(String(id)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'PATCH', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication bearer required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(updateDevelopmentPlanDto, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * DevelopmentPlansApi - functional programming interface
 * @export
 */
export const DevelopmentPlansApiFp = function(configuration?: Configuration) {
    const localVarAxiosParamCreator = DevelopmentPlansApiAxiosParamCreator(configuration)
    return {
        /**
         * 
         * @summary Create a new development plan
         * @param {CreateDevelopmentPlanDto} createDevelopmentPlanDto 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async developmentPlansControllerCreate(createDevelopmentPlanDto: CreateDevelopmentPlanDto, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.developmentPlansControllerCreate(createDevelopmentPlanDto, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['DevelopmentPlansApi.developmentPlansControllerCreate']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @summary Get all development plans with pagination
         * @param {number} [limit] Number of items per page
         * @param {number} [page] Page number
         * @param {string} [sortBy] Field to sort by
         * @param {DevelopmentPlansControllerFindAllOrderEnum} [order] Sort order
         * @param {string} [careerType] Filter by career type
         * @param {string} [search] Search term for name or description
         * @param {string} [isCentral] Filter by isCentral
         * @param {number} [parentId] Filter by parent ID
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async developmentPlansControllerFindAll(limit?: number, page?: number, sortBy?: string, order?: DevelopmentPlansControllerFindAllOrderEnum, careerType?: string, search?: string, isCentral?: string, parentId?: number, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<DevelopmentPlanResponseDto>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.developmentPlansControllerFindAll(limit, page, sortBy, order, careerType, search, isCentral, parentId, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['DevelopmentPlansApi.developmentPlansControllerFindAll']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @summary Get development plan by ID
         * @param {string} id 
         * @param {boolean} isCentral 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async developmentPlansControllerFindOne(id: string, isCentral: boolean, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.developmentPlansControllerFindOne(id, isCentral, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['DevelopmentPlansApi.developmentPlansControllerFindOne']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * Retrieve personal development plans with pagination and filtering by career, faculty, and role
         * @summary Get personal development plans with filters
         * @param {number} [page] Page number
         * @param {number} [limit] Number of items per page
         * @param {string} [search] Search term
         * @param {string} [sortBy] Sort by field
         * @param {DevelopmentPlansControllerGetPersonalPlansOrderEnum} [order] Sort order
         * @param {number} [careerFilter] Filter by career ID
         * @param {number} [facultyFilter] Filter by faculty ID
         * @param {number} [roleFilter] Filter by role ID
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async developmentPlansControllerGetPersonalPlans(page?: number, limit?: number, search?: string, sortBy?: string, order?: DevelopmentPlansControllerGetPersonalPlansOrderEnum, careerFilter?: number, facultyFilter?: number, roleFilter?: number, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<PersonalPlansResponseDto>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.developmentPlansControllerGetPersonalPlans(page, limit, search, sortBy, order, careerFilter, facultyFilter, roleFilter, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['DevelopmentPlansApi.developmentPlansControllerGetPersonalPlans']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @summary Delete a development plan
         * @param {string} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async developmentPlansControllerRemove(id: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.developmentPlansControllerRemove(id, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['DevelopmentPlansApi.developmentPlansControllerRemove']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @summary Update a development plan
         * @param {string} id 
         * @param {UpdateDevelopmentPlanDto} updateDevelopmentPlanDto 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async developmentPlansControllerUpdate(id: string, updateDevelopmentPlanDto: UpdateDevelopmentPlanDto, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.developmentPlansControllerUpdate(id, updateDevelopmentPlanDto, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['DevelopmentPlansApi.developmentPlansControllerUpdate']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
    }
};

/**
 * DevelopmentPlansApi - factory interface
 * @export
 */
export const DevelopmentPlansApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    const localVarFp = DevelopmentPlansApiFp(configuration)
    return {
        /**
         * 
         * @summary Create a new development plan
         * @param {CreateDevelopmentPlanDto} createDevelopmentPlanDto 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        developmentPlansControllerCreate(createDevelopmentPlanDto: CreateDevelopmentPlanDto, options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.developmentPlansControllerCreate(createDevelopmentPlanDto, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary Get all development plans with pagination
         * @param {number} [limit] Number of items per page
         * @param {number} [page] Page number
         * @param {string} [sortBy] Field to sort by
         * @param {DevelopmentPlansControllerFindAllOrderEnum} [order] Sort order
         * @param {string} [careerType] Filter by career type
         * @param {string} [search] Search term for name or description
         * @param {string} [isCentral] Filter by isCentral
         * @param {number} [parentId] Filter by parent ID
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        developmentPlansControllerFindAll(limit?: number, page?: number, sortBy?: string, order?: DevelopmentPlansControllerFindAllOrderEnum, careerType?: string, search?: string, isCentral?: string, parentId?: number, options?: RawAxiosRequestConfig): AxiosPromise<DevelopmentPlanResponseDto> {
            return localVarFp.developmentPlansControllerFindAll(limit, page, sortBy, order, careerType, search, isCentral, parentId, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary Get development plan by ID
         * @param {string} id 
         * @param {boolean} isCentral 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        developmentPlansControllerFindOne(id: string, isCentral: boolean, options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.developmentPlansControllerFindOne(id, isCentral, options).then((request) => request(axios, basePath));
        },
        /**
         * Retrieve personal development plans with pagination and filtering by career, faculty, and role
         * @summary Get personal development plans with filters
         * @param {number} [page] Page number
         * @param {number} [limit] Number of items per page
         * @param {string} [search] Search term
         * @param {string} [sortBy] Sort by field
         * @param {DevelopmentPlansControllerGetPersonalPlansOrderEnum} [order] Sort order
         * @param {number} [careerFilter] Filter by career ID
         * @param {number} [facultyFilter] Filter by faculty ID
         * @param {number} [roleFilter] Filter by role ID
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        developmentPlansControllerGetPersonalPlans(page?: number, limit?: number, search?: string, sortBy?: string, order?: DevelopmentPlansControllerGetPersonalPlansOrderEnum, careerFilter?: number, facultyFilter?: number, roleFilter?: number, options?: RawAxiosRequestConfig): AxiosPromise<PersonalPlansResponseDto> {
            return localVarFp.developmentPlansControllerGetPersonalPlans(page, limit, search, sortBy, order, careerFilter, facultyFilter, roleFilter, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary Delete a development plan
         * @param {string} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        developmentPlansControllerRemove(id: string, options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.developmentPlansControllerRemove(id, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary Update a development plan
         * @param {string} id 
         * @param {UpdateDevelopmentPlanDto} updateDevelopmentPlanDto 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        developmentPlansControllerUpdate(id: string, updateDevelopmentPlanDto: UpdateDevelopmentPlanDto, options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.developmentPlansControllerUpdate(id, updateDevelopmentPlanDto, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * DevelopmentPlansApi - object-oriented interface
 * @export
 * @class DevelopmentPlansApi
 * @extends {BaseAPI}
 */
export class DevelopmentPlansApi extends BaseAPI {
    /**
     * 
     * @summary Create a new development plan
     * @param {CreateDevelopmentPlanDto} createDevelopmentPlanDto 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DevelopmentPlansApi
     */
    public developmentPlansControllerCreate(createDevelopmentPlanDto: CreateDevelopmentPlanDto, options?: RawAxiosRequestConfig) {
        return DevelopmentPlansApiFp(this.configuration).developmentPlansControllerCreate(createDevelopmentPlanDto, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @summary Get all development plans with pagination
     * @param {number} [limit] Number of items per page
     * @param {number} [page] Page number
     * @param {string} [sortBy] Field to sort by
     * @param {DevelopmentPlansControllerFindAllOrderEnum} [order] Sort order
     * @param {string} [careerType] Filter by career type
     * @param {string} [search] Search term for name or description
     * @param {string} [isCentral] Filter by isCentral
     * @param {number} [parentId] Filter by parent ID
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DevelopmentPlansApi
     */
    public developmentPlansControllerFindAll(limit?: number, page?: number, sortBy?: string, order?: DevelopmentPlansControllerFindAllOrderEnum, careerType?: string, search?: string, isCentral?: string, parentId?: number, options?: RawAxiosRequestConfig) {
        return DevelopmentPlansApiFp(this.configuration).developmentPlansControllerFindAll(limit, page, sortBy, order, careerType, search, isCentral, parentId, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @summary Get development plan by ID
     * @param {string} id 
     * @param {boolean} isCentral 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DevelopmentPlansApi
     */
    public developmentPlansControllerFindOne(id: string, isCentral: boolean, options?: RawAxiosRequestConfig) {
        return DevelopmentPlansApiFp(this.configuration).developmentPlansControllerFindOne(id, isCentral, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * Retrieve personal development plans with pagination and filtering by career, faculty, and role
     * @summary Get personal development plans with filters
     * @param {number} [page] Page number
     * @param {number} [limit] Number of items per page
     * @param {string} [search] Search term
     * @param {string} [sortBy] Sort by field
     * @param {DevelopmentPlansControllerGetPersonalPlansOrderEnum} [order] Sort order
     * @param {number} [careerFilter] Filter by career ID
     * @param {number} [facultyFilter] Filter by faculty ID
     * @param {number} [roleFilter] Filter by role ID
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DevelopmentPlansApi
     */
    public developmentPlansControllerGetPersonalPlans(page?: number, limit?: number, search?: string, sortBy?: string, order?: DevelopmentPlansControllerGetPersonalPlansOrderEnum, careerFilter?: number, facultyFilter?: number, roleFilter?: number, options?: RawAxiosRequestConfig) {
        return DevelopmentPlansApiFp(this.configuration).developmentPlansControllerGetPersonalPlans(page, limit, search, sortBy, order, careerFilter, facultyFilter, roleFilter, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @summary Delete a development plan
     * @param {string} id 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DevelopmentPlansApi
     */
    public developmentPlansControllerRemove(id: string, options?: RawAxiosRequestConfig) {
        return DevelopmentPlansApiFp(this.configuration).developmentPlansControllerRemove(id, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @summary Update a development plan
     * @param {string} id 
     * @param {UpdateDevelopmentPlanDto} updateDevelopmentPlanDto 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DevelopmentPlansApi
     */
    public developmentPlansControllerUpdate(id: string, updateDevelopmentPlanDto: UpdateDevelopmentPlanDto, options?: RawAxiosRequestConfig) {
        return DevelopmentPlansApiFp(this.configuration).developmentPlansControllerUpdate(id, updateDevelopmentPlanDto, options).then((request) => request(this.axios, this.basePath));
    }
}

/**
 * @export
 */
export const DevelopmentPlansControllerFindAllOrderEnum = {
    Asc: 'ASC',
    Desc: 'DESC'
} as const;
export type DevelopmentPlansControllerFindAllOrderEnum = typeof DevelopmentPlansControllerFindAllOrderEnum[keyof typeof DevelopmentPlansControllerFindAllOrderEnum];
/**
 * @export
 */
export const DevelopmentPlansControllerGetPersonalPlansOrderEnum = {
    Asc: 'ASC',
    Desc: 'DESC'
} as const;
export type DevelopmentPlansControllerGetPersonalPlansOrderEnum = typeof DevelopmentPlansControllerGetPersonalPlansOrderEnum[keyof typeof DevelopmentPlansControllerGetPersonalPlansOrderEnum];
