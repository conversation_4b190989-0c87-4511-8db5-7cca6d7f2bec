/* tslint:disable */
/* eslint-disable */
/**
 * HRD Backend API
 * Human Resource Development Backend API Documentation
 *
 * The version of the OpenAPI document: 1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


import type { Configuration } from '../configuration';
import type { AxiosPromise, AxiosInstance, RawAxiosRequestConfig } from 'axios';
import globalAxios from 'axios';
// Some imports not used depending on template conditions
// @ts-ignore
import { DUMMY_BASE_URL, assertParamExists, setApiKeyToObject, setBasicAuthToObject, setBearerAuthToObject, setOAuthToObject, setSearchParams, serializeDataIfNeeded, toPathString, createRequestFunction } from '../common';
// @ts-ignore
import { BASE_PATH, COLLECTION_FORMATS, type RequestArgs, BaseAPI, RequiredError, operationServerMap } from '../base';
// @ts-ignore
import type { CreateAgeWorkDto } from '../models';
// @ts-ignore
import type { UpdateAgeWorkDto } from '../models';
/**
 * AgeWorkApi - axios parameter creator
 * @export
 */
export const AgeWorkApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * 
         * @summary Create a new age work
         * @param {CreateAgeWorkDto} createAgeWorkDto 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        ageWorkControllerCreate: async (createAgeWorkDto: CreateAgeWorkDto, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'createAgeWorkDto' is not null or undefined
            assertParamExists('ageWorkControllerCreate', 'createAgeWorkDto', createAgeWorkDto)
            const localVarPath = `/age-works`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication bearer required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(createAgeWorkDto, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary Get an age work by ID
         * @param {number} id Age work ID
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        ageWorkControllerFindOne: async (id: number, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            assertParamExists('ageWorkControllerFindOne', 'id', id)
            const localVarPath = `/age-works/{id}`
                .replace(`{${"id"}}`, encodeURIComponent(String(id)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication bearer required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary Delete an age work
         * @param {number} id Age work ID
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        ageWorkControllerRemove: async (id: number, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            assertParamExists('ageWorkControllerRemove', 'id', id)
            const localVarPath = `/age-works/{id}`
                .replace(`{${"id"}}`, encodeURIComponent(String(id)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'DELETE', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication bearer required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary Update an age work
         * @param {number} id Age work ID
         * @param {UpdateAgeWorkDto} updateAgeWorkDto 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        ageWorkControllerUpdate: async (id: number, updateAgeWorkDto: UpdateAgeWorkDto, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            assertParamExists('ageWorkControllerUpdate', 'id', id)
            // verify required parameter 'updateAgeWorkDto' is not null or undefined
            assertParamExists('ageWorkControllerUpdate', 'updateAgeWorkDto', updateAgeWorkDto)
            const localVarPath = `/age-works/{id}`
                .replace(`{${"id"}}`, encodeURIComponent(String(id)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'PATCH', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication bearer required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(updateAgeWorkDto, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * AgeWorkApi - functional programming interface
 * @export
 */
export const AgeWorkApiFp = function(configuration?: Configuration) {
    const localVarAxiosParamCreator = AgeWorkApiAxiosParamCreator(configuration)
    return {
        /**
         * 
         * @summary Create a new age work
         * @param {CreateAgeWorkDto} createAgeWorkDto 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async ageWorkControllerCreate(createAgeWorkDto: CreateAgeWorkDto, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.ageWorkControllerCreate(createAgeWorkDto, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['AgeWorkApi.ageWorkControllerCreate']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @summary Get an age work by ID
         * @param {number} id Age work ID
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async ageWorkControllerFindOne(id: number, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.ageWorkControllerFindOne(id, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['AgeWorkApi.ageWorkControllerFindOne']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @summary Delete an age work
         * @param {number} id Age work ID
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async ageWorkControllerRemove(id: number, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.ageWorkControllerRemove(id, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['AgeWorkApi.ageWorkControllerRemove']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @summary Update an age work
         * @param {number} id Age work ID
         * @param {UpdateAgeWorkDto} updateAgeWorkDto 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async ageWorkControllerUpdate(id: number, updateAgeWorkDto: UpdateAgeWorkDto, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.ageWorkControllerUpdate(id, updateAgeWorkDto, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['AgeWorkApi.ageWorkControllerUpdate']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
    }
};

/**
 * AgeWorkApi - factory interface
 * @export
 */
export const AgeWorkApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    const localVarFp = AgeWorkApiFp(configuration)
    return {
        /**
         * 
         * @summary Create a new age work
         * @param {CreateAgeWorkDto} createAgeWorkDto 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        ageWorkControllerCreate(createAgeWorkDto: CreateAgeWorkDto, options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.ageWorkControllerCreate(createAgeWorkDto, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary Get an age work by ID
         * @param {number} id Age work ID
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        ageWorkControllerFindOne(id: number, options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.ageWorkControllerFindOne(id, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary Delete an age work
         * @param {number} id Age work ID
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        ageWorkControllerRemove(id: number, options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.ageWorkControllerRemove(id, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary Update an age work
         * @param {number} id Age work ID
         * @param {UpdateAgeWorkDto} updateAgeWorkDto 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        ageWorkControllerUpdate(id: number, updateAgeWorkDto: UpdateAgeWorkDto, options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.ageWorkControllerUpdate(id, updateAgeWorkDto, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * AgeWorkApi - object-oriented interface
 * @export
 * @class AgeWorkApi
 * @extends {BaseAPI}
 */
export class AgeWorkApi extends BaseAPI {
    /**
     * 
     * @summary Create a new age work
     * @param {CreateAgeWorkDto} createAgeWorkDto 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof AgeWorkApi
     */
    public ageWorkControllerCreate(createAgeWorkDto: CreateAgeWorkDto, options?: RawAxiosRequestConfig) {
        return AgeWorkApiFp(this.configuration).ageWorkControllerCreate(createAgeWorkDto, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @summary Get an age work by ID
     * @param {number} id Age work ID
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof AgeWorkApi
     */
    public ageWorkControllerFindOne(id: number, options?: RawAxiosRequestConfig) {
        return AgeWorkApiFp(this.configuration).ageWorkControllerFindOne(id, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @summary Delete an age work
     * @param {number} id Age work ID
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof AgeWorkApi
     */
    public ageWorkControllerRemove(id: number, options?: RawAxiosRequestConfig) {
        return AgeWorkApiFp(this.configuration).ageWorkControllerRemove(id, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @summary Update an age work
     * @param {number} id Age work ID
     * @param {UpdateAgeWorkDto} updateAgeWorkDto 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof AgeWorkApi
     */
    public ageWorkControllerUpdate(id: number, updateAgeWorkDto: UpdateAgeWorkDto, options?: RawAxiosRequestConfig) {
        return AgeWorkApiFp(this.configuration).ageWorkControllerUpdate(id, updateAgeWorkDto, options).then((request) => request(this.axios, this.basePath));
    }
}

