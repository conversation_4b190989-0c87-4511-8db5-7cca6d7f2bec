import type { RouteRecordRaw } from 'vue-router';

const skillRoutes: RouteRecordRaw[] = [
  {
    path: '/skill',
    name: 'skill',
    component: () => import('../layouts/MainLayout.vue'),
    meta: { breadcrumb: { label: 'จัดการทักษะ', link: '/skill/management' } },
    beforeEnter: (to, from, next) => {
      if (to.path === '/skill') {
        next({ name: 'skill-management' });
      } else {
        next();
      }
    },
    children: [
      {
        path: 'management',
        name: 'skill-management',
        component: () => import('../pages/skill/SkillManagementPage.vue'),
        meta: { breadcrumb: 'ทักษะทั้งหมด' },
      },
    ],
  },
];

export default skillRoutes;
