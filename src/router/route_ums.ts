import type { RouteRecordRaw } from 'vue-router';

const umsRoutes: RouteRecordRaw[] = [
  {
    path: '/ums',
    name: 'ums',
    meta: { breadcrumb: { label: 'ระบบจัดการผู้ใช้และสิทธิ์', link: '/ums/management' } },
    component: () => import('src/layouts/MainLayout.vue'),
    beforeEnter: (to, from, next) => {
      if (to.path === '/ums') {
        next({ name: 'ums-management' });
      } else {
        next();
      }
    },
    children: [
      {
        path: 'management',
        name: 'ums-management',
        component: () => import('src/pages/ums/UmsMainPage.vue'),
        meta: { breadcrumb: 'จัดการผู้ใช้งานและบทบาท' },
      },
    ],
  },
];

export default umsRoutes;
