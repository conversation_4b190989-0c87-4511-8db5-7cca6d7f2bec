import type { RouteRecordRaw } from 'vue-router';

const monitorRoutes: RouteRecordRaw[] = [
  {
    path: '/monitor',
    name: 'monitor',
    component: () => import('../layouts/MainLayout.vue'),
    meta: { breadcrumb: { label: 'Monitor', link: '/monitor' } },
    children: [
      {
        path: '',
        name: '',
        component: () => import('src/pages/monitor/monitorPage.vue'),
        meta: { breadcrumb: 'หน้าหลัก Monitor' },
      },
    ],
  },
];

export default monitorRoutes;
