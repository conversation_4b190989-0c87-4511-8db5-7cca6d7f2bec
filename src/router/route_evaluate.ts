import type { RouteRecordRaw, RouteLocationNormalized } from 'vue-router';

const evaluateRoutes: RouteRecordRaw[] = [
  {
    path: '/evaluate',
    name: 'evaluate',
    meta: {
      breadcrumb: {
        label: 'จัดการแบบประเมิน',
        link: '/evaluate/management',
      },
    },
    component: () => import('src/layouts/MainLayout.vue'),
    beforeEnter: (to, from, next) => {
      if (to.name === 'evaluate') {
        next({ name: 'evaluate-management' });
      } else {
        next();
      }
    },
    children: [
      {
        path: 'management',
        name: 'evaluate-management',
        component: () => import('src/pages/evaluate/EvaluateManagementPage.vue'),
        meta: { breadcrumb: 'แบบประเมินทั้งหมด' },
      },
      {
        path: ':id(\\d+)/edit',
        name: 'evaluate-edit',
        component: () => import('src/pages/evaluate/EvaluateEditorPage.vue'),
        props: true,
        meta: {
          breadcrumb: (route: RouteLocationNormalized) => `แก้ไข #${route.params.id}`,
        },
      },
      {
        path: ':id/:section/preview',
        name: 'evaluate-preview',
        component: () => import('src/pages/evaluate/DoEvaluatePage.vue'),
        props: true,
        meta: {
          status: 'preview',
          breadcrumb: (route: RouteLocationNormalized) =>
            `พรีวิว #${route.params.id} ส่วนที่ ${route.params.section}`,
        },
      },
    ],
  },
];

export default evaluateRoutes;
