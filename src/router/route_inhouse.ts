import type { RouteRecordRaw } from 'vue-router';

const inHouseRoutes: RouteRecordRaw[] = [
  {
    path: '/in-house',
    name: 'in-house',
    component: () => import('../layouts/MainLayout.vue'),
    meta: { breadcrumb: { label: 'In-House', link: '/in-house/management' } },
    beforeEnter: (to, from, next) => {
      if (to.path === '/in-house') {
        next({ name: 'in-house-management' });
      } else {
        next();
      }
    },
    children: [
      {
        path: 'management',
        name: 'in-house-management',
        component: () => import('../pages/in-house/InHouseManagementPage.vue'),
        meta: { breadcrumb: 'หน้าหลัก In-House' },
      },
    ],
  },
];

export default inHouseRoutes;
