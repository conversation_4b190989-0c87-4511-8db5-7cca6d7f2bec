import { createRouter, createWebHistory } from 'vue-router';
import type { NavigationGuardNext, RouteLocationNormalized } from 'vue-router';
import umsRoutes from './route_ums';
import quizRoutes from './route_quiz';
import evaluateRoutes from './route_evaluate';
import { useAuthStore } from '../stores/auth';
import { jwtDecode, type JwtPayload } from 'jwt-decode';
import idpRoutes from './route_idp';
import inHouseRoutes from './route_inhouse';
import skillRoutes from './route_skill';
import competencyRoutes from './route_competency';
import monitorRoutes from './route_monitor';
import personalRoutes from './route_personal';

// ✅ ฟังก์ชันเช็คว่า token หมดอายุหรือยัง
function isTokenExpired(token: string): boolean {
  try {
    const decoded = jwtDecode<JwtPayload>(token);
    if (!decoded.exp) return true;
    return decoded.exp * 1000 < Date.now(); // exp เป็นวินาที, Date.now() เป็นมิลลิวินาที
  } catch {
    return true;
  }
}

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/login',
      component: () => import('layouts/FullLayout.vue'),
      children: [{ path: '', name: 'login', component: () => import('../pages/LoginPage.vue') }],
    },
    {
      path: '/',
      component: () => import('src/layouts/MainLayout.vue'),
      children: [
        {
          path: '', // Empty path means this is the default child route
          redirect: { name: 'home' }, // Redirect to the home page
        },
        {
          path: 'home',
          name: 'home',
          component: () => import('../pages/HomePage.vue'),
        },
      ],
    },
    ...umsRoutes,
    ...quizRoutes,
    ...evaluateRoutes,
    ...idpRoutes,
    ...inHouseRoutes,
    ...competencyRoutes,
    ...skillRoutes,
    ...monitorRoutes,
    ...personalRoutes,
    {
      path: '/test',
      component: () => import('src/layouts/MainLayout.vue'),
      children: [
        {
          path: '',
          name: 'test',
          component: () => import('pages/TestPage.vue'),
        },
      ],
    },
    {
      path: '/:catchAll(.*)*',
      component: () => import('pages/ErrorNotFound.vue'),
    },
  ],
});

// ✅ Helper function to handle login page access
function handleLoginPageAccess(token: string | null, next: NavigationGuardNext): void {
  // If user has valid token and trying to access login, redirect to home or intended page
  if (token && !isTokenExpired(token)) {
    // Check if there's a redirect path stored from previous navigation attempt
    const redirectPath = localStorage.getItem('redirectAfterLogin');
    if (redirectPath && redirectPath !== '/login') {
      localStorage.removeItem('redirectAfterLogin');
      console.log('Redirecting to stored path:', redirectPath);
      next(redirectPath);
    } else {
      console.log('Redirecting to home');
      next({ name: 'home' });
    }
    return;
  }
  // Allow access to login page if no valid token
  next();
}

// ✅ Helper function to handle permission-based access
function handlePermissionAccess(
  authStore: ReturnType<typeof useAuthStore>,
  to: RouteLocationNormalized,
  next: NavigationGuardNext,
): void {
  if (!to.meta.perms) {
    next();
    return;
  }

  // Check if user is Super Admin - if so, allow access to all routes
  if (authStore.isSuperAdmin()) {
    next();
    return;
  }

  // Check string-based permissions
  const perms = to.meta.perms as string[];
  const hasAccess =
    Array.isArray(perms) && perms.length > 0 ? authStore.hasAnyPermissionByName(perms) : false;

  if (hasAccess) {
    next();
  } else {
    next({ name: 'home' });
  }
}

// ✅ Global Route Guard
router.beforeEach(
  (to: RouteLocationNormalized, from: RouteLocationNormalized, next: NavigationGuardNext) => {
    const authStore = useAuthStore();
    const token = localStorage.getItem('access_token');

    console.log('Router guard - navigating from:', from.path, 'to:', to.path);
    console.log('Router guard - token exists:', !!token);

    // 🔐 ตรวจสอบว่า token หมดอายุหรือยัง
    if (token && isTokenExpired(token)) {
      console.log('Token expired, logging out');
      authStore.logout(); // ล้าง token และ redirect ไป login
      return;
    }

    // ✅ Handle login page access prevention for authenticated users
    if (to.name === 'login') {
      console.log('Attempting to access login page');
      handleLoginPageAccess(token, next);
      return;
    }

    localStorage.setItem('acs', 'Y');

    if (to.meta.acs) {
      // หากเข้าผ่านลิงก์พิเศษ เช่น email link
      localStorage.setItem('acs', 'Y');
      next();
      return;
    }

    if (!authStore.isLoggedIn && to.name !== 'login' && to.name !== 'at-test') {
      localStorage.setItem('acs', 'N');
      // ✅ เพิ่มบรรทัดนี้เพื่อเก็บ path ที่ user พยายามเข้าก่อน login
      console.log('User not logged in, storing redirect path:', to.fullPath);
      localStorage.setItem('redirectAfterLogin', to.fullPath);
      next({ name: 'login' });
      return;
    }

    localStorage.setItem('acs', 'N');
    // Handle permission-based access control
    handlePermissionAccess(authStore, to, next);
  },
);

export default router;
