import type { RouteLocationNormalized, RouteRecordRaw } from 'vue-router';

const quizRoutes: RouteRecordRaw[] = [
  {
    path: '/quiz',
    name: 'quiz',
    meta: {
      breadcrumb: {
        label: 'จัดการแบบทดสอบ',
        link: '/quiz/management',
      },
    },
    component: () => import('src/layouts/MainLayout.vue'),
    beforeEnter: (to, from, next) => {
      if (to.path === '/quiz') {
        next({ name: 'quiz-management' });
      } else {
        next();
      }
    },
    children: [
      {
        path: 'management',
        name: 'quiz-management',
        component: () => import('src/pages/quiz/QuizManagementPage.vue'),
        meta: { breadcrumb: 'แบบทดสอบทั้งหมด' },
      },
      {
        path: ':id(\\d+)/edit',
        name: 'quiz-edit',
        component: () => import('src/pages/quiz/QuizEditorPage.vue'),
        props: true,
        meta: {
          breadcrumb: (route: RouteLocationNormalized) => `แก้ไข #${route.params.id}`,
        },
      },
      {
        path: ':linkUrl/preview',
        name: 'quiz-preview',
        component: () => import('src/pages/quiz/DoQuizPage.vue'),
        props: true,
        meta: {
          breadcrumb: (route: RouteLocationNormalized) => `พรีวิว #${route.params.linkUrl}`,
        },
      },
      {
        path: '/participant/:participantId',
        name: 'ParticipantDetails',
        component: () => import('src/components/quiz/ParticipantDetailsPage.vue'),
        props: true,
        meta: {
          requiresAuth: true,
          title: 'รายละเอียดผู้เข้าสอบ',
          breadcrumb: (route: RouteLocationNormalized) =>
            `รายละเอียดผู้เข้าสอบ #${route.params.participantId}`,
        },
      },
    ],
  },
];

export default quizRoutes;
