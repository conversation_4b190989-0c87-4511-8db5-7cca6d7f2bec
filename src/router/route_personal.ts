import { getAllowedSectionsForUser } from 'src/utils/sequenceHelper';
import type { RouteLocationNormalized, RouteRecordRaw } from 'vue-router';

const personalRoutes: RouteRecordRaw[] = [
  {
    path: '/personal',
    name: 'personal',
    meta: { breadcrumb: { label: 'ส่วนตัว' } },
    component: () => import('src/layouts/MainLayout.vue'),
    children: [
      {
        path: 'do-quiz/:linkUrl',
        name: 'do-quiz',
        component: () => import('src/pages/quiz/DoQuizPage.vue'),
        meta: {
          breadcrumb: (route: RouteLocationNormalized) => `ทำแบบทดสอบ ${route.params.linkUrl}`,
        },
      },
      {
        path: 'my-quiz',
        name: 'my-quiz',
        component: () => import('src/pages/MyAssessmentPage.vue'),
        props: { type: 'quiz' },
        meta: { breadcrumb: 'แบบทดสอบของฉัน' },
      },
      {
        path: 'do-evaluate/:url/:section',
        name: 'do-evaluate',
        component: () => import('src/pages/evaluate/DoEvaluatePage.vue'),
        props: true,
        meta: {
          status: 'do',
          id: 0,
          breadcrumb: (route: RouteLocationNormalized) =>
            `ทำแบบสอบถาม ${route.params.url} ส่วนที่ ${route.params.section}`,
        },
        beforeEnter: (to, from, next) => {
          try {
            const allowedSections = getAllowedSectionsForUser();
            const currentSection = Number(to.params.section);

            if (allowedSections.includes(currentSection)) {
              next();
            } else {
              // redirect กลับไปยัง section ล่าสุดที่ user มีสิทธิ์
              const lastAllowed = Math.max(...allowedSections);
              next({
                name: 'evaluate-do',
                params: {
                  url: to.params.url,
                  section: lastAllowed.toString(),
                },
              });
            }
          } catch (error) {
            console.error('beforeEnter error:', error);
            next(false);
          }
        },
      },
      {
        path: 'my-evaluate',
        name: 'my-evaluate',
        component: () => import('src/pages/MyAssessmentPage.vue'),
        props: { type: 'evaluate' },
        meta: { breadcrumb: 'แบบสอบถามของฉัน' },
      },
      {
        path: 'my-plan',
        name: 'my-development-plan',
        component: () => import('../pages/idp/MyDevelopmentPlan.vue'),
        meta: { breadcrumb: 'แผนพัฒนาของฉัน' },
      },
    ],
  },
];

export default personalRoutes;
