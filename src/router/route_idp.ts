import type { RouteLocationNormalized, RouteRecordRaw } from 'vue-router';
import { useGlobalStore } from 'src/stores/global';

const idpRoutes: RouteRecordRaw[] = [
  {
    path: '/idp',
    name: 'idp',
    component: () => import('../layouts/MainLayout.vue'),
    meta: { breadcrumb: { label: 'จัดการแผนพัฒนาบุคลากร', link: '/idp/management' } },
    beforeEnter: (to, from, next) => {
      if (to.path === '/idp') {
        next({ name: 'idp-management' });
      } else {
        next();
      }
    },
    children: [
      {
        path: 'dev-management',
        name: 'dev-management',
        component: () => import('../pages/idp/DevPlanManagement.vue'),
        meta: { breadcrumb: 'แผนพัฒนาทั้งหมด' },
      },
      {
        path: 'planing',
        name: 'Development Planing',
        component: () => import('../pages/idp/DevPlaning.vue'),
        meta: { breadcrumb: 'วางแผนพัฒนา' },
      },
      {
        path: 'planing/:id',
        name: 'dev-plan-planning',
        component: () => import('../pages/idp/DevPlaning.vue'),
        meta: {
          breadcrumb: () => {
            const globalStore = useGlobalStore();
            const year = globalStore.developmentPlanYear || 'XXXX';
            return `จัดการแผนพัฒนาบุคลากร ปี ${year}`;
          },
        },
      },
      {
        path: 'idp-management',
        name: 'idp-management',
        component: () => import('../pages/idp/IdpManagementPage.vue'),
        meta: { breadcrumb: 'แผนพัฒนารายบุคคล' },
      },
      {
        path: 'age-work-criteria',
        name: 'idp-age-work-management',
        component: () => import('../pages/idp/AgeWorkManagementPage.vue'),
        meta: { breadcrumb: 'เกณฑ์อายุงาน' },
      },
      {
        path: 'age-work-criteria/:criteriaId/age-works',
        name: 'idp-age-work',
        component: () => import('../pages/idp/AgeWorkPage.vue'),
        meta: {
          breadcrumb: (route: RouteLocationNormalized) => `อายุงาน #${route.params.criteriaId}`,
        },
      },
    ],
  },
];

export default idpRoutes;
