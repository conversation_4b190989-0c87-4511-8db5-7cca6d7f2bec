// ENUMS
export type ItemBlockType = DropdownItemBlockType | 'HEADER' | 'IMAGE' | 'NEXTSECTION';
export type DropdownItemBlockType =
  | 'RADIO'
  | 'CHECKBOX'
  | 'TEXTFIELD'
  | 'GRID'
  | 'UPLOAD'
  | 'NEXTSECTION';

// USERS
export type User = {
  id: string; // Changed to string to match PSN_ID
  idcard?: string;
  prefixTh?: string;
  prefixEn?: string;
  firstNameTh?: string;
  firstNameEn?: string;
  lastNameTh?: string;
  lastNameEn?: string;
  campusName?: string;
  facultyName?: string;
  departmentName?: string;
  groupName?: string;
  positionName?: string;
  positionLevel?: string;
  startDate?: string;
  workStatus?: 'Y' | 'N';
  email: string;
  username?: string;
  password?: string;
  currentPassword?: string;
  newPassword?: string;

  // New structure fields
  firstName?: string;
  lastName?: string;
  departments?: UserDepartment[];

  // Relations
  programs?: Program[];
  assessments?: Assessment[];
  submissions?: Submission[];
  userSkills?: UserSkill[];
  permAuditSkills?: PermAuditSkill[];
  evaluatedSkills?: Skill[];
  careerRecords?: CareerRecord[];
  facDepUserRoles?: FacDepUserRole[];
  privatePlans?: TypePlan[];

  // Legacy fields for backward compatibility
  roleId?: number;
  dep_id?: number;
  roles?: Role[];
  dep?: DEP;
  permissions?: { perId: number; perNameEn: string }[];
  faculties?: Faculty[];
};

// FACULTIES
export type Faculty = {
  id: string; // Changed to string to match FAC_ID
  name: string; // Changed from nameEn/nameTh to single name
  campus?: Campus;
  departments?: Department[];
  developmentPlans?: DevelopmentPlan[];
  careerRecords?: CareerRecord[];

  // Legacy fields for backward compatibility
  nameEn?: string;
  nameTh?: string;
  roles?: Role[];
};

// ROLES
export type Role = {
  id: number;
  name: string;
  department: string; // Added new field
  description: string;
  permissions?: Permission[];
  facDepRoles?: FacDepUserRole[];

  // Legacy fields for backward compatibility
  userId?: number;
};

export type createRoles = {
  name: string;
  description: string;
  department?: string;
};

// INDIVIDUAL DEVELOPMENT PLAN
export type IndividualDevelopmentPlanStatus = 'active' | 'inactive' | 'completed' | 'cancelled';

export type IndividualDevelopmentPlan = {
  id: number;
  employee_name: string;
  employee_id: string;
  department: string;
  position: string;
  development_goals: string;
  target_year: number;
  status: IndividualDevelopmentPlanStatus;
  development_activities?: string;
  success_criteria?: string;
  created_at: string;
  updated_at?: string;
};

export type IndividualDevelopmentPlanForm = Omit<
  IndividualDevelopmentPlan,
  'id' | 'created_at' | 'updated_at'
> & {
  target_year: number;
};

// PERMISSIONS
export type Permission = {
  id: number;
  name: string;
  descEn: string; // Changed from nameEn
  descTh: string; // Added descTh
  status: boolean;
  isDefault: boolean;
  roles?: Role[];

  // Legacy fields for backward compatibility
  roleId?: number;
  nameEn?: string;
  role?: Role;
  perId?: number;
  perName?: string;
  perDescEn?: string;
  perDescTh?: string;
};

// PROGRAMS
export type Program = {
  id: number;
  creatorUserId: string; // Changed to string to match User.id
  name: string;
  description: string;

  creatorUser?: User;
  assessments?: Assessment[];
};

// ASSESSMENTS
export type Assessment = {
  id: number;
  creatorUserId: string; // Changed to string to match User.id
  programId: number;
  name: string;
  type: 'QUIZ' | 'EVALUATE';
  createdAt: string;
  startAt?: string;
  endAt?: string;
  submitLimit?: number;
  linkURL: string;
  responseEdit: boolean;
  status: boolean;
  totalScore: number;
  timeout: number;
  passRatio: number;
  program?: Program;
  creatorUser?: User;
  itemBlocks?: ItemBlock[];
  submissions?: Submission[];
  globalIsRequired?: boolean;
  isPrototype?: boolean;
};

// ITEM_BLOCKS
export type ItemBlock = {
  id: number;
  assessmentId: number;
  sequence: number;
  section: number;
  type: ItemBlockType;
  isRequired: boolean;

  headerBody?: HeaderBody;
  imageBody?: ImageBody;
  questions?: Question[];
  options?: Option[];
};

// HEADER_BODIES
export type HeaderBody = {
  id: number;
  itemBlockId: number;
  title: string;
  description?: string;
  nextSection?: number;
};

// IMAGE_BODIES
export type ImageBody = {
  id: number;
  itemBlockId: number;
  imageText?: string;
  imagePath?: string;
  imageWidth?: number;
  imageHeight?: number;
};

// QUESTIONS
export type Question = {
  id: number;
  itemBlockId: number;
  questionText: string;
  imagePath?: string;
  imageWidth?: number;
  imageHeight?: number;
  isHeader: boolean;
  sequence: number;
  sizeLimit?: number;
  acceptFile?: string;
  uploadLimit?: number;
  score: number;
};

// OPTIONS
export type Option = {
  id: number;
  itemBlockId: number;
  optionText: string;
  imagePath?: string;
  value: number;
  sequence: number;
  nextSection?: number;
};

// SUBMISSIONS
export type Submission = {
  id: number;
  assessmentId: number;
  userId: string; // Changed to string to match User.id
  startAt: string;
  endAt: string;

  user?: User;
  responses?: Response[];
};

// RESPONSES
export type Response = {
  id: number;
  submissionId: number;
  selectedOptionId?: number;
  questionId?: number;
  filePath?: string;

  selectedOption?: Option;
  question?: Question;
};

export type getQuestionMeta = {
  questionBlock: ItemBlock;
  response: Response[] | null;
  isLast: boolean;
  questionList: questionList[];
};

export type questionList = {
  id: number;
  sequence: number;
  isDone: boolean;
};

export interface Competency {
  id?: number;
  name: string;
  description?: string;
  career_type: string;
  skills?: Skill[];
}

export type Skill = {
  id?: number;
  name: string;
  description?: string;
  dep_id: number;
  evaluatorId: string; // Changed to string to match User.id
  competencyIds: number[];
  career_type: string;
  tracking: boolean;
  programId: number;

  dep?: DEP;
  program?: Program;
  evaluator?: User;
  competencies?: Partial<Competency[]>;
};

export type DEP = {
  dep_id: number;
  name: string;
};

export type IDP = {
  id: number;
  name: string;
  creatorUserId?: string; // Changed to string to match User.id
};

// NEW TYPES MATCHING BACKEND STRUCTURE

// USER DEPARTMENTS (for new structure)
export type UserDepartment = {
  id: string;
  name: string;
  faculty?: Faculty;
  role?: Role;
  isDepartmentDirector: boolean;
};

// CAMPUS
export type Campus = {
  id: string;
  name: string;
  faculties?: Faculty[];
};

// DEPARTMENTS
export type Department = {
  id: string;
  name: string;
  faculty?: Faculty;
  facDepUserRoles?: FacDepUserRole[];
};

// FACULTY DEPARTMENT USER ROLES
export type FacDepUserRole = {
  id: number;
  departmentId?: string;
  roleId: number;
  personId: string;
  isDepartmentDirector: boolean;
  department?: Department;
  user?: User;
  role?: Role;
};

// USER SKILLS
export type UserSkill = {
  id?: number;
  userId: string;
  skillId: number;
  user?: User;
  skill?: Skill;
};

// PERMISSION AUDIT SKILLS
export type PermAuditSkill = {
  id?: number;
  userId: string;
  skillId: number;
  user?: User;
  skill?: Skill;
};

// CAREER RECORDS
export type CareerRecord = {
  id?: number;
  userId: string;
  facultyId?: string;
  user?: User;
  faculty?: Faculty;
};

// DEVELOPMENT PLANS
export type DevelopmentPlan = {
  id?: number;
  facultyId?: string;
  faculty?: Faculty;
};

// TYPE PLANS
export type TypePlan = {
  id?: number;
  privatePlanUserId?: string;
  privatePlanUser?: User;
};
