import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsNotEmpty, IsOptional, Matches } from 'class-validator';

export class UploadFileDto {
  @ApiProperty({
    description: 'File path where the file is stored',
    example: '/uploads/documents/report.pdf',
    type: String,
  })
  @IsString()
  @IsNotEmpty()
  path: string;

  @ApiProperty({
    description: 'Original file name',
    example: 'Annual_Report_2024.pdf',
    type: String,
  })
  @IsString()
  @IsNotEmpty()
  fileName: string;

  @ApiProperty({
    description: 'File MIME type',
    example: 'application/pdf',
    type: String,
  })
  @IsString()
  @IsNotEmpty()
  @Matches(/^[\w-]+\/[\w-]+$/, {
    message: 'File type must be a valid MIME type (e.g., application/pdf, image/jpeg)',
  })
  fileType: string;

  @ApiProperty({
    description: 'QR code verification string (optional)',
    example: 'QR123456789',
    type: String,
    required: false,
  })
  @IsString()
  @IsOptional()
  qrVerify?: string;
}

/**
 * Response DTO for file upload operation
 */
export class UploadFileResponseDto {
  @ApiProperty({
    description: 'Unique file identifier',
    example: 'file_123456789',
    type: String,
  })
  fileId: string;

  @ApiProperty({
    description: 'File access URL',
    example: 'https://example.com/uploads/documents/report.pdf',
    type: String,
  })
  fileUrl: string;

  @ApiProperty({
    description: 'File size in bytes',
    example: 1024000,
    type: Number,
  })
  fileSize: number;

  @ApiProperty({
    description: 'Upload timestamp',
    example: '2024-01-15T10:30:00Z',
    type: String,
    format: 'date-time',
  })
  uploadedAt: Date;
}
