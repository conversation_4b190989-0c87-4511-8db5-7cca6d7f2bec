import { DataTablePaginate } from './dataTable';

/**
 * Standard paginated response type used across all API endpoints
 * Uses the generic DataTablePaginate interface for consistency
 */
export type DataResponse<T> = DataTablePaginate<T>;

/**
 * Standard query parameters for paginated API requests
 */
export interface DataParams {
  /** Field to sort by */
  sortBy?: string;
  /** Sort order: 'ASC' for ascending, 'DESC' for descending */
  order: 'ASC' | 'DESC';
  /** Maximum number of items to return per page */
  limit: number;
  /** Page number (1-based) */
  page: number;
  /** Search query string */
  search?: string;
}

/**
 * Development plan tab types (Thai language)
 */
export type DevPlanTabs =
  | 'ทั้งหมด'
  | 'ทั่วไปบุคลากร'
  | 'ทั่วไปผู้บริหาร'
  | 'เฉพาะทางสายวิชาการ'
  | 'เฉพาะทางสนับสนุนวิชาการ'
  | 'เฉพาะทางผู้บริหาร'
  | 'ตำแหน่ง';

/**
 * Development plan tab values array
 * Pre-defined array for validation and iteration
 */
export const DevPlanTabsValues: DevPlanTabs[] = [
  'ทั้งหมด',
  'ทั่วไปบุคลากร',
  'ทั่วไปผู้บริหาร',
  'เฉพาะทางสายวิชาการ',
  'เฉพาะทางสนับสนุนวิชาการ',
  'เฉพาะทางผู้บริหาร',
  'ตำแหน่ง',
] as const;

/**
 * Extended query parameters for development plan filtering
 */
export interface DevelopmentPlanParams extends DataParams {
  /** Development plan tab type */
  type: DevPlanTabs;
}

/**
 * Default pagination values for consistent API behavior
 */
export const DEFAULT_PAGINATION = {
  PAGE: 1,
  LIMIT: 10,
  SORT_BY: 'createdAt',
  ORDER: 'DESC' as const,
} as const;

/**
 * Creates standardized query parameters with defaults
 * @param params User-provided query parameters
 * @returns Query parameters with defaults applied
 */
export function normalizeQueryParams(params: Partial<DataParams>): DataParams {
  return {
    sortBy: params.sortBy ?? DEFAULT_PAGINATION.SORT_BY,
    order: params.order ?? DEFAULT_PAGINATION.ORDER,
    limit: Math.max(1, Math.min(100, params.limit ?? DEFAULT_PAGINATION.LIMIT)), // Clamp between 1-100
    page: Math.max(1, params.page ?? DEFAULT_PAGINATION.PAGE), // Ensure minimum 1
    search: params.search?.trim() || undefined,
  };
}

/**
 * Validates development plan tab type
 * @param tab The tab value to validate
 * @returns Whether the tab is valid
 */
export function isValidDevPlanTab(tab: string): tab is DevPlanTabs {
  return DevPlanTabsValues.includes(tab as DevPlanTabs);
}
