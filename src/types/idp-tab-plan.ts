import type { Skill } from './models';

export type IdpTabs = 'common-i' | 'common-m' | 'specialized' | 'support' | 'manager' | 'career';

export interface CreateIdpTabPlanProps {
  tabs: IdpTabs;
}

export interface IdpFormData {
  ageWork: string;
  skills: Skill[];
  disableAgeWork?: boolean;
}

export interface CreateIdpTabPlanEmits {
  (e: 'update:formData', value: { ageWork: string; skills: Skill[] }): void;
  (e: 'submit', value: { ageWork: string; skills: Skill[] }): void;
}

export interface AgeWorkOption {
  label: string;
  value: '1-2YEAR' | '3-5YEAR' | '6-8YEAR' | '9UP';
}

export interface SkillTypeOption {
  label: string;
  value: string;
}
