# Types Directory

This directory contains TypeScript type definitions, interfaces, and DTOs used throughout the application.

## Structure

```
src/types/
├── DataTablePaginate.ts    # Generic pagination interface and utilities
├── params.ts               # Query parameters and validation utilities
├── api/
│   └── uploadFile.ts       # File upload related DTOs
├── index.ts               # Main export file
└── README.md              # This file
```

## Files Overview

### DataTablePaginate.ts
- **Purpose**: Generic interface for paginated data responses
- **Key Features**:
  - Type-safe pagination with generic support
  - Helper function `createPaginatedResponse()` for consistent response formatting
  - Optional pagination metadata (totalPages, hasPrev, hasNext)

### params.ts
- **Purpose**: Query parameters and validation utilities
- **Key Features**:
  - `DataParams`: Standard query parameters for paginated requests
  - `DataResponse`: Type alias for paginated responses
  - `DevPlanTabs`: Enum for development plan tabs
  - `DevelopmentPlanParams`: Extended parameters for development plan filtering
  - `normalizeQueryParams()`: Utility to apply defaults and validate parameters
  - `isValidDevPlanTab()`: Validation function for development plan tabs
  - `DEFAULT_PAGINATION`: Constants for consistent pagination behavior

### api/uploadFile.ts
- **Purpose**: File upload related DTOs
- **Key Features**:
  - `UploadFileDto`: Request DTO for file uploads with validation
  - `UploadFileResponseDto`: Response DTO for file upload operations
  - Comprehensive Swagger documentation
  - Input validation using class-validator decorators

## Usage Examples

### Pagination
```typescript
import { DataParams, DataResponse, normalizeQueryParams } from 'src/types';

// Normalize query parameters with defaults
const params: DataParams = normalizeQueryParams({
  page: 2,
  limit: 20,
  search: 'example'
});

// Use in service
async getUsers(query: DataParams): Promise<DataResponse<User>> {
  const { page, limit } = query;
  const [users, total] = await Promise.all([
    userRepository.find({ skip: (page - 1) * limit, take: limit }),
    userRepository.count()
  ]);
  
  return createPaginatedResponse(users, total, page, limit);
}
```

### File Upload
```typescript
import { UploadFileDto, UploadFileResponseDto } from 'src/types/api/uploadFile';

// In controller
@Post('upload')
@ApiConsumes('multipart/form-data')
@UseInterceptors(FileInterceptor('file'))
async uploadFile(@Body() uploadDto: UploadFileDto, @UploadedFile() file: Express.Multer.File) {
  // Process file upload
  const response: UploadFileResponseDto = {
    fileId: 'file_123',
    fileUrl: `/uploads/${file.filename}`,
    fileSize: file.size,
    uploadedAt: new Date()
  };
  return response;
}
```

### Development Plan Filtering
```typescript
import { DevelopmentPlanParams, isValidDevPlanTab } from 'src/types';

// Validate development plan tab
const tab = 'ทั่วไปบุคลากร';
if (isValidDevPlanTab(tab)) {
  // Use tab safely
}

// In service
async getDevelopmentPlans(params: DevelopmentPlanParams) {
  const { type, ...queryParams } = params;
  // Filter by development plan type
}
```

## Performance Optimizations

1. **Type Safety**: Strong typing prevents runtime errors and improves IDE support
2. **Validation**: Input validation at the DTO level reduces database errors
3. **Consistency**: Standardized interfaces ensure consistent API responses
4. **Reusability**: Common types and utilities reduce code duplication
5. **Documentation**: Comprehensive JSDoc and Swagger annotations improve maintainability

## Best Practices

1. **Import from index.ts**: Always import from the main index file for consistency
2. **Use generics**: Leverage generic types for reusable components
3. **Validate early**: Apply validation at the DTO level before business logic
4. **Document thoroughly**: Use JSDoc and Swagger annotations for complex types
5. **Keep focused**: Each file should have a single, clear responsibility

## Migration Guide

### Before
```typescript
import { DataParams } from 'src/types/params';
import { DataTablePaginate } from 'src/types/dataTable';
```

### After
```typescript
import { DataParams, DataTablePaginate } from 'src/types';
```

The new structure provides better organization, improved type safety, and enhanced maintainability while maintaining backward compatibility.