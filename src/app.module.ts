import { Module } from '@nestjs/common';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { APP_INTERCEPTOR, APP_GUARD } from '@nestjs/core';
import { TypeOrmModule } from '@nestjs/typeorm';
import { DataSource } from 'typeorm';
import { ConfigModule } from '@nestjs/config';
import { CacheModule } from '@nestjs/cache-manager';
import apiConfig from './configs/apiConfig';
import mainConfig from './configs/mainConfig';
import { LoggingInterceptor } from './logging.interceptor';
import { UtilsModule } from './utils/utils.module';
import { UtilsService } from './utils/utils.service';
import { ApiModule } from './api/api.module';
import { GraylogModule } from './graylog/graylog.module';
import { AuthModule } from './auth/auth.module';
import utilsConfig from './configs/utilsConfig';
import { ProgramsModule } from './resources/programs/programs.module';
import { PermissionsModule } from './resources/permissions/permissions.module';
import { AssessmentsModule } from './resources/assessment-forms/assessments.module';
import { RolesModule } from './resources/roles/roles.module';
import { UsersModule } from './resources/users/users.module';
import { ResponsesModule } from './resources/assessment-forms/responses/responses.module';
import { FileUploadModule } from './resources/assessment-forms/utils/file-upload.module';
import { SkillsModule } from './resources/individual-develop-plans/skills/skills.module';
import { CompetenciesModule } from './resources/individual-develop-plans/competencies/competencies.module';
import { DevelopmentPlansModule } from './resources/individual-develop-plans/development-plans/development-plans.module';
import { PermissionsGuard } from './common/guards/permissions.guard';
import { AuthGuard } from './auth/auth.guard';
import { CareersModule } from './resources/individual-develop-plans/careers/careers.module';
import { CareersRecordsModule } from './resources/individual-develop-plans/career-records/career-records.module';
import { AgeWorkModule } from './resources/individual-develop-plans/age-work/age-work.module';
import { TypePlansModule } from './resources/individual-develop-plans/type-plans/type-plans.module';
import { FacultiesModule } from './resources/faculties/faculties.module';
import { PositionModule } from './resources/positions/positions.module';
import { MonitorModule } from './resources/individual-develop-plans/monitor/monitor.module';

// AUTOMATE_TEST_MODE = true -> เปิดการเชื่อม db เป็น localhost

@Module({
  imports: [
    ConfigModule.forRoot({
      load: [apiConfig, mainConfig, utilsConfig],
      envFilePath: '.development.env', //can also specify multiple paths for .env files ex. ['.env.development.local', '.env.development'],
      isGlobal: true,
    }),
    CacheModule.register({
      isGlobal: true,
    }),
    TypeOrmModule.forRoot({
      // name: 'eOfficeDev',
      type: 'mariadb',
      host:
        process.env.AUTOMATE_TEST_MODE == 'true'
          ? process.env.DB_TEST_HOST
          : process.env.DB_HOST,
      port:
        process.env.AUTOMATE_TEST_MODE == 'true'
          ? +process.env.DB_TEST_PORT
          : +process.env.DB_PORT,
      username:
        process.env.AUTOMATE_TEST_MODE == 'true'
          ? process.env.DB_TEST_USER
          : process.env.DB_USER,
      password:
        process.env.AUTOMATE_TEST_MODE == 'true'
          ? process.env.DB_TEST_PASSWORD
          : process.env.DB_PASSWORD,
      database:
        process.env.AUTOMATE_TEST_MODE == 'true'
          ? process.env.DB_TEST_NAME
          : process.env.DB_NAME,
      synchronize: false,
      migrationsRun: false, // Disabled since we don't have migration files
      autoLoadEntities: true,
      entities: [
        __dirname + '/**/*.entity{.ts,.js}', // สำหรับ NestJS/TypeScript projects
        // หรือถ้าอยากจำกัดให้ค้นหาเฉพาะใน folder src:
        // 'dist/**/*.entity.js', // สำหรับ production ที่ build แล้ว
      ],
    }),
    UtilsModule,
    ApiModule,
    GraylogModule,
    SkillsModule,
    ResponsesModule,
    AuthModule,
    ProgramsModule,
    PermissionsModule,
    RolesModule,
    UsersModule,
    AssessmentsModule,
    FileUploadModule,
    CompetenciesModule,
    DevelopmentPlansModule,
    CareersModule,
    CareersRecordsModule,
    AgeWorkModule,
    TypePlansModule,
    PositionModule,
    FacultiesModule,
    MonitorModule,

  ],
  controllers: [AppController],
  providers: [
    AppService,
    UtilsService,
    {
      provide: APP_INTERCEPTOR,
      useClass: LoggingInterceptor,
    },
    {
      provide: APP_GUARD,
      useClass: AuthGuard,
    },
    {
      provide: APP_GUARD,
      useClass: PermissionsGuard,
    },
  ],
})
export class AppModule {
  constructor(private readonly dataSource: DataSource) {}
}
