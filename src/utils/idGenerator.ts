/**
 * ID generation utilities
 */

// Internal counters
let questionIdCounter = 0;
let optionIdCounter = 0;

/**
 * Generate next question ID
 */
export function generateNextQuestionId(): number {
  questionIdCounter++;
  return questionIdCounter;
}

/**
 * Generate next option ID
 */
export function generateNextOptionId(): number {
  optionIdCounter++;
  return optionIdCounter;
}

/**
 * Reset counters
 */
export function resetIdCounters(): void {
  questionIdCounter = 0;
  optionIdCounter = 0;
}

/**
 * Set initial counter values
 */
export function initializeIdCounters(questionId = 0, optionId = 0): void {
  questionIdCounter = questionId;
  optionIdCounter = optionId;
}

/**
 * Get current counter values
 */
export function getCounterValues(): { questionId: number; optionId: number } {
  return {
    questionId: questionIdCounter,
    optionId: optionIdCounter,
  };
}
