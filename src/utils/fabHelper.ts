import { computed, nextTick } from 'vue';
import type { ItemBlock } from 'src/types/models';

// โครงสร้างสถานะของ FAB (Floating Action Button)
export interface FabPositionState {
  positionLock: boolean;
  pendingPosition: number | null;
  creationInProgress: boolean;
  targetBlockId: number | null;
}

// ตัวจัดการ timeout สำหรับ FAB
export interface TimeoutManager {
  fabPositionTimeout: NodeJS.Timeout | null;
  lockReleaseTimeout: NodeJS.Timeout | null;
  clearFabTimeout(): void;
  clearLockTimeout(): void;
  clearAll(): void;
}

// ตัวจัดการตำแหน่ง FAB
export interface FabPositionManager {
  setFabPosition(blockId: number, immediate?: boolean): void;
  createFabProtection(blockId: number, duration?: number): void;
  scrollToTarget(): void;
  setFabAndScroll(id: number): Promise<void>;
}

/**
 * Create timeout manager for FAB operations
 */
export function createTimeoutManager(): TimeoutManager {
  const manager = {
    fabPositionTimeout: null as NodeJS.Timeout | null,
    lockReleaseTimeout: null as NodeJS.Timeout | null,

    clearFabTimeout() {
      if (this.fabPositionTimeout) {
        clearTimeout(this.fabPositionTimeout);
        this.fabPositionTimeout = null;
      }
    },

    clearLockTimeout() {
      if (this.lockReleaseTimeout) {
        clearTimeout(this.lockReleaseTimeout);
        this.lockReleaseTimeout = null;
      }
    },

    clearAll() {
      this.clearFabTimeout();
      this.clearLockTimeout();
    },
  };

  return manager;
}

/**
 * ฟังก์ชันสร้างตัวจัดการตำแหน่ง FAB พร้อมการจัดการสถานะและประสิทธิภาพที่ดีขึ้น
 */
export function createFabPositionManager(
  fabState: { value: FabPositionState },
  selectedBlockId: { value: string | undefined },
  isCreatingBlock: { value: boolean },
  timeoutManager: TimeoutManager,
  getBlockRef: (id: number) => Element | null,
): FabPositionManager {
  const fabPositionLock = computed({
    get: () => fabState.value.positionLock,
    set: (value: boolean) => {
      fabState.value.positionLock = value;
    },
  });

  const pendingFabPosition = computed({
    get: () => fabState.value.pendingPosition,
    set: (value: number | null) => {
      fabState.value.pendingPosition = value;
    },
  });

  const blockCreationInProgress = computed({
    get: () => fabState.value.creationInProgress,
    set: (value: boolean) => {
      fabState.value.creationInProgress = value;
    },
  });

  const targetBlockId = computed({
    get: () => fabState.value.targetBlockId,
    set: (value: number | null) => {
      fabState.value.targetBlockId = value;
    },
  });

  // ตั้งค่าตำแหน่ง FAB
  const setFabPosition = (blockId: number, immediate = false) => {
    // ถ้ากำลังสร้าง block ใหม่และไม่ได้ immediate ให้รอไว้ก่อน
    if (isCreatingBlock.value && !immediate) {
      pendingFabPosition.value = blockId;
      return;
    }

    // ล้าง timeout เดิม
    timeoutManager.clearFabTimeout();

    if (immediate) {
      // ตั้งค่าทันทีสำหรับกรณีสำคัญ
      fabPositionLock.value = true;
      selectedBlockId.value = `block-${blockId}`;

      // ล้าง lock timeout เดิม
      timeoutManager.clearLockTimeout();

      // ตั้ง lock release timeout ใหม่
      timeoutManager.lockReleaseTimeout = setTimeout(() => {
        fabPositionLock.value = false;
        // ถ้ามี pending position ให้เปลี่ยน
        const pending = pendingFabPosition.value;
        if (pending && pending !== blockId) {
          setFabPosition(pending, false);
          pendingFabPosition.value = null;
        }
      }, 200);
    } else {
      // ตั้งค่าแบบ debounce สำหรับ interaction ปกติ
      timeoutManager.fabPositionTimeout = setTimeout(() => {
        if (!fabPositionLock.value) {
          selectedBlockId.value = `block-${blockId}`;
        } else {
          // ถ้าล็อกอยู่ ให้เก็บ pending ไว้
          pendingFabPosition.value = blockId;
        }
      }, 50);
    }
  };

  // ป้องกัน FAB ระหว่างสร้าง block
  const createFabProtection = (blockId: number, duration = 200) => {
    blockCreationInProgress.value = true;
    targetBlockId.value = blockId;
    fabPositionLock.value = true;
    selectedBlockId.value = `block-${blockId}`;

    // ล้าง protection timeout เดิม
    timeoutManager.clearLockTimeout();

    // ตั้ง protection timeout ใหม่
    timeoutManager.lockReleaseTimeout = setTimeout(() => {
      fabPositionLock.value = false;
      blockCreationInProgress.value = false;
      targetBlockId.value = null;
      // ให้ FAB อยู่กับ block ล่าสุด
      selectedBlockId.value = `block-${blockId}`;
    }, duration);
  };

  // เลื่อน scroll ไปยัง block เป้าหมาย
  const scrollToTarget = () => {
    if (!selectedBlockId.value) return;
    const id = Number(selectedBlockId.value.split('-')[1]);
    const el = getBlockRef(id);
    if (el && 'scrollIntoView' in el) {
      (el as HTMLElement).scrollIntoView({ behavior: 'smooth', block: 'center' });
    }
  };

  // ตั้ง FAB และ scroll ไปยัง block เป้าหมาย
  const setFabAndScroll = async (id: number) => {
    setFabPosition(id, true);
    await nextTick();
    await nextTick();
    scrollToTarget();
  };

  return {
    setFabPosition,
    createFabProtection,
    scrollToTarget,
    setFabAndScroll,
  };
}

/**
 * ฟังก์ชันช่วยอัปเดตลำดับ sequence ของ block
 */
export function createSequenceUpdater() {
  const updateBlockSequences = (blocks: ItemBlock[]) => {
    blocks.forEach((block, index) => {
      block.sequence = index + 1;
    });
  };

  return {
    updateBlockSequences,
  };
}

/**
 * ฟังก์ชันช่วย sync ข้อมูล block กับ assessment
 */
export function createAssessmentSynchronizer() {
  const syncBlocksWithAssessment = async (
    blocks: { value: ItemBlock[] },
    currentAssessment: { value: { itemBlocks?: ItemBlock[] } | null },
    forceRefreshBlocks: () => Promise<void>,
  ) => {
    if (currentAssessment.value && currentAssessment.value.itemBlocks) {
      // sync blocks กับ currentAssessment
      blocks.value = [...currentAssessment.value.itemBlocks];
      await forceRefreshBlocks();
    }
  };

  const updateAssessmentBlocks = (
    currentAssessment: { value: { itemBlocks?: ItemBlock[] } | null },
    blocks: ItemBlock[],
  ) => {
    if (currentAssessment.value) {
      currentAssessment.value.itemBlocks = [...blocks];
    }
  };

  return {
    syncBlocksWithAssessment,
    updateAssessmentBlocks,
  };
}

/**
 * ฟังก์ชันสร้างตัวช่วย force update สำหรับ component ที่ต้องการ trigger การ render หลายรอบ
 * จัดการ timeout ให้สะอาดขึ้นและรองรับกรณี DOM ซับซ้อน
 */
export function createForceUpdateUtility() {
  const executeForceUpdate = async (forceUpdateTrigger: { value: number }, iterations = 5) => {
    for (let i = 0; i < iterations; i++) {
      forceUpdateTrigger.value++;
      await nextTick();
    }

    // อัปเดตเพิ่มเติมแบบ delay สำหรับกรณี DOM ซับซ้อน
    const delayedUpdate = (delay: number) =>
      new Promise<void>((resolve) => {
        setTimeout(() => {
          forceUpdateTrigger.value++;
          resolve();
        }, delay);
      });

    await delayedUpdate(200);
    await nextTick();
    await delayedUpdate(100);
    await nextTick();
    await delayedUpdate(100);
  };

  return {
    executeForceUpdate,
  };
}
