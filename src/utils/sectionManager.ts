import type { ItemBlock } from 'src/types/models';

/**
 * Section management utilities
 */

/**
 * Check if a block is a section block (header with section > 0)
 */
export function isSectionBlock(blocks: ItemBlock[], index: number): boolean {
  const block = blocks[index];
  return block?.type === 'HEADER' && block.section > 0;
}

/**
 * Check if a block is a section block by ID
 */
export function isSectionBlockById(blocks: ItemBlock[], blockId: number): boolean {
  const block = blocks.find((b) => b.id === blockId);
  return block?.type === 'HEADER' && block.section > 0;
}

/**
 * Calculate total sections
 */
export function calculateTotalSections(blocks: ItemBlock[]): number {
  return blocks.filter((block) => block.type === 'HEADER' && block.section > 0).length;
}

/**
 * Get section number by index
 */
export function getSectionNumber(blocks: ItemBlock[], index: number): number {
  let sectionCounter = 0;
  for (let i = 0; i <= index; i++) {
    const block = blocks[i];
    if (block?.type === 'HEADER' && block.section > 0) {
      sectionCounter++;
    }
  }
  return sectionCounter;
}

/**
 * Get section number by sequence (for sorted blocks)
 */
export function getSectionNumberBySequence(blocks: ItemBlock[], blockSequence: number): number {
  const sortedBlocks = blocks.slice().sort((a, b) => a.sequence - b.sequence);
  let sectionCounter = 0;

  for (const block of sortedBlocks) {
    if (block.sequence > blockSequence) break;
    if (block.type === 'HEADER' && block.section > 0) {
      sectionCounter++;
    }
  }

  return sectionCounter;
}

/**
 * Get section number by block ID
 */
export function getSectionNumberById(blocks: ItemBlock[], blockId: number): number {
  const block = blocks.find((b) => b.id === blockId);
  if (!block) return 0;
  return getSectionNumberBySequence(blocks, block.sequence);
}

/**
 * Find maximum section number
 */
export function findMaxSectionNumber(blocks: ItemBlock[]): number {
  return blocks.reduce((max, block) => {
    if (block.section > 0) {
      return Math.max(max, block.section);
    }
    return max;
  }, 1);
}

/**
 * Update section numbers after deletion
 */
export function updateSectionNumbersAfterDeletion(remainingBlocks: ItemBlock[]): {
  updatedBlocks: ItemBlock[];
  sectionMap: Map<number, number>;
} {
  const headerBlocks = remainingBlocks
    .filter((block) => block.type === 'HEADER' && block.section > 0)
    .sort((a, b) => a.section - b.section);

  const sectionMap = new Map<number, number>();
  headerBlocks.forEach((header, idx) => {
    const oldSection = header.section;
    const newSection = idx + 1;
    sectionMap.set(oldSection, newSection);
    header.section = newSection;
  });

  const blocksNeedingUpdate: ItemBlock[] = [];
  remainingBlocks.forEach((block) => {
    if (block.section > 0 && sectionMap.has(block.section)) {
      const oldSection = block.section;
      const newSection = sectionMap.get(block.section)!;
      if (oldSection !== newSection) {
        block.section = newSection;
        blocksNeedingUpdate.push(block);
      }
    }
  });

  return {
    updatedBlocks: [...headerBlocks, ...blocksNeedingUpdate],
    sectionMap,
  };
}
