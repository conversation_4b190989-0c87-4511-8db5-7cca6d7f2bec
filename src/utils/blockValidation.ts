import type { Assessment } from 'src/types/models';

/**
 * Block validation utilities
 */

// Constants for validation
export const ALLOWED_BLOCK_TYPES: string[] = ['RADIO', 'CHECKBOX', 'TEXTFIELD', 'GRID'];

/**
 * Check if block type is an answer item block
 */
export function isAnswerItemBlockType(type: string): boolean {
  return ALLOWED_BLOCK_TYPES.includes(type);
}

/**
 * Validate assessment for operations
 */
export function validateAssessmentForOperation(
  assessment: Assessment | null,
  operationName: string
): boolean {
  if (!assessment?.id) {
    console.error(`❌ Assessment ID is required for ${operationName}`);
    return false;
  }
  return true;
}

/**
 * Validate assessment IDs
 */
export function validateAssessmentIds(assessment: Assessment | null): {
  valid: boolean;
  missing: string[];
} {
  const missing: string[] = [];

  if (!assessment?.id) {
    missing.push('assessmentId');
  }

  if (!assessment?.itemBlocks || assessment.itemBlocks.length === 0) {
    missing.push('itemBlocks');
  } else {
    assessment.itemBlocks.forEach((block, index) => {
      if (!block.id) {
        missing.push(`itemBlock[${index}].id`);
      }
      if (!block.assessmentId) {
        missing.push(`itemBlock[${index}].assessmentId`);
      }
    });
  }

  return {
    valid: missing.length === 0,
    missing,
  };
}

/**
 * Validate block deletion
 */
export function validateBlockDeletion(
  blockId: number,
  assessment: Assessment | null
): { canDelete: boolean; issues: string[] } {
  const issues: string[] = [];

  if (!assessment) {
    issues.push('No current assessment loaded');
    return { canDelete: false, issues };
  }

  if (!blockId) {
    issues.push('Invalid block ID provided');
    return { canDelete: false, issues };
  }

  const targetBlock = assessment.itemBlocks?.find((block) => block.id === blockId);
  if (!targetBlock) {
    issues.push(`Block with ID ${blockId} not found in current assessment`);
    return { canDelete: false, issues };
  }

  if (targetBlock.type === 'HEADER' && !targetBlock.headerBody) {
    console.warn('⚠️ Header block missing headerBody data, but deletion will proceed');
  }

  if (targetBlock.assessmentId !== assessment.id) {
    issues.push(
      `Block assessmentId (${targetBlock.assessmentId}) does not match current assessment ID (${assessment.id})`
    );
  }

  return {
    canDelete: issues.length === 0,
    issues,
  };
}

/**
 * Validate post deletion
 */
export function validatePostDeletion(
  deletedBlockId: number,
  assessment: Assessment | null
): { success: boolean; issues: string[] } {
  const issues: string[] = [];

  if (!assessment) {
    issues.push('No current assessment loaded');
    return { success: false, issues };
  }

  const blockStillExists = assessment.itemBlocks?.some((block) => block.id === deletedBlockId);
  if (blockStillExists) {
    issues.push(`Block with ID ${deletedBlockId} still exists in assessment after deletion`);
  }

  const orphanedQuestions = assessment.itemBlocks?.some((block) =>
    block.questions?.some((question) => question.itemBlockId === deletedBlockId)
  );
  if (orphanedQuestions) {
    issues.push(`Found orphaned questions referencing deleted block ID ${deletedBlockId}`);
  }

  const orphanedOptions = assessment.itemBlocks?.some((block) =>
    block.options?.some((option) => option.itemBlockId === deletedBlockId)
  );
  if (orphanedOptions) {
    issues.push(`Found orphaned options referencing deleted block ID ${deletedBlockId}`);
  }

  return {
    success: issues.length === 0,
    issues,
  };
}
