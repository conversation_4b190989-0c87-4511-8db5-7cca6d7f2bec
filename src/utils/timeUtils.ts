/**
 * Time and conversion utility functions
 */

/**
 * Convert seconds to hours and minutes
 * @param seconds - Total seconds to convert
 * @returns Object containing hours and minutes
 */
export const convertSecondsToHourMinute = (seconds: number) => {
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  return { hours, minutes };
};

/**
 * Convert hours and minutes to total seconds
 * @param hours - Number of hours
 * @param minutes - Number of minutes
 * @returns Total seconds
 */
export const convertToSeconds = (hours: number, minutes: number) => {
  return hours * 3600 + minutes * 60;
};

/**
 * Convert percentage to decimal (0-100 to 0-1)
 * @param percentage - Percentage value (0-100)
 * @returns Decimal value (0-1)
 */
export const convertToDecimal = (percentage: number): number => {
  return percentage / 100;
};

/**
 * Convert decimal to percentage (0-1 to 0-100)
 * @param decimal - Decimal value (0-1)
 * @returns Percentage value (0-100)
 */
export const convertToPercentage = (decimal: number): number => {
  return decimal * 100;
};
