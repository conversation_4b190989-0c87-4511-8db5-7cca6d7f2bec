import type { ItemBlock } from 'src/types/models';

/**
 * Block operations utilities
 */

/**
 * Update all block sequences
 */
export function updateAllBlockSequences(blocks: ItemBlock[]): void {
  blocks.forEach((block, index) => {
    block.sequence = index + 1;
  });
}

/**
 * Insert block with sequence management
 */
export function insertBlockWithSequenceManagement(
  blocks: ItemBlock[],
  newBlock: ItemBlock,
  targetIndex: number
): ItemBlock[] {
  // Validate index
  if (targetIndex < 0 || targetIndex >= blocks.length) {
    newBlock.sequence = blocks.length + 1;
    return [...blocks, newBlock];
  }

  const currentBlock = blocks[targetIndex];
  if (!currentBlock) {
    newBlock.sequence = blocks.length + 1;
    return [...blocks, newBlock];
  }

  const insertSequence = currentBlock.sequence + 1;

  // Update sequences of blocks after insertion point
  const updatedBlocks = blocks.map((block) => ({
    ...block,
    sequence: block.sequence >= insertSequence ? block.sequence + 1 : block.sequence,
  }));

  // Set sequence for new block
  newBlock.sequence = insertSequence;

  // Insert block at correct position
  const result = [...updatedBlocks];
  result.splice(targetIndex + 1, 0, newBlock);
  return result;
}

/**
 * Create duplicate block
 */
export function createDuplicateBlock(
  blocks: ItemBlock[],
  sourceBlock: ItemBlock,
  insertionIndex: number
): ItemBlock[] {
  const duplicatedBlock: ItemBlock = { ...sourceBlock };
  const result = [...blocks];
  result.splice(insertionIndex + 1, 0, duplicatedBlock);
  return result;
}

/**
 * Replace block at index
 */
export function replaceBlockAtIndex(
  blocks: ItemBlock[],
  updatedBlock: ItemBlock,
  targetIndex: number
): ItemBlock[] {
  const result = [...blocks];
  result[targetIndex] = updatedBlock;
  return result;
}

/**
 * Remove block from list
 */
export function removeBlockFromList(blocks: ItemBlock[], targetIndex: number): {
  updatedBlocks: ItemBlock[];
  removedBlock: ItemBlock | null;
} {
  if (targetIndex < 0 || targetIndex >= blocks.length) {
    return { updatedBlocks: blocks, removedBlock: null };
  }

  const result = [...blocks];
  const [removedBlock] = result.splice(targetIndex, 1);
  return { updatedBlocks: result, removedBlock: removedBlock || null };
}

/**
 * Update block section
 */
export function updateBlockSection(
  blocks: ItemBlock[],
  sectionNumber: number,
  blockIndex: number
): ItemBlock[] {
  if (blockIndex < 0 || blockIndex >= blocks.length) {
    return blocks;
  }

  const result = [...blocks];
  result[blockIndex] = {
    ...result[blockIndex],
    section: sectionNumber,
  } as ItemBlock;
  return result;
}

/**
 * Reorder blocks and update sequences
 */
export function reorderBlocksAndUpdateSequences(reorderedBlocks: ItemBlock[]): ItemBlock[] {
  const result = [...reorderedBlocks];
  updateAllBlockSequences(result);
  return result;
}

/**
 * Add blocks to end
 */
export function addBlocksToEnd(currentBlocks: ItemBlock[], newBlocks: ItemBlock[]): ItemBlock[] {
  return [...currentBlocks, ...newBlocks];
}

/**
 * Update option text in blocks
 */
export function updateOptionTextInBlocks(
  blocks: ItemBlock[],
  optionId: number,
  newText: string
): ItemBlock[] {
  return blocks.map((block) => {
    if (block.options) {
      const updatedOptions = block.options.map((option) =>
        option.id === optionId ? { ...option, optionText: newText } : option
      );
      return { ...block, options: updatedOptions };
    }
    return block;
  });
}
