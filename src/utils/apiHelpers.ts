import type { AxiosError } from 'axios';
import { getAssessmentErrorMessage } from 'src/utils/assessmentErrors';
import { showSuccess, showError } from 'src/utils/notifications';
import { useGlobalStore } from 'src/stores/global';

/**
 * API error handling utilities
 */

export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
}

/**
 * Handle API errors consistently
 */
export function handleApiError(error: unknown, operation: string): never {
  const axiosError = error as AxiosError;
  const errorMessage = getAssessmentErrorMessage(axiosError, operation);
  showError(errorMessage);
  throw new Error(errorMessage);
}

/**
 * Show success message and trigger loading state
 */
export function handleApiSuccess(message: string): void {
  const globalStore = useGlobalStore();
  globalStore.Loading();
  showSuccess(message);
}

/**
 * Just trigger loading state without message
 */
export function triggerLoading(): void {
  const globalStore = useGlobalStore();
  globalStore.Loading();
}

/**
 * Wrapper for API calls with consistent error handling
 */
export async function apiCall<T>(
  operation: () => Promise<T>,
  errorOperation: string
): Promise<T> {
  try {
    return await operation();
  } catch (error) {
    handleApiError(error, errorOperation);
  }
}

/**
 * Wrapper for API calls that return data
 */
export async function apiCallWithData<T>(
  operation: () => Promise<{ data: T }>,
  errorOperation: string
): Promise<T> {
  try {
    const response = await operation();
    return response.data;
  } catch (error) {
    handleApiError(error, errorOperation);
  }
}
