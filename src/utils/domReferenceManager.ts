/**
 * DOM reference management utilities
 */

type DOMRefElement = Element | null;

/**
 * Create DOM reference manager
 */
export function createDOMReferenceManager() {
  const blockDOMReferences: Record<number, DOMRefElement> = {};

  /**
   * Set block DOM reference
   */
  function setBlockDOMReference(blockId: number, element: DOMRefElement): void {
    blockDOMReferences[blockId] = element;
  }

  /**
   * Get block DOM reference
   */
  function getBlockDOMReference(blockId: number): DOMRefElement {
    return blockDOMReferences[blockId] || null;
  }

  /**
   * Clear all references
   */
  function clearAllReferences(): void {
    Object.keys(blockDOMReferences).forEach((key) => {
      delete blockDOMReferences[Number(key)];
    });
  }

  /**
   * Remove specific reference
   */
  function removeReference(blockId: number): void {
    delete blockDOMReferences[blockId];
  }

  /**
   * Get all references
   */
  function getAllReferences(): Record<number, DOMRefElement> {
    return { ...blockDOMReferences };
  }

  return {
    setBlockDOMReference,
    getBlockDOMReference,
    clearAllReferences,
    removeReference,
    getAllReferences,
  };
}
