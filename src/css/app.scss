// * app global css in SCSS form

@import url('./font.css');

* {
  font-family: 'Sarabun', sans-serif;
}

.card-form {
  min-width: 500px;
}

.header-dialog {
  font-size: 30px;
  font-weight: medium;
}

.header {
  font-size: 20px;
}

.body {
  font-size: 18px;
}

.sub-body {
  font-size: 16px;
  color: #dddddd;
}

.container {
  width: 100%;
  max-width: 1024px;
}

.wrapper {
  max-width: 1024px;
  margin: auto;
}

.bg-page-primary {
  background: $surface-primary;
}

.evaluate-item {
  margin: auto;
  min-height: 200px;
  max-width: 900px;
  min-width: 900px;
  margin-bottom: 16px;
  width: 100%;
}

.evaluate-get {
  margin: auto;
  max-width: 900px;
  min-width: 900px;
  margin-bottom: 16px;
  width: 100%;
  display: block;
  margin-left: auto;
  margin-right: auto;
}

@media (max-width: 1023.98px) {
  .evaluate-item {
    min-width: 600px;
  }

  .evaluate-get {
    min-width: 300px;
  }
}

.q-btn {
  border-radius: $generic-border-radius;
  justify-content: center;
  align-items: center;
}

.q-btn::before {
  box-shadow: none;
}

.q-card {
  box-shadow: none;
  outline: $surface;
  border: 1px solid $surface;
}

.text-body1 {
  font-size: 16px;
}

.q-header {
  background: $secondary;
}

// Unified Q-Table Styles
// =============================================

/* Table Container Styles */
.q-table {
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  table-layout: fixed !important;
  margin: 0 !important;

  &:hover {
    box-shadow: 0 6px 15px rgba(0, 0, 0, 0.1);
  }
}

.q-table__container {
  max-width: 100% !important;
  width: 100% !important;
  overflow: hidden !important;
}

.q-table__middle,
.q-table__bottom,
.q-table__top {
  padding: 0 !important;
  overflow: hidden !important;
}

.q-table__bottom {
  border-top: 1px solid rgba(0, 0, 0, 0.12);
  min-height: 48px;
}

/* Table Header Styles */
.q-table thead th {
  font-size: 1.1rem;
  padding: 16px;
  background: #ffca28 !important;
  color: #000000 !important;
  font-weight: 600;
  border-bottom: 2px solid #ffa000;
  transition: all 0.3s ease;
  user-select: none;
  cursor: pointer;
  white-space: nowrap;

  &:hover {
    background: #ffa000 !important;
  }

  &[aria-sort]:not([aria-sort='none']) .q-table__sort-icon {
    color: #ffa000 !important;
    opacity: 1;
  }

  &[aria-sort]::after {
    content: '';
    display: inline-block;
    width: 18px;
    height: 1px;
    vertical-align: middle;
    margin-left: 6px;
  }
}

/* Table Body Styles */
.q-table tbody {
  td {
    font-size: 1.1rem;
    padding: 12px;
    vertical-align: middle;
    line-height: 1.6;
    transition: background-color 0.3s ease;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  tr:hover td {
    background-color: rgba(255, 202, 40, 0.1) !important;
  }

  tr {
    height: 40px !important;
    opacity: 0;
    animation: fadeIn 0.3s ease-out forwards;

    // Animation delay for rows
    @for $i from 1 through 10 {
      &:nth-child(#{$i}) {
        animation-delay: #{$i * 0.05}s;
      }
    }
  }
}

.q-table thead tr {
  height: 48px !important;
  background-color: var(--q-primary) !important;
  color: black !important;
  text-align: center !important;
}

/* Table Action Button Styles */
.view-icon,
.edit-graph-icon,
.del-icon,
.manage-role-icon,
.manage-dept-icon,
.manage-perm-icon {
  transition: all 0.3s ease;
  padding: 7px;
  border-radius: 8px;
  min-width: 36px;
  height: 36px;

  .q-icon {
    font-size: 20px;
  }
}

.view-icon {
  background-color: $accent-dark;
  color: $text-white;
  &:hover {
    transform: scale(1.05);
  }
}

.edit-graph-icon {
  background-color: var(--q-accent);
  color: $text-white;
  &:hover {
    transform: scale(1.1);
  }
}

.del-icon {
  background-color: $negative;
  color: $text-white;
  &:hover {
    transform: scale(1.1);
  }
}

.manage-role-icon {
  background-color: $primary;
  color: $text-black;
  &:hover {
    transform: scale(1.1);
  }
}

.manage-dept-icon {
  background-color: $secondary;
  color: $text-black;
  &:hover {
    transform: scale(1.1);
  }
}

.manage-perm-icon {
  background-color: $color-system-hover;
  color: $text-white;
  &:hover {
    transform: scale(1.1);
  }
}

/* Remove scroll behaviors */
// .scroll,
// .q-virtual-scroll__content {
//   overflow: hidden !important;
// }

.q-table th,
.q-table td {
  padding: 8px 12px !important;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* Standard column widths */
.q-table th:nth-child(1),
.q-table td:nth-child(1) {
  width: 100px;
  max-width: fit-content !important;
}

.q-checkbox:not(.q-checkbox--truthy) .q-checkbox__bg {
  border: 2px solid $primary !important;
}

// .q-table th:nth-child(2),
// .q-table td:nth-child(2) {
//   width: 25% !important;
// }

// .q-table th:nth-child(3),
// .q-table td:nth-child(3) {
//   width: 15% !important;
// }

// .q-table th:nth-child(4),
// .q-table td:nth-child(4) {
//   width: 20% !important;
// }

// .q-table th:nth-child(5),
// .q-table td:nth-child(5) {
//   width: 10% !important;
// }

// .q-table th:nth-child(6),
// .q-table td:nth-child(6) {
//   width: 180px !important;
//   min-width: 180px !important;
// }

/* Responsive Design */
@media screen and (max-width: 600px) {
  .q-table {
    font-size: 0.9rem;

    thead th {
      font-size: 0.95rem;
      padding: 8px 4px;
    }

    tbody td {
      font-size: 0.9rem;
      padding: 8px 4px;
    }
  }

  .view-icon,
  .edit-graph-icon,
  .del-icon,
  .manage-role-icon,
  .manage-dept-icon,
  .manage-perm-icon {
    padding: 4px;
  }
}

/* Table Animation */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
