import { CACHE_MANAGER } from '@nestjs/cache-manager';
import {
  Inject,
  Injectable,
  // NotFoundException,
  UnauthorizedException,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { JwtService } from '@nestjs/jwt';
import { InjectRepository } from '@nestjs/typeorm';
import { Cache } from 'cache-manager';
import { ApiService } from 'src/api/api.service';

import { GraylogService } from 'src/graylog/graylog.service';
import { UtilsService } from 'src/utils/utils.service';
import { Repository } from 'typeorm';
import * as bcrypt from 'bcrypt';
import { User } from 'src/resources/users/entities/user.entity';

@Injectable()
export class AuthService {
  constructor(
    private readonly configService: ConfigService,
    private readonly jwtService: JwtService,
    private readonly apiService: ApiService,
    @Inject(CACHE_MANAGER)
    private readonly cacheManager: Cache,
    private readonly utilsService: UtilsService,
    private readonly graylogService: GraylogService,
    @InjectRepository(User)
    private readonly userRepo: Repository<User>,
  ) {}

  // New simple login method without encryption
  async simpleSignIn(username: string, password: string): Promise<any> {
    const oneWeekInSeconds = 7 * 24 * 60 * 60;
    const encryptionKey = this.configService.get<string>('ENCRYPTION_KEY') || 'E13qDGn!Z|"38y/BUvFjl$cA-vs4)P';

    console.log('Simple login attempt:', username);

    // Find user by email with faculty roles and permissions using query builder
    const user = await this.userRepo
      .createQueryBuilder('user')
      .leftJoinAndSelect('user.facultyUsers', 'facultyUsers')
      .leftJoinAndSelect('facultyUsers.faculty', 'faculty')
      .leftJoinAndSelect('facultyUsers.facultyUserRoles', 'facultyUserRoles')
      .leftJoinAndSelect('facultyUserRoles.role', 'role')
      .leftJoinAndSelect('role.permissions', 'permissions')
      .where('user.email = :email', { email: username })
      .getOne();

    if (!user) {
      throw new UnauthorizedException('ไม่พบบัญชีผู้ใช้');
    }

    // Check password with bcrypt
    const passwordMatch = await bcrypt.compare(password, user.password);

    if (!passwordMatch) {
      throw new UnauthorizedException('ชื่อผู้ใช้หรือรหัสผ่านไม่ถูกต้อง');
    }

    console.log('User authenticated successfully:', user.email);

    // Create and return auth token
    return this.createAuthToken(user, encryptionKey, oneWeekInSeconds);
  }

  async signIn(username: string, password: string): Promise<any> {
    const oneWeekInSeconds = 7 * 24 * 60 * 60;
    const encryptionKey = this.configService.get<string>('ENCRYPTION_KEY') || 'E13qDGn!Z|"38y/BUvFjl$cA-vs4)P';

    console.log('Raw username:', username);
    console.log('Raw password:', password);

    const decryptedUsername = await this.utilsService.decryptString(
      'loginBuu_username',
      username,
    );
    const decryptedPassword = await this.utilsService.decryptString(
      'loginBuu_password',
      password,
    );
    console.log(
      'decryptedUsername',
      decryptedUsername,
      'decryptedPassword',
      decryptedPassword,
    );

    const user = await this.userRepo
      .createQueryBuilder('user')
      .leftJoinAndSelect('user.facultyUsers', 'facultyUsers')
      .leftJoinAndSelect('facultyUsers.faculty', 'faculty')
      .leftJoinAndSelect('facultyUsers.facultyUserRoles', 'facultyUserRoles')
      .leftJoinAndSelect('facultyUserRoles.role', 'role')
      .leftJoinAndSelect('role.permissions', 'permissions')
      .where('user.email = :email', { email: decryptedUsername })
      .getOne();

    if (!user) {
      throw new UnauthorizedException('ไม่พบบัญชีผู้ใช้');
    }

    // decrypt password bcrypt
    const passwordMatch = await bcrypt.compare(
      decryptedPassword,
      user.password,
    );

    if (!passwordMatch) {
      throw new UnauthorizedException('ชื่อผู้ใช้หรือรหัสผ่านไม่ถูกต้อง');
    }
    console.log('user', user);

    return this.createAuthToken(user, encryptionKey, oneWeekInSeconds);
  }

  private async createAuthToken(
    user: User,
    encryptionKey: string,
    ttlSeconds: number,
  ) {
    // Get all roles and permissions from faculty users
    const allRoles = user.facultyUsers?.flatMap(facultyUser => 
      facultyUser.facultyUserRoles?.map(facultyUserRole => facultyUserRole.role) || []
    ) || [];

    // Get all faculties that user belongs to with their roles
    const faculties = user.facultyUsers?.map(facultyUser => ({
      id: facultyUser.faculty.id,
      nameTh: facultyUser.faculty.nameTh,
      nameEn: facultyUser.faculty.nameEn,
      roles: facultyUser.facultyUserRoles?.map(facultyUserRole => ({
        id: facultyUserRole.role.id,
        name: facultyUserRole.role.name,
        permissions: (facultyUserRole.role.permissions ?? [])
          .filter((permission) => permission.status === true)
          .map((permission) => ({
            perId: permission.id,
            perName: permission.name,
            perDescEn: permission.descEn,
            perDescTh: permission.descTh,
          })),
      })) || [],
    })) || [];

    const userPayload = {
      id: user.id,
      name: user.name,
      firstName: user.firstName,
      lastName: user.lastName,
      email: user.email,
      faculties,
    };

    console.log('userPayload', userPayload);

    const encryptedUser = this.utilsService.encryptObject(
      encryptionKey,
      userPayload,
    );

    await this.cacheManager.set(
      `${user.email}_encryptedUser`,
      encryptedUser,
      ttlSeconds,
    );

    // private async createAuthToken(
    //   username: string,
    //   user: any,
    //   encryptionKey: string,
    //   ttlSeconds: number,
    // ) {
    //   const encryptedUser = this.utilsService.encryptObject(encryptionKey, user);
    //   await this.cacheManager.set(
    //     ${username}_encryptedUser,
    //     encryptedUser,
    //     ttlSeconds,
    //   );

    //   const token = await this.jwtService.signAsync({
    //     sub: encryptedUser,
    //   });
    //   await this.cacheManager.set(${username}_encryptedTK, token, ttlSeconds);

    const token = await this.jwtService.signAsync({
      sub: encryptedUser,
    });
    await this.cacheManager.set(`${user.email}_encryptedTK`, token, ttlSeconds);

    console.log(token + ' ' + encryptedUser + ' ' + user.email + ' ' + token);
    return {
      access_token: token,
    };
  }

  // Verify and decode JWT token for debugging
  async verifyAndDecodeToken(token: string): Promise<any> {
    try {
      const encryptionKey = this.configService.get<string>('ENCRYPTION_KEY') || 'E13qDGn!Z|"38y/BUvFjl$cA-vs4)P';

      // Verify and decode JWT
      const decoded = await this.jwtService.verifyAsync(token);
      console.log('JWT decoded payload:', decoded);

      // Decrypt the user data
      const decryptedUser = this.utilsService.decryptObject(
        encryptionKey,
        decoded.sub,
      );
      console.log('Decrypted user data:', decryptedUser);

      return {
        success: true,
        jwt_payload: decoded,
        user_data: decryptedUser,
      };
    } catch (error) {
      console.error('Token verification error:', error);
      throw new UnauthorizedException('Invalid or expired token');
    }
  }

  private async handleSignInError(
    error: any,
    username: string,
    password: string,
    userFullName: string,
    apiName: string,
  ): Promise<void> {
    const logData = {
      efLogRequest: 'Request... POST /auth/loginBuu',
      efLogRequestBody: JSON.stringify({ username, password }),
      efLogResStatusCode: error.response?.status?.toString() || '',
      efLogResMessage: error.toString(),
      efLogReqUser: userFullName,
    };

    await this.graylogService.error(
      `${logData.efLogRequest} ${logData.efLogResMessage}`,
      logData.efLogRequestBody,
      'from_web',
    );

    const errorMsg = error.response?.data?.message;
    const errorType = error.response?.data?.error;

    if (errorType === 'invalid_token' || error.response?.status === 401) {
      await this.apiService.refreshToken(apiName);
      return this.signIn(username, password); // Retry
    } else if (errorMsg === 'ชื่อผู้ใช้หรือรหัสผ่านไม่ถูกต้อง') {
      await this.apiService.sendNoti(
        `${this.configService.get<string>('noti')}${apiName}: ${error}`,
      );
      throw new UnauthorizedException('ชื่อผู้ใช้หรือรหัสผ่านไม่ถูกต้อง');
    } else {
      await this.apiService.sendNoti(
        `${this.configService.get<string>('noti')}${apiName}: ${error}`,
      );
      console.error(error);
    }
  }
}
