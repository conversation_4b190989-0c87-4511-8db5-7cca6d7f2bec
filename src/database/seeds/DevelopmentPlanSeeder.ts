import { DataSource } from 'typeorm';
import { Seeder } from 'typeorm-extension';
import { DevelopmentPlan } from '../../resources/individual-develop-plans/development-plans/entities/development-plan.entity';

export default class DevelopmentPlanSeeder implements Seeder {
  public async run(dataSource: DataSource): Promise<any> {
    const repository = dataSource.getRepository(DevelopmentPlan);

    const developmentPlansData = [
      {
        name: 'Annual Performance Review Plan 2024',
        description: 'Comprehensive development plan for 2024 performance reviews.',
        isActive: true,
        parentId: null,
        facId: null,
        ageWorkCriteriaId: null,
      },
      {
        name: 'Leadership Development Program Q3',
        description: 'Targeted development for emerging leaders in Q3.',
        isActive: true,
        parentId: null,
        facId: null,
        ageWorkCriteriaId: null,
      },
      {
        name: 'Technical Skill Enhancement - Backend',
        description: 'Plan to enhance backend development skills for the engineering team.',
        isActive: true,
        parentId: null,
        facId: null,
        ageWorkCriteriaId: null,
      },
      {
        name: 'Customer Service Excellence Training',
        description: 'Training program to improve customer service interactions.',
        isActive: false,
        parentId: null,
        facId: null,
        ageWorkCriteriaId: null,
      },
    ];

    await repository.save(developmentPlansData);
  }
}