<script setup lang="ts">
import AppBreadcrumb from 'src/components/AppBreadcrumb.vue';
import MainFooter from 'src/components/MainFooter.vue';
import { useRoute } from 'vue-router';
import { computed, ref } from 'vue';

import MainHeader from 'src/components/MainHeader.vue';
import LeftDrawer from 'src/components/common/LeftDrawer.vue';
import ConfirmDialog from 'src/components/ConfirmDialog.vue';

import { useAuthStore } from 'src/stores/auth';
import { roleMap } from 'src/data/roleMap';

const route = useRoute();

const authStore = useAuthStore();
const confirmLogout = ref(false);

// Dynamically determine menu type based on current route path
// const menuType = computed(() => {
//   const queryType = route.query.type as MenuType;
//   if (queryType) return queryType;
//
//   const path = route.path;
//   if (path.startsWith('/evaluate')) return 'form';
//   if (path.startsWith('/quiz')) return 'quiz';
//   if (path.startsWith('/ums')) return 'ums';
//   if (path.startsWith('/competency')) return 'competency';
//   if (path.startsWith('/skill')) return 'skill';
//   if (path.startsWith('/idp')) return 'idp';
//   if (path.startsWith('/in-house')) return 'in-house';
//   if (path.startsWith('/developments')) return 'developments';
//   if (path.startsWith('/individual-development')) return 'individual-development';
//
//   return 'default';
// });

// --- Refactored breadcrumbs: use route meta.breadcrumb ---
import type { RouteLocationMatched } from 'vue-router';

const breadcrumbItems = computed(() => {
  // Only include routes with meta.breadcrumb
  return route.matched
    .filter((r: RouteLocationMatched) => r.meta && r.meta.breadcrumb)
    .map((r: RouteLocationMatched) => {
      const bc = r.meta.breadcrumb;
      if (typeof bc === 'function') {
        return {
          title: bc(route),
          link: r.path ? route.fullPath.split(r.path)[0] + r.path : '',
        };
      } else if (bc && typeof bc === 'object' && 'label' in bc) {
        return {
          title: (bc as { label: string }).label,
          link: (bc as { link?: string }).link || (r.path ? route.fullPath.split(r.path)[0] + r.path : ''),
        };
      } else {
        return {
          title: bc,
          link: r.path ? route.fullPath.split(r.path)[0] + r.path : '',
        };
      }
    });
});

const activeMenu = computed(() => {
  const roleName = authStore.currentRoleName ?? 'Standard User';
  return roleMap[roleName] || [];
});

const handleLogout = () => {
  authStore.logout(); // เรียก logout จาก authStore
};
</script>

<template>
  <q-layout view="hHh lpR fff">
    <MainHeader />
    <q-page-container class="page-container">
      <LeftDrawer :menu="activeMenu" @logout="confirmLogout = true" />
      <AppBreadcrumb :items="breadcrumbItems" class="q-px-lg q-py-md" />
      <div class="scrollable-content">
        <router-view />
      </div>
    </q-page-container>

    <MainFooter />
    <ConfirmDialog v-model="confirmLogout" title="ยืนยันการออกจากระบบ" @confirm="handleLogout" />
  </q-layout>
</template>

<style scoped>
.page-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  overflow: hidden;
}

.scrollable-content {
  flex: 1 1 auto;
  overflow-y: auto;
}
</style>
