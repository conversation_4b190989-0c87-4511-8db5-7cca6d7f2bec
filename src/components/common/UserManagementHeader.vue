<template>
  <div class="top-header-table">
    <div class="row q-mt-sm items-center">
      <div class="col-auto">
        <div class="text-h6">{{ title }}</div>
      </div>
    </div>
    <div class="row items-center">
      <div class="col-auto">
        <slot name="tab" />
      </div>
    </div>
    <div class="row q-mt-md q-mb-sm items-center justify-end q-pr-none">
      <div class="row items-center q-gutter-md q-pr-none">
        <div v-if="showRoleFilter">
          <q-select
            v-model="selectedRole"
            outlined
            dense
            style="width: 220px"
            label="บทบาท"
            :options="roleOptions"
            option-label="name"
            option-value="id"
            emit-value
            map-options
            clearable
            @update:model-value="handleRoleFilterChange"
          />
        </div>
        <div v-if="showSearch">
          <SearchBar @search="handleSearch" />
        </div>
        <div v-if="showCreateButton">
          <q-btn :label="createButtonLabel" color="accent" icon="add" @click="handleCreate" />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import SearchBar from 'src/components/SearchBar.vue';
import { ref, onMounted } from 'vue';
import type { Role } from 'src/types/models';

interface Props {
  title: string;
  createButtonLabel?: string;
  showCreateButton?: boolean;
  showSearch?: boolean;
  showDepartmentFilter?: boolean;
  showRoleFilter?: boolean;
  subtitle?: string;
  roleOptions?: Role[];
}

interface Emits {
  (e: 'search', keyword: string): void;
  (e: 'create'): void;
  (e: 'role-filter-change', roleId: number | null): void;
}

const props = withDefaults(defineProps<Props>(), {
  createButtonLabel: 'สร้าง',
  showCreateButton: true,
  showSearch: true,
  showDepartmentFilter: false,
  showRoleFilter: false,
  roleOptions: () => [],
});
const selectedRole = ref<number | null>(null);

onMounted(() => {
  console.log(props.title);
});
const emit = defineEmits<Emits>();

const handleSearch = (keyword: string) => {
  emit('search', keyword);
};

const handleCreate = () => {
  emit('create');
};

const handleRoleFilterChange = (roleId: number | null) => {
  emit('role-filter-change', roleId);
};
</script>
