<template>
  <q-dialog v-model="modelValue" persistent>
    <q-card
      class="q-pa-md q-mx-sm"
      style="
        border-radius: 12px;
        width: 100%;
        max-width: 1000px;
        height: 700px;
        display: flex;
        flex-direction: column;
      "
    >
      <!-- Header -->
      <q-card-section>
        <div class="text-h6">สลับบทบาท</div>
        <div class="text-caption text-grey">เลือกบทบาทและหน่วยงานที่ต้องการใช้งาน</div>
      </q-card-section>
      <!-- Content -->
      <q-card-section style="flex: 1; overflow-y: auto">
        <div class="row justify-center" style="gap: 24px; height: 100%">
          <!-- Left Side - Role Selection -->
          <div class="col-12 col-md-6 flex-1 selection-container">
            <div class="text-subtitle1 text-weight-medium q-mb-md">
              <q-icon name="admin_panel_settings" class="q-mr-sm" />
              เลือกบทบาท
            </div>
            <div class="q-gutter-sm">
              <div v-if="availableRoles.length === 0" class="text-center text-grey q-py-lg">
                <div>ไม่พบบทบาทที่สามารถใช้งานได้</div>
                <div class="text-caption q-mt-sm">
                  Debug: User object = {{ user ? 'Found' : 'Not found' }}
                </div>
                <div class="text-caption" v-if="user">
                  Departments: {{ user.departments?.length || 0 }} items<br />
                  FacDepUserRoles: {{ user.facDepUserRoles?.length || 0 }} items<br />
                  Faculties: {{ user.faculties?.length || 0 }} items
                </div>
              </div>

              <div v-else>
                <q-card
                  v-for="role in availableRoles"
                  :key="role"
                  class="cursor-pointer transition-all q-mb-sm"
                  :class="selectedRole === role ? 'bg-primary text-white' : 'bg-grey-2'"
                  @click="selectRole(role)"
                >
                  <q-card-section class="q-pa-md">
                    <div class="row items-center justify-between">
                      <div>
                        <div class="text-subtitle2 text-weight-medium">
                          {{ role }}
                        </div>
                        <div
                          class="text-caption"
                          :class="selectedRole === role ? 'text-white' : 'text-grey-6'"
                        >
                          บทบาทผู้ใช้งาน
                        </div>
                      </div>
                      <q-icon
                        v-if="selectedRole === role"
                        name="check_circle"
                        size="sm"
                        color="white"
                      />
                      <q-icon
                        v-else-if="currentRole === role"
                        name="radio_button_checked"
                        size="sm"
                        color="primary"
                      />
                      <q-icon v-else name="radio_button_unchecked" size="sm" color="grey-5" />
                    </div>
                  </q-card-section>
                </q-card>
              </div>
            </div>
          </div>

          <!-- Right Side - Faculty Selection -->
          <div class="col-12 col-md-6 flex-1 selection-container">
            <div class="text-subtitle1 text-weight-medium q-mb-md">
              <q-icon name="apartment" class="q-mr-sm" />
              เลือกหน่วยงาน
            </div>
            <div class="q-gutter-sm">
              <div v-if="!selectedRole" class="text-center text-grey q-py-lg">
                กรุณาเลือกบทบาทก่อน
              </div>

              <div
                v-else-if="availableFaculties.length === 0"
                class="text-center text-grey q-py-lg"
              >
                ไม่พบหน่วยงานสำหรับบทบาทนี้
              </div>

              <div v-else>
                <q-card
                  v-for="(faculty, index) in availableFaculties"
                  :key="faculty?.id || `faculty-${index}`"
                  class="cursor-pointer transition-all q-mb-sm"
                  :class="
                    selectedFaculty?.id === faculty?.id ? 'bg-secondary text-white' : 'bg-grey-2'
                  "
                  @click="faculty && selectFaculty(faculty)"
                >
                  <q-card-section class="q-pa-md">
                    <div class="row items-center justify-between">
                      <div>
                        <div class="text-subtitle2 text-weight-medium">
                          {{ faculty?.name || faculty?.nameTh || 'ไม่ระบุชื่อ' }}
                        </div>
                        <div
                          class="text-caption"
                          :class="
                            selectedFaculty?.id === faculty?.id ? 'text-white' : 'text-grey-6'
                          "
                        >
                          หน่วยงาน
                        </div>
                      </div>
                      <q-icon
                        v-if="selectedFaculty?.id === faculty?.id"
                        name="check_circle"
                        size="sm"
                        color="white"
                      />
                      <q-icon
                        v-else-if="currentFaculty?.id === faculty?.id"
                        name="radio_button_checked"
                        size="sm"
                        color="secondary"
                      />
                      <q-icon v-else name="radio_button_unchecked" size="sm" color="grey-5" />
                    </div>
                  </q-card-section>
                </q-card>
              </div>
            </div>
          </div>
        </div>
      </q-card-section>

      <!-- Actions -->
      <q-card-actions align="right" class="q-px-md">
        <div class="q-gutter-sm">
          <q-btn
            label="ยกเลิก"
            color="grey-7"
            @click="cancel"
            icon="close"
            flat
            style="border: 1px solid rgba(0, 0, 0, 0.3)"
          />
          <q-btn
            label="ยืนยัน"
            icon="check"
            color="positive"
            :disable="
              !selectedRole ||
              !selectedFaculty ||
              (selectedRole === currentRole && selectedFaculty?.id === currentFaculty?.id)
            "
            @click="confirm"
          />
        </div>
      </q-card-actions>
    </q-card>
  </q-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import { useAuthStore } from 'src/stores/auth';
import type { Faculty } from 'src/types/models';

// Props & Emits
const props = defineProps<{
  modelValue: boolean;
}>();

const emit = defineEmits<{
  (e: 'update:modelValue', value: boolean): void;
  (
    e: 'roleChanged',
    value: {
      role: string;
      facultyId: string; // Changed to string
      previousRole?: string;
      previousFacultyId?: string; // Changed to string
    },
  ): void;
}>();

// State
const authStore = useAuthStore();
const selectedRole = ref<string | null>(null);
const selectedFaculty = ref<Faculty | null>(null);

// Computed
const modelValue = computed({
  get: () => props.modelValue,
  set: (value: boolean) => emit('update:modelValue', value),
});

const currentRole = computed(() => authStore.currentRoleName);

const user = computed(() => {
  const currentUser = authStore.getCurrentUser();
  console.log('RoleSwitchDialog - Current user:', currentUser);
  console.log('RoleSwitchDialog - User departments:', currentUser?.departments);
  console.log('RoleSwitchDialog - User facDepUserRoles:', currentUser?.facDepUserRoles);
  console.log('RoleSwitchDialog - User faculties:', currentUser?.faculties);
  return currentUser;
});

const currentFaculty = computed(() => {
  const user = authStore.getCurrentUser();
  if (!user || !currentRole.value) return null;

  // Try new departments structure first
  if (user.departments) {
    const facultyId = authStore.currentFacultyId || localStorage.getItem('currentFacultyId');

    if (facultyId) {
      const departmentById = user.departments.find(
        (department) =>
          department.faculty?.id === facultyId && department.role?.name === currentRole.value,
      );
      if (departmentById?.faculty) return departmentById.faculty;
    }

    // Fallback to first faculty with current role
    const firstDepartment = user.departments.find(
      (department) => department.role?.name === currentRole.value,
    );
    return firstDepartment?.faculty || null;
  }

  // Try facDepUserRoles structure next
  if (user.facDepUserRoles) {
    const facultyId = authStore.currentFacultyId || localStorage.getItem('currentFacultyId');

    if (facultyId) {
      const facDepRoleById = user.facDepUserRoles.find(
        (facDepRole) =>
          facDepRole.department?.faculty?.id === facultyId &&
          facDepRole.role?.name === currentRole.value,
      );
      if (facDepRoleById?.department?.faculty) return facDepRoleById.department.faculty;
    }

    // Fallback to first faculty with current role
    const firstFacDepRole = user.facDepUserRoles.find(
      (facDepRole) => facDepRole.role?.name === currentRole.value,
    );
    return firstFacDepRole?.department?.faculty || null;
  }

  // Fallback to legacy structure
  if (user.faculties) {
    const facultyId = authStore.currentFacultyId || localStorage.getItem('currentFacultyId');

    if (facultyId) {
      const facultyById = user.faculties.find(
        (faculty) =>
          faculty.id === facultyId &&
          faculty.roles?.some((role) => role.name === currentRole.value),
      );
      if (facultyById) return facultyById;
    }

    return (
      user.faculties.find((faculty) =>
        faculty.roles?.some((role) => role.name === currentRole.value),
      ) || null
    );
  }

  return null;
});

const availableRoles = computed(() => {
  console.log('RoleSwitchDialog - Computing available roles...');

  // Try new departments structure first
  if (user.value?.departments) {
    console.log('RoleSwitchDialog - Using departments structure');
    const allRoles = user.value.departments
      .map((department) => department.role?.name)
      .filter(Boolean) as string[];
    const uniqueRoles = [...new Set(allRoles)];
    console.log('RoleSwitchDialog - Available roles from departments:', uniqueRoles);
    return uniqueRoles;
  }

  // Try facDepUserRoles structure next
  if (user.value?.facDepUserRoles) {
    console.log('RoleSwitchDialog - Using facDepUserRoles structure');
    const allRoles = user.value.facDepUserRoles
      .map((facDepRole) => facDepRole.role?.name)
      .filter(Boolean) as string[];
    const uniqueRoles = [...new Set(allRoles)];
    console.log('RoleSwitchDialog - Available roles from facDepUserRoles:', uniqueRoles);
    return uniqueRoles;
  }

  // Fallback to legacy structure
  if (user.value?.faculties) {
    console.log('RoleSwitchDialog - Using faculties structure');
    const allRoles = user.value.faculties.flatMap((faculty) =>
      (faculty.roles || []).map((role) => role.name),
    );
    const uniqueRoles = [...new Set(allRoles)];
    console.log('RoleSwitchDialog - Available roles from faculties:', uniqueRoles);
    return uniqueRoles;
  }

  console.log('RoleSwitchDialog - No roles found, returning empty array');
  return [];
});

const availableFaculties = computed(() => {
  console.log('RoleSwitchDialog - Computing available faculties for role:', selectedRole.value);

  if (!selectedRole.value || !user.value) {
    console.log('RoleSwitchDialog - No selected role or user, returning empty faculties');
    return [];
  }

  // Try new departments structure first
  if (user.value.departments) {
    console.log('RoleSwitchDialog - Using departments structure for faculties');
    const facultiesForRole = user.value.departments
      .filter((department) => department.role?.name === selectedRole.value)
      .map((department) => department.faculty)
      .filter(Boolean);

    console.log('RoleSwitchDialog - Faculties for role from departments:', facultiesForRole);

    // Remove duplicates based on faculty id
    const uniqueFaculties = facultiesForRole.reduce(
      (acc, faculty) => {
        if (faculty && !acc.find((f) => f && f.id === faculty.id)) {
          acc.push(faculty);
        }
        return acc;
      },
      [] as typeof facultiesForRole,
    );

    console.log('RoleSwitchDialog - Unique faculties from departments:', uniqueFaculties);
    return uniqueFaculties;
  }

  // Try facDepUserRoles structure next
  if (user.value.facDepUserRoles) {
    console.log('RoleSwitchDialog - Using facDepUserRoles structure for faculties');
    const facultiesForRole = user.value.facDepUserRoles
      .filter((facDepRole) => facDepRole.role?.name === selectedRole.value)
      .map((facDepRole) => facDepRole.department?.faculty)
      .filter(Boolean);

    // Remove duplicates based on faculty id
    const uniqueFaculties = facultiesForRole.reduce(
      (acc, faculty) => {
        if (faculty && !acc.find((f) => f && f.id === faculty.id)) {
          acc.push(faculty);
        }
        return acc;
      },
      [] as typeof facultiesForRole,
    );

    console.log('RoleSwitchDialog - Unique faculties from facDepUserRoles:', uniqueFaculties);
    return uniqueFaculties;
  }

  // Fallback to legacy structure
  if (user.value.faculties) {
    console.log('RoleSwitchDialog - Using faculties structure for faculties');
    const faculties = user.value.faculties.filter((faculty) =>
      (faculty.roles || []).some((role) => role.name === selectedRole.value),
    );
    console.log('RoleSwitchDialog - Faculties from legacy structure:', faculties);
    return faculties;
  }

  console.log('RoleSwitchDialog - No faculties found, returning empty array');
  return [];
});

// Methods
const selectRole = (roleValue: string) => {
  selectedRole.value = roleValue;
  selectedFaculty.value = null; // Reset faculty when role changes
};

const selectFaculty = (faculty: Faculty) => {
  selectedFaculty.value = faculty;
};

const cancel = () => {
  selectedRole.value = null;
  selectedFaculty.value = null;
  emit('update:modelValue', false);
};

const confirm = () => {
  if (selectedRole.value && selectedFaculty.value) {
    // Check if there's actually a change
    const isDifferentRole = selectedRole.value !== currentRole.value;
    const isDifferentFaculty = selectedFaculty.value.id !== currentFaculty.value?.id;

    if (isDifferentRole || isDifferentFaculty) {
      // Pass facultyId to setCurrentRole for more accurate logging
      // Store previous data before setting new role
      const emitData: {
        role: string;
        facultyId: string; // Changed to string
        previousRole?: string;
        previousFacultyId?: string; // Changed to string
      } = {
        role: selectedRole.value,
        facultyId: selectedFaculty.value.id,
      };

      if (currentRole.value) {
        emitData.previousRole = currentRole.value;
      }

      if (currentFaculty.value?.id !== undefined) {
        emitData.previousFacultyId = currentFaculty.value.id;
      }

      // Set new role after preparing emit data
      authStore.setCurrentRole(selectedRole.value, selectedFaculty.value.id);

      emit('roleChanged', emitData);
    }
  }
  selectedRole.value = null;
  selectedFaculty.value = null;
  emit('update:modelValue', false);
};

// Watch for dialog open/close
watch(
  () => props.modelValue,
  (newValue) => {
    if (newValue) {
      // Initialize with current values when dialog opens
      selectedRole.value = currentRole.value;
      selectedFaculty.value = currentFaculty.value;
    } else {
      // Reset when dialog closes
      selectedRole.value = null;
      selectedFaculty.value = null;
    }
  },
  { immediate: true },
);
</script>

<style scoped lang="scss">
.selection-container {
  border: 1px solid #e0e0e0;
  border-radius: 12px;
  height: 100%;
  min-height: 300px;
  max-width: 450px;
  width: 100%;
  overflow-y: auto;
  padding: 15px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);

  // Responsive สำหรับหน้าจอเล็ก
  @media (max-width: 768px) {
    height: auto;
    min-height: 250px;
    max-height: 400px;
    max-width: 100%;
    margin-bottom: 16px;
  }
}

.transition-all {
  transition: all 0.3s ease;
}

.cursor-pointer:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.bg-primary {
  background: var(--q-primary) !important;
}
</style>
