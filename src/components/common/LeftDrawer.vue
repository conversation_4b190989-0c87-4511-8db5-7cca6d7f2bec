<template>
  <q-drawer
    :mini="miniState"
    @mouseenter="miniState = false"
    @mouseleave="miniState = true"
    :breakpoint="500"
    show-if-above
    v-model:model-value="globalStore.leftDrawerState"
    side="left"
    bordered
    :class="{ 'drawer-mini': miniState }"
  >
    <div class="full-height drawer-container-content">
      <div class="drawer-content">
        <MenuList :items="allDrawerMenu"></MenuList>
      </div>
      <q-space />
      <q-item @click="handleLogoutClick" clickable exact active-class="bg-primary text-black" class="menu-item logout-item">
        <q-item-section avatar>
          <q-icon name="logout" />
        </q-item-section>
        <q-item-section>
          <q-item-label class="text-subtitle1">ออกจากระบบ </q-item-label>
        </q-item-section>
      </q-item>
    </div>
  </q-drawer>
</template>

<script setup lang="ts">
import { useGlobalStore } from 'src/stores/global';
import type { MenuLink } from 'src/types/app';
import { ref } from 'vue';
import { allDrawerMenu } from 'src/data/menu';
import MenuList from '../MenuList.vue';

const globalStore = useGlobalStore();
const miniState = ref(true);
const emit = defineEmits<(e: 'logout') => void>();

const handleLogoutClick = () => {
  emit('logout');
};

defineProps<{
  menu: MenuLink[];
}>();

</script>

<style scoped lang="scss">
.menu-item {
  border-radius: 0 $generic-border-radius $generic-border-radius 0;
}

.logout-item:hover {
  background: $negative;
  color: white;
}

.drawer-mini .menu-item {
  border-radius: 0px;
}

.drawer-content{
  overflow-x: hidden;
  overflow-y: auto;
}

.drawer-container-content{
  display: flex;
  flex-direction: column;
}

.space {
  display: flex;
  
}
</style>
