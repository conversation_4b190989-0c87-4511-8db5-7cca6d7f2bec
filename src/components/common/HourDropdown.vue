<script setup lang="ts">
import { ref, watch } from 'vue';

const props = defineProps<{
  modelValue: number | null;
}>();

const emit = defineEmits<(e: 'update:modelValue', value: number | null) => void>();

const selectedHour = ref<number | null>(props.modelValue);

watch(
  () => props.modelValue,
  (newValue) => {
    selectedHour.value = newValue;
  },
);

// อัปเดตค่ากลับไปยัง parent ทุกครั้งที่ user เปลี่ยนค่า
watch(selectedHour, (val) => {
  emit('update:modelValue', val);
});

const hours = Array.from({ length: 23 }, (_, i) => ({
  label: `${i + 1}`,
  value: i + 1,
}));
</script>

<template>
  <q-select
    v-model="selectedHour"
    :options="hours"
    emit-value
    map-options
    outlined
    dense
    dropdown-icon="arrow_drop_down"
    style="width: 100px"
    label="ชั่วโมง"
  >
  </q-select>
</template>

<style scoped>
.custom-placeholder {
  color: #9e9e9e;
  font-size: 14px;
}
</style>
