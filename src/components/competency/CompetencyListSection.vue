<template>
  <div class="q-mb-md">
    <div class="row items-center justify-between q-mb-md">
      <div class="row items-center">
        <q-icon
          :name="isOpen ? 'keyboard_arrow_down' : 'keyboard_arrow_right'"
          color="dark"
          size="sm"
          class="q-mr-sm"
          @click="toggleOpen"
          style="cursor: pointer"
        />
        <q-icon name="folder" color="dark" size="sm" />
        <span class="text-weight-bold q-ml-sm">{{ title }}</span>
      </div>
      <q-btn color="accent" icon="add" size="sm" class="q-mr-md" round @click="add" />
    </div>

    <!-- Default items display -->
    <div v-if="isOpen && showItems" class="bg-white q-mt-sm">
      <q-list v-if="items.length > 0">
        <q-item v-for="(item, idx) in items" :key="idx">
          <q-item-section>
            <span class="q-ml-xl">{{ item }}</span>
          </q-item-section>
          <q-item-section side>
            <q-btn
              flat
              round
              icon="delete"
              color="negative"
              size="md"
              @click="remove(idx)"
              class="q-ml-md"
            />
          </q-item-section>
        </q-item>
      </q-list>
      <div v-else class="q-pa-md text-grey-6">ไม่มีรายการ</div>
    </div>

    <!-- Custom content slot -->
    <div v-if="isOpen && !showItems">
      <slot name="content"></slot>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, toRefs, withDefaults } from 'vue';

const props = withDefaults(
  defineProps<{
    title: string;
    items: string[];
    // modelValue: boolean; // ลบ prop นี้ออก
    showItems?: boolean;
  }>(),
  {
    showItems: true,
  },
);
const emit = defineEmits(['remove', 'add']);

const { showItems } = toRefs(props);
const isOpen = ref(false); // local state

function toggleOpen() {
  isOpen.value = !isOpen.value;
}

function remove(idx: number) {
  emit('remove', idx);
}

function add() {
  emit('add');
}
</script>

<style scoped></style>
