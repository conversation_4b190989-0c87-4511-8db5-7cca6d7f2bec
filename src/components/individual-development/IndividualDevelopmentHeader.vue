<template>
  <div class="q-pa-md">
    <!-- Title -->
    <div class="row items-center justify-between q-mb-md">
      <div class="text-h5 text-weight-medium">{{ title }}</div>
    </div>

    <!-- Tabs -->
    <q-tabs
      v-model="activeTab"
      class="text-grey-7"
      active-color="primary"
      indicator-color="primary"
      align="left"
      narrow-indicator
      @update:model-value="onTabChange"
    >
      <q-tab v-for="tab in tabs" :key="tab.value" :name="tab.value" :label="tab.label" />
    </q-tabs>

    <!-- Search and Filter Section -->
    <div class="row items-center q-gutter-md q-mt-md">
      <div class="col-12 col-md-4">
        <q-input
          v-model="searchValue"
          placeholder="ค้นหา..."
          outlined
          dense
          clearable
          @update:model-value="onSearchChange"
        >
          <template v-slot:prepend>
            <q-icon name="search" />
          </template>
        </q-input>
      </div>

      <div class="col-12 col-md-3">
        <q-select
          v-model="selectedDepartment"
          :options="departmentOptions"
          label="แผนก"
          outlined
          dense
          clearable
          emit-value
          map-options
          @update:model-value="onFilterChange"
        />
      </div>

      <div class="col-12 col-md-3">
        <q-select
          v-model="selectedStatus"
          :options="statusOptions"
          label="สถานะ"
          outlined
          dense
          clearable
          emit-value
          map-options
          @update:model-value="onFilterChange"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';
import {
  DEPARTMENT_OPTIONS,
  INDIVIDUAL_DEVELOPMENT_PLAN_STATUS_OPTIONS,
} from 'src/constants/individualDevelopmentPlan';

// Props
interface Props {
  title: string;
  tabs: Array<{ label: string; value: string }>;
  defaultTab?: string;
}

const props = withDefaults(defineProps<Props>(), {
  defaultTab: '',
});

// Emits
const emit = defineEmits<{
  search: [value: string];
  'tab-change': [tab: string];
  'filter-change': [filters: FilterOptions];
  'confirm-filter': [filters: FilterOptions];
}>();

// Types
interface FilterOptions {
  search: string;
  department: string | null;
  status: string | null;
  tab: string;
}

// State
const activeTab = ref(props.defaultTab || props.tabs[0]?.value || '');
const searchValue = ref('');
const selectedDepartment = ref<string | null>(null);
const selectedStatus = ref<string | null>(null);

// Options
const departmentOptions = DEPARTMENT_OPTIONS.map((dept) => ({
  label: dept,
  value: dept,
}));

const statusOptions = INDIVIDUAL_DEVELOPMENT_PLAN_STATUS_OPTIONS;

// Methods
const onTabChange = (tab: string) => {
  emit('tab-change', tab);
  emitFilterChange();
};

const onSearchChange = (value: string | number | null) => {
  emit('search', value?.toString() || '');
};

const onFilterChange = () => {
  emitFilterChange();
};

const emitFilterChange = () => {
  const filters: FilterOptions = {
    search: searchValue.value,
    department: selectedDepartment.value,
    status: selectedStatus.value,
    tab: activeTab.value,
  };
  emit('filter-change', filters);
};

// Watch for tab changes
watch(
  () => props.defaultTab,
  (newTab) => {
    if (newTab) {
      activeTab.value = newTab;
    }
  },
  { immediate: true },
);
</script>

<style scoped>
/* Custom styles if needed */
</style>
