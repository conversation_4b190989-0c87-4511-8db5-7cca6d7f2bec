<template>
  <q-dialog 
    v-model="dialogModel" 
    persistent 
    transition-show="scale" 
    transition-hide="scale"
  >
    <q-card style="width: 800px; max-width: 90vw;">
      <!-- Header -->
      <q-card-section class="row items-center q-pb-none bg-primary text-white">
        <div class="text-h6">
          {{ isEditMode ? 'แก้ไขแผนพัฒนารายบุคคล' : 'เพิ่มแผนพัฒนารายบุคคล' }}
        </div>
        <q-space />
        <q-btn flat round dense icon="close" @click="closeDialog" />
      </q-card-section>

      <!-- Content -->
      <q-card-section class="q-pa-md">
        <q-form @submit.prevent="onSubmit" class="q-gutter-y-md">
          <div class="row q-gutter-md">
            <div class="col-12 col-md-6">
              <q-input
                v-model="form.employee_name"
                label="ชื่อพนักงาน *"
                outlined
                dense
                :rules="[val => !!val || 'กรุณากรอกชื่อพนักงาน']"
              />
            </div>
            <div class="col-12 col-md-6">
              <q-input
                v-model="form.employee_id"
                label="รหัสพนักงาน *"
                outlined
                dense
                :rules="[val => !!val || 'กรุณากรอกรหัสพนักงาน']"
              />
            </div>
          </div>

          <div class="row q-gutter-md">
            <div class="col-12 col-md-6">
              <q-select
                v-model="form.department"
                :options="DEPARTMENT_OPTIONS"
                label="แผนก *"
                outlined
                dense
                :rules="[val => !!val || 'กรุณาเลือกแผนก']"
              />
            </div>
            <div class="col-12 col-md-6">
              <q-input
                v-model="form.position"
                label="ตำแหน่ง *"
                outlined
                dense
                :rules="[val => !!val || 'กรุณากรอกตำแหน่ง']"
              />
            </div>
          </div>

          <div class="col-12">
            <q-input
              v-model="form.development_goals"
              label="เป้าหมายพัฒนา *"
              outlined
              type="textarea"
              rows="3"
              :rules="[val => !!val || 'กรุณากรอกเป้าหมายพัฒนา']"
            />
          </div>

          <div class="row q-gutter-md">
            <div class="col-12 col-md-6">
              <q-select
                v-model="form.target_year"
                :options="generateYearOptions(5)"
                label="ปีเป้าหมาย *"
                outlined
                dense
                :rules="[val => !!val || 'กรุณาเลือกปีเป้าหมาย']"
              />
            </div>
            <div class="col-12 col-md-6">
              <q-select
                v-model="form.status"
                :options="INDIVIDUAL_DEVELOPMENT_PLAN_STATUS_OPTIONS"
                option-label="label"
                option-value="value"
                emit-value
                map-options
                label="สถานะ *"
                outlined
                dense
                :rules="[val => !!val || 'กรุณาเลือกสถานะ']"
              />
            </div>
          </div>
        </q-form>
      </q-card-section>

      <!-- Actions -->
      <q-card-actions align="right" class="q-pa-md">
        <q-btn 
          flat 
          label="ยกเลิก" 
          color="grey-7" 
          @click="closeDialog"
        />
        <q-btn 
          unelevated 
          :label="isEditMode ? 'บันทึก' : 'เพิ่ม'" 
          color="primary" 
          @click="onSubmit"
          :loading="loading"
        />
      </q-card-actions>
    </q-card>
  </q-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import { useQuasar } from 'quasar';
import { useIndividualDevelopmentPlan } from 'src/composables/useIndividualDevelopmentPlan';
import { 
  DEPARTMENT_OPTIONS, 
  INDIVIDUAL_DEVELOPMENT_PLAN_STATUS_OPTIONS,
  generateYearOptions
} from 'src/constants/individualDevelopmentPlan';
import type { IndividualDevelopmentPlan, IndividualDevelopmentPlanForm } from 'src/types/models';

// Props
interface Props {
  modelValue: boolean;
  editItem?: IndividualDevelopmentPlan | null;
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: false,
  editItem: null,
});

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean];
  'saved': [];
}>();

// Composables
const $q = useQuasar();
const { createPlan, updatePlan } = useIndividualDevelopmentPlan();

// State
const loading = ref(false);

// Computed
const dialogModel = computed({
  get: () => props.modelValue,
  set: (value: boolean) => emit('update:modelValue', value),
});

const isEditMode = computed(() => !!props.editItem);

// Form data
const defaultForm = {
  employee_name: '',
  employee_id: '',
  department: '',
  position: '',
  development_goals: '',
  target_year: new Date().getFullYear(),
  status: 'active' as IndividualDevelopmentPlanForm['status'],
  development_activities: '',
  success_criteria: '',
} satisfies IndividualDevelopmentPlanForm;

const form = ref<IndividualDevelopmentPlanForm>({ ...defaultForm });

// Methods
const resetForm = () => {
  form.value = { ...defaultForm };
};

const closeDialog = () => {
  dialogModel.value = false;
  resetForm();
};

const onSubmit = async () => {
  if (loading.value) return;
  
  try {
    loading.value = true;
    
    if (isEditMode.value && props.editItem) {
      await updatePlan(props.editItem.id, form.value);
      $q.notify({
        type: 'positive',
        message: 'อัปเดตแผนพัฒนารายบุคคลเรียบร้อยแล้ว',
      });
    } else {
      await createPlan(form.value);
      $q.notify({
        type: 'positive',
        message: 'เพิ่มแผนพัฒนารายบุคคลเรียบร้อยแล้ว',
      });
    }
    
    emit('saved');
    closeDialog();
  } catch (error) {
    console.error('Error saving plan:', error);
    $q.notify({
      type: 'negative',
      message: 'เกิดข้อผิดพลาดในการบันทึกข้อมูล',
    });
  } finally {
    loading.value = false;
  }
};

// Watchers
watch(() => props.editItem, (newItem) => {
  if (newItem) {
    form.value = {
      employee_name: newItem.employee_name,
      employee_id: newItem.employee_id,
      department: newItem.department,
      position: newItem.position,
      development_goals: newItem.development_goals,
      target_year: newItem.target_year,
      status: newItem.status,
      development_activities: newItem.development_activities || '',
      success_criteria: newItem.success_criteria || '',
    };
  } else {
    resetForm();
  }
}, { immediate: true });

watch(() => props.modelValue, (isOpen) => {
  if (!isOpen) {
    resetForm();
  }
});
</script>
