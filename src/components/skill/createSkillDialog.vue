<template>
  <q-dialog v-model="skillStore.createDialog" persistent>
    <q-card class="container" style="max-width: 900px">
      <q-card-section>
        <div class="text-h6">{{ skillStore.dialogTitile }}</div>
      </q-card-section>
      <q-form ref="formRef" @submit.prevent="submitForm">
        <q-card-section class="q-pt-none">
          <div class="q-gutter-y-md">
            <q-card-section
              style="
                border: 0.5px solid black;
                border-radius: 15px;
                min-height: 110px;
                max-height: 120px;
              "
            >
              <div class="text-subtitle1 text-bold q-mb-sm">ชื่อความรู้และทักษะ</div>

              <q-input
                v-model="formDataRef.name"
                :rules="[(val) => !!val || 'กรุณากรอกชื่อความรู้และทักษะ']"
                outlined
                dense
                :disable="skillStore.isPreview"
              />
            </q-card-section>
            <q-card-section style="border: 0.5px solid black; border-radius: 15px">
              <div class="text-subtitle1 text-bold q-mb-sm">ประเภทความรู้และทักษะ</div>
              <div class="row q-gutter-sm">
                <div class="col-sm-12">
                  <q-select
                    v-model="formDataRef.career_type"
                    label="ประเภทความรู้และทักษะ"
                    :options="skillTypeOptions"
                    outlined
                    dense
                    class="full-width"
                    :disable="skillStore.isPreview"
                  />
                </div>
              </div>
            </q-card-section>

            <q-card-section
              style="border: 0.5px solid black; border-radius: 15px; max-height: 150px"
            >
              <div class="text-subtitle1 text-bold q-mb-sm">ต้องการติดตามหรือไม่</div>
              <div class="q-gutter-sm">
                <q-radio
                  v-model="formDataRef.tracking"
                  :val="true"
                  label="ต้องการติดตาม"
                  :disable="skillStore.isPreview"
                />
                <q-radio
                  v-model="formDataRef.tracking"
                  :val="false"
                  label="ไม่ต้องการติดตาม"
                  :disable="skillStore.isPreview"
                />
              </div>
            </q-card-section>

            <q-card-section style="border: 0.5px solid black; border-radius: 15px; height: 215px">
              <div class="text-subtitle1 text-bold q-mb-sm">คำอธิบายเพิ่มเติม</div>

              <q-input
                v-model="formDataRef.description"
                label="คำอธิบายเพิ่มเติม"
                type="textarea"
                outlined
                :disable="skillStore.isPreview"
              />
            </q-card-section>
            <q-card-section style="border: 0.5px solid black; border-radius: 15px">
              <div class="text-subtitle1 text-bold q-mb-sm" style="width: 100%">
                สมรรถนะที่เชื่อมโยง
                <q-btn
                  icon="add"
                  label="เพิ่มสมรรถนะใหม่"
                  style="background-color: #4050b5; color: white; margin-left: 520px"
                  @click="onAddCompetency"
                  :disable="skillStore.isPreview"
                />
              </div>

              <div class="row q-gutter-sm">
                <div class="text-subtitle1 q-mb-sm" style="margin-top: 13px">ค้นหาโดย:</div>
                <div class="col-sm-10">
                  <q-select
                    v-model="selectedCompCareer"
                    label="สมรรถณะหลัก"
                    :options="compTypeOptions"
                    :disable="skillStore.isPreview"
                    outlined
                    dense
                  />
                </div>
              </div>
              <div class="q-mt-sm">
                <q-select
                  v-model="compCareerTypeModel"
                  label="ค้นหาสมรรถนะหลัก"
                  :options="compCareerTypeOptions"
                  :disable="skillStore.isPreview"
                  option-label="name"
                  option-value="id"
                  outlined
                  dense
                  style="width: 100%"
                  multiple
                  input-class="hide-selected-values"
                >
                  <template #prepend>
                    <q-icon name="search" />
                  </template>
                </q-select>
              </div>
              <div v-if="formDataRef.competencies">
                <div
                  v-for="(item, index) in formDataRef.competencies"
                  :key="index"
                  class="q-my-sm row items-center justify-between"
                >
                  <div>{{ item?.name }}</div>
                  <q-btn
                    color="negative"
                    icon="delete"
                    flat
                    dense
                    :disable="skillStore.isPreview"
                    @click="removeItem(item?.name)"
                  />
                </div>
              </div>
            </q-card-section>
          </div>
        </q-card-section>

        <q-card-actions align="right" class="q-pa-md">
          <div class="q-gutter-sm">
            <q-btn
              icon="close"
              label="ยกเลิก"
              flat
              color="grey-7"
              @click="close"
              style="border: 1px solid rgba(0, 0, 0, 0.3)"
            />
            <q-btn
              v-if="!skillStore.isPreview"
              icon="check"
              label="ยืนยัน"
              color="positive"
              @click="submitForm"
            />
          </div>
        </q-card-actions>
      </q-form>
    </q-card>
  </q-dialog>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from 'vue';
import { QForm, useQuasar } from 'quasar';
import type { Competency, Skill } from 'src/types/models';
import { useSkillStore } from 'src/stores/skills';
import { CompetenciesService } from 'src/services/competencies/competencies';
import CompetencyForm from '../competency/CompetencyForm.vue';

const skillStore = useSkillStore();
const $q = useQuasar();

const onAddCompetency = async () => {
  try {
    const dialogResult = await $q.dialog({
      component: CompetencyForm,
      componentProps: {
        title: 'สร้างสมรรถนะหลัก',
      },
    });

    const result = dialogResult as unknown as Competency;

    if (result?.name && result?.career_type) {
      // เมื่อบันทึกสมรรถนะใหม่ให้เพิ่มเข้าไปใน competencies
      if (Array.isArray(compCareerTypeModel.value)) {
        const newCompetency = {
          id: result.id || undefined,
          name: result.name || undefined,
          description: result.description || undefined,
          career_type: result.career_type || undefined,
        } as Partial<Competency>;
        compCareerTypeModel.value.push(newCompetency);
      }
    }
  } catch {
    console.error('Dialog was cancelled');
  }
};

const formRef = ref<QForm | null>(null);
const formDataRef = ref<Skill>({
  competencyIds: [0],
  name: '',
  description: '',
  career_type: '',
  dep_id: 1,
  evaluatorId: '0', // Changed to string
  tracking: true,
  programId: 0,
});

// Options for dropdowns
const skillTypeOptions = [
  'ทั่วไปบุคลากร',
  'ทั่วไปผู้บริหาร',
  'เฉพาะด้านสายวิชาการ',
  'เฉพาะด้านสายสนับสนุนวิชาการ',
  'เฉพาะด้านผู้บริหาร',
];

const compTypeOptions = [
  'สมรรถนะหลัก',
  'สมรรถนะสายวิชาการ',
  'สมรรถนะสายสนับสนุนวิชการ',
  'สมรรถนะทางการบริหาร',
];
const compCareerTypeModel = ref<Partial<Competency>[]>([]);
const selectedCompCareer = ref('');

watch(compCareerTypeModel, (newItems) => {
  if (Array.isArray(newItems)) {
    formDataRef.value.competencies = newItems.filter((item): item is Competency => !!item.name);
  }
});

const compCareerTypeOptions = ref<Competency[]>([]);

watch(selectedCompCareer, async (newVal) => {
  if (newVal) {
    console.log('watch');
    try {
      compCareerTypeOptions.value = await new CompetenciesService().fetchByType(
        selectedCompCareer.value,
      );
    } catch (error) {
      console.error('โหลดข้อมูล compCareerTypeOptions ล้มเหลว', error);
    }
  } else {
    compCareerTypeOptions.value = []; // เคลียร์ถ้าไม่มีค่า
  }
});

onMounted(() => {
  if (skillStore.editedSkill.id !== 0) {
    formDataRef.value = { ...skillStore.editedSkill };
  } else {
    formDataRef.value = {
      competencyIds: [],
      name: '',
      description: '',
      career_type: '',
      programId: 1,
      evaluatorId: '2', // Changed to string
      tracking: false,
      dep_id: 1,
    };
  }
});

const submitForm = async () => {
  if (formRef.value) {
    try {
      const isValid = await formRef.value.validate();
      if (isValid) {
        if (!formDataRef.value.id) {
          await skillStore.addSkill(formDataRef.value);
        } else {
          await skillStore.editSkill(formDataRef.value);
        }
      }
    } catch (error) {
      console.error('Form validation error:', error);
    }
  }
};

function removeItem(name?: string) {
  if (!name) return;

  if (formDataRef.value.competencies) {
    formDataRef.value.competencies = formDataRef.value.competencies.filter(
      (item) => item?.name !== name,
    );
  }

  if (Array.isArray(compCareerTypeModel.value)) {
    compCareerTypeModel.value = compCareerTypeModel.value.filter((item) => item?.name !== name);
  }
}

const close = () => {
  formDataRef.value = {
    competencyIds: [0],
    name: '',
    description: '',
    career_type: '',
    dep_id: 0,
    evaluatorId: '0', // Changed to string
    tracking: true,
    programId: 0,
  };
  skillStore.cleanValue();
  skillStore.createDialog = false;
  skillStore.isPreview = false;
};
</script>

<style scoped>
::v-deep(.custom-disabled-select .q-field__control) {
  background-color: #f0f0f0;
  border-radius: 8px;
}
.hide-selected-values .q-field__native,
.hide-selected-values .q-chip {
  display: none !important;
}
</style>
