<template>
  <q-dialog :model-value="modelValue" persistent>
    <q-card style="min-width: 700px; min-height: 600px">
      <q-card-section style="position: sticky; background-color: white; z-index: 1000; top: 0">
        <div class="text-h6">รายละเอียดสมรรถนะ</div>
      </q-card-section>
      <q-card-section>
        <Radar :data="radarChart" :options="radarChartOptions" width="600" height="500" />
        <!-- แสดง card ของทุก label -->
        <div class="q-mt-lg row q-col-gutter-md">
          <div v-for="label in radarChart.labels" :key="label" class="col-12 col-md-6">
            <q-card>
              <q-card-section>
                <div class="text-subtitle2">{{ label }}</div>
                <div v-for="project in labelPrograms[label]" :key="project">
                  {{ project }}
                </div>
              </q-card-section>
            </q-card>
          </div>
        </div>
      </q-card-section>
      <q-card-actions align="right" style="position: sticky; background-color: white; bottom: 0">
        <q-btn flat label="ปิด" color="primary" @click="closeDialog" />
      </q-card-actions>
    </q-card>
  </q-dialog>
</template>

<script setup lang="ts">
import type { PropType } from 'vue';
import { Radar } from 'vue-chartjs';

defineProps({
  modelValue: { type: Boolean, required: true },
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  radarChart: { type: Object as PropType<any>, required: true },
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  radarChartOptions: { type: Object as PropType<any>, required: true },
  labelPrograms: { type: Object as PropType<Record<string, string[]>>, required: true },
});

const emit = defineEmits(['update:modelValue']);

function closeDialog() {
  emit('update:modelValue', false);
}
</script>
