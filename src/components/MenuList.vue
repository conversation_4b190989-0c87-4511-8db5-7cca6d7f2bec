<template>
  <q-list>
    <template v-for="item in items" :key="item.title">
      <q-expansion-item
        v-if="item.children && item.children.length"
        :icon="item.icon"
        :label="item.title"
        class="menu-item"
        expand-icon="keyboard_arrow_down"
        expand-icon-class=""
      >
        <MenuList :items="item.children" :level="level + 1" />
      </q-expansion-item>
      <q-item
        v-else
        clickable
        :to="item.link"
        exact
        active-class="bg-primary text-black"
        :class="[ 'menu-item', level > 0 ? 'sub-menu-item' : '' ]"
      >
        <q-item-section avatar>
          <q-icon :name="item.icon" />
        </q-item-section>
        <q-item-section>
          <q-item-label class="text-subtitle1">{{ item.title }}</q-item-label>
        </q-item-section>
      </q-item>
    </template>
  </q-list>
</template>

<script setup lang="ts">
import type { MenuLink } from 'src/types/app';
import { defineProps } from 'vue';

const props = defineProps<{ items: MenuLink[]; level?: number }>();
const level = props.level ?? 0;
</script>

<style scoped lang="scss">
.menu-item {
  border-radius: 0 $generic-border-radius $generic-border-radius 0;
}
.sub-menu-item {
  padding-left: 2rem;
}
</style>
