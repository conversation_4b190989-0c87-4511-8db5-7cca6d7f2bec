<template>
  <q-card
    style="
      width: 1050px;
      min-height: 450px;
      max-height: 650px;
      max-width: 90vw;
      max-height: 900px;
      border: 2px solid #dddddd;
    "
  >
    <q-card-section class="row items-center q-pb-none">
      <div class="text-h6">อนุมัติหลักฐานการอบรมภายนอก</div>
      <q-space />
    </q-card-section>
    <q-separator spaced />

    <q-card-section class="q-pa-md">
      <div class="col-12 col-md-6">
        <div class="q-mb-sm"><span class="text-weight-medium">ชื่อ :</span> ผศ.ลลิตา มะลิวัน</div>
        <div class="q-mb-sm"><span class="text-weight-medium">ตำแหน่ง :</span> อาจารย์</div>
        <div class="q-mb-md">
          <span class="text-weight-medium">ความรู้และทักษะ :</span> {{ props.skillName || '—' }}
        </div>
        <div class="q-mb-sm">
          <span class="text-weight-medium">หลักฐาน : </span>
          <span v-if="props.evidences && props.evidences.length">
            <span
              v-for="(evidence, idx) in props.evidences"
              :key="idx"
              style="display: inline-block"
            >
              <span
                style="
                  display: inline-block;
                  border: 1px solid #d1d5db;
                  border-radius: 8px;
                  padding: 2px 10px;
                  margin-right: 8px;
                  background: #f8f9fa;
                  font-size: 14px;
                "
              >
                <a
                  :href="evidence.url"
                  target="_blank"
                  rel="noopener"
                  style="color: #3b5cff; text-decoration: underline; cursor: pointer"
                >
                  {{ evidence.name }}
                </a>
              </span>
            </span>
          </span>
        </div>
      </div>
    </q-card-section>

    <q-card-section>
      <div class="column">
        <q-radio
          val="approved"
          label="อนุมัติ"
          :model-value="props.status"
          disable
          class="q-ml-md"
          color="grey-6"
        />

        <q-radio
          val="rejected"
          label="ไม่อนุมัติ"
          :model-value="props.status"
          disable
          class="q-ml-md"
          color="grey-6"
        />
        <q-radio
          val="revise"
          label="ส่งกลับแก้ไข"
          :model-value="props.status"
          disable
          class="q-ml-md"
          color="grey-6"
        />
      </div>
      <q-slide-transition>
        <div v-if="props.status === 'rejected'">
          <q-input
            :model-value="props.reason"
            type="textarea"
            placeholder="ไม่สามารถนำมาเป็นหลักฐานได้"
            outlined
            class="q-ml-md"
            style="margin-top: 10px"
            disable
            :rows="8"
            color="grey-6"
          />
        </div>
      </q-slide-transition>
    </q-card-section>
    <q-card-actions align="right" class="q-mb-md">
      <q-btn
        flat
        label="ปิด"
        color="grey-6"
        @click="closeDialog"
        icon="close"
        style="border: 2px solid #dddddd; margin-bottom: 5px; margin-right: 12px"
      />
    </q-card-actions>
  </q-card>
</template>

<script setup lang="ts">
const emit = defineEmits(['close']);
const props = defineProps<{
  skillName: string;
  status: string;
  reason: string;
  evidences: { name: string; url: string }[];
}>();

function closeDialog() {
  emit('close');
}
</script>
