<template>
  <q-dialog ref="dialogRef" @hide="onDialogHide" persistent>
    <q-card style="width: 800px; max-width: 100vw">
      <q-card-section class="q-pb-none">
        <div class="row">
          <div class="col text-h6 text-weight-medium">{{ dialogTitle }}</div>
          <q-btn fab-mini flat padding="none" icon="close" @click="onDialogCancel" />
        </div>
      </q-card-section>

      <q-card-section>
        <component :is="tabComponent" v-model="formData" />
      </q-card-section>

      <q-card-actions class="q-mx-sm q-mb-sm q-pt-none justify-end">
        <q-btn
          icon="close"
          label="ยกเลิก"
          flat
          color="grey-7"
          style="border: 1px solid rgba(0, 0, 0, 0.3)"
          @click="onDialogCancel"
        />
        <q-btn icon="check" label="ยืนยัน" color="positive" @click="submitForm" />
      </q-card-actions>
    </q-card>
  </q-dialog>
</template>

<script setup lang="ts">
import { ref, nextTick, computed } from 'vue';
import { useDialogPluginComponent } from 'quasar';
import type { CreateIdpTabPlanProps as IdpTabPlanProps, IdpFormData } from 'src/types/idp-tab-plan';
import CommonITab from './content/CommonITab.vue';
import CommonMTab from './content/CommonMTab.vue';
import SpecializedTab from './content/SpecializedTab.vue';
import SupportTab from './content/SupportTab.vue';
import ManagerTab from './content/ManagerTab.vue';
import CareerTab from './content/CareerTab.vue';

// Dialog plugin component setup
defineEmits([...useDialogPluginComponent.emits]);
const { dialogRef, onDialogHide, onDialogOK, onDialogCancel } = useDialogPluginComponent();

const props = withDefaults(defineProps<IdpTabPlanProps & { form?: IdpFormData }>(), {
  tabs: 'common-i',
  form: (): IdpFormData => ({
    ageWork: '',
    skills: [],
    disableAgeWork: false,
  }),
});

// Create reactive form data from props
const formData = ref<IdpFormData>({
  ageWork: props.form?.ageWork || '',
  skills: props.form?.skills || [],
  disableAgeWork: props.form?.disableAgeWork || false,
});

// Component registry for tabs
const tabComponents = {
  'common-i': CommonITab,
  'common-m': CommonMTab,
  specialized: SpecializedTab,
  support: SupportTab,
  manager: ManagerTab,
  career: CareerTab,
};

// Dialog titles for each tab type
const dialogTitles = {
  'common-i': 'สร้างแผนพัฒนาบุคลากรทั่วไป',
  'common-m': 'สร้างแผนพัฒนาผู้บริหารทั่วไป',
  specialized: 'สร้างแผนพัฒนาผู้บริหารเฉพาะด้าน',
  support: 'สร้างแผนพัฒนาบุคลากรเฉพาะตำแหน่ง',
  manager: 'สร้างแผนพัฒนาบุคลากรสายงานสนับสนุนวิชาการ',
  career: 'สร้างแผนพัฒนาบุคลากรสายงานวิชาการ',
};

const tabComponent = computed(() => {
  return tabComponents[props.tabs] || CommonITab;
});

const dialogTitle = computed(() => {
  return dialogTitles[props.tabs] || 'สร้างแผนพัฒนาบุคลากร';
});

const submitForm = async () => {
  await nextTick();
  onDialogOK(formData.value);
};
</script>

<style scoped lang="scss">
.card-section {
  border: 0.5px solid $surface-selected;
  border-radius: $generic-border-radius;
  min-height: 120px;
  margin-bottom: 1rem;
}
</style>
