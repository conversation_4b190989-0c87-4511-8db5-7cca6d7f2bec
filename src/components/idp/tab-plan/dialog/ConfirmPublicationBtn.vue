<template>
  <q-dialog v-model="showDialog" persistent>
    <q-card class="confirm-publication-card">
      <q-card-section class="text-center q-pb-sm">
        <!-- Title -->
        <div class="text-h5 text-weight-bold q-mb-sm">ยืนยันการเผยแพร่</div>

        <!-- Publication Icon -->
        <div class="publication-icon q-mb-md">
          <q-icon name="cast" size="48px" color="accent" />
        </div>

        <!-- Confirmation Question -->
        <div class="text-black text-body1">คุณต้องการเผยแพร่ข้อมูลนี้หรือไม่?</div>
      </q-card-section>

      <q-card-actions class="row justify-end q-pa-md q-gutter-md">
        <!-- Cancel Button -->
        <q-btn
          unelevated
          rounded
          size="md"
          padding="sm xl"
          color="grey-4"
          text-color="black"
          class="cancel-btn"
          @click="handleCancel"
        >
          <q-icon name="close" size="20px" class="q-mr-sm" />
          ยกเลิก
        </q-btn>

        <!-- Confirm Button -->
        <q-btn
          unelevated
          rounded
          size="md"
          padding="sm xl"
          color="positive"
          text-color="white"
          class="confirm-btn"
          @click="handleConfirm"
        >
          <q-icon name="check" size="20px" class="q-mr-sm" />
          ยืนยัน
        </q-btn>
      </q-card-actions>
    </q-card>
  </q-dialog>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';

// Props
interface Props {
  modelValue?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: false,
});

// Emits
interface Emits {
  (e: 'update:modelValue', value: boolean): void;
  (e: 'confirm'): void;
  (e: 'cancel'): void;
}

const emit = defineEmits<Emits>();

// Reactive data
const showDialog = ref(props.modelValue);

// Watch for prop changes
watch(
  () => props.modelValue,
  (newValue: boolean) => {
    showDialog.value = newValue;
  },
);

// Watch for dialog changes
watch(showDialog, (newValue: boolean) => {
  emit('update:modelValue', newValue);
});

// Methods
const handleConfirm = () => {
  emit('confirm');
  showDialog.value = false;
};

const handleCancel = () => {
  emit('cancel');
  showDialog.value = false;
};
</script>

<style lang="scss" scoped>
.confirm-publication-card {
  min-width: 400px;
  max-width: 500px;
  border-radius: 16px;

  .publication-icon {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 80px;
    height: 80px;
    margin: 0 auto;
    background-color: rgba(var(--q-primary-rgb), 0.1);
    border-radius: 50%;
  }

  .cancel-btn {
    min-width: 120px;
    font-weight: 500;

    &:hover {
      background-color: rgba(0, 0, 0, 0.05);
    }
  }

  .confirm-btn {
    min-width: 120px;
    font-weight: 500;

    &:hover {
      background-color: rgba(var(--q-positive-rgb), 0.8);
    }
  }
}

// Responsive design
@media (max-width: 480px) {
  .confirm-publication-card {
    min-width: 90vw;
    margin: 16px;
  }
}
</style>
