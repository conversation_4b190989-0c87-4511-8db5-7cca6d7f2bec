<template>
  <div>
    <!-- Age Work Section -->
    <q-card-section class="card-section">
      <div class="text-subtitle1 text-bold q-mb-sm">อายุการปฏิบัติงาน</div>
      <div class="row q-gutter-md">
        <q-radio
          v-for="ageOption in ageWorkOptions"
          :key="ageOption.value"
          v-model="formData.ageWork"
          :val="ageOption.value"
          :label="ageOption.label"
          :disable="formData.disableAgeWork"
          color="accent"
        />
      </div>
    </q-card-section>

    <!-- Management Skills Section -->
    <q-card-section class="card-section">
      <div class="row items-center justify-between q-mb-md">
        <div class="text-subtitle1 text-bold">ทักษะการบริหารจัดการ</div>
        <q-btn color="accent" icon="add" label="เพิ่มทักษะใหม่" @click="onAddSkill" />
      </div>

      <!-- Management specific content -->
      <div class="q-mb-md">
        <q-inner-loading :showing="loading">
          <q-spinner-dots size="50px" color="accent" />
        </q-inner-loading>
        <q-select
          v-if="!loading"
          v-model="selectedSkill"
          label="ค้นหาทักษะ"
          :options="managementSkills"
          outlined
          dense
          use-input
          option-value="id"
          option-label="name"
          color="accent"
        >
          <template #prepend>
            <q-icon name="search" />
          </template>
        </q-select>
      </div>

      <!-- Selected Skills List -->
      <div v-if="formData.skills.length > 0" class="q-mt-md">
        <div class="text-subtitle2 q-mb-sm">ทักษะการบริหารที่เลือก:</div>
        <div class="row q-gutter-sm">
          <div
            v-for="skill in formData.skills"
            :key="skill.id || 0"
            class="q-pa-sm"
            style="
              border: 1px solid #e0e0e0;
              border-radius: 8px;
              background-color: #f0f8ff;
              display: flex;
              align-items: center;
              gap: 8px;
            "
          >
            <span>{{ skill.name }}</span>
            <q-btn
              icon="close"
              size="xs"
              flat
              round
              color="negative"
              @click="removeSkill(skill.id || 0)"
            />
          </div>
        </div>
      </div>
    </q-card-section>

    <!-- Create Skill Dialog -->
    <CreateSkillDialog />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import type { Skill } from 'src/types/models';
import type { IdpFormData, AgeWorkOption } from 'src/types/idp-tab-plan';
import { useSkillStore } from 'src/stores/skills';
import { useSkillsService } from 'src/services/skills/skills';
import CreateSkillDialog from 'src/components/skill/createSkillDialog.vue';

const skillStore = useSkillStore();

const formData = defineModel<IdpFormData>({
  required: true,
  default: (): IdpFormData => ({
    ageWork: '',
    skills: [],
    disableAgeWork: false,
  }),
});

const ageWorkOptions = ref<AgeWorkOption[]>([
  { label: 'อายุงาน 5 - 10 ปี', value: '3-5YEAR' },
  { label: 'อายุงาน 10 - 15 ปี', value: '6-8YEAR' },
  { label: 'อายุงานตั้งแต่ 15 ปีขึ้นไป', value: '9UP' },
]);

const selectedSkill = ref<Skill | null>(null);

const managementSkills = ref<Skill[]>([]);
const loading = ref(false);
const pagination = ref({
  page: 1,
  rowsPerPage: 25,
  sortBy: 'id',
  descending: false,
});

const fetchSkills = async () => {
  loading.value = true;
  try {
    const res = await useSkillsService.getAll(pagination.value);
    if (res && res.data) {
      managementSkills.value = res.data;
    }
  } catch (error) {
    console.error('Error fetching skills:', error);
  } finally {
    loading.value = false;
  }
};

const onAddSkill = () => {
  skillStore.dialogTitile = 'สร้างทักษะการบริหารจัดการใหม่';
  skillStore.createDialog = true;
};

// Initialize and fetch data
onMounted(() => {
  fetchSkills();
});

const removeSkill = (skillId: number) => {
  formData.value.skills = formData.value.skills.filter((skill) => skill.id !== skillId);
};
</script>

<style scoped lang="scss">
.card-section {
  border: 0.5px solid $surface-selected;
  border-radius: $generic-border-radius;
  min-height: 120px;
  margin-bottom: 1rem;
}
</style>
