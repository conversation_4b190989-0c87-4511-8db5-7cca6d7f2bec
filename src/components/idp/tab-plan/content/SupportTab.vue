<template>
  <div>
    <!-- Age Work Section -->
    <q-card-section class="card-section">
      <div class="text-subtitle1 text-bold q-mb-sm">อายุการปฏิบัติงาน</div>
      <div class="row q-gutter-md">
        <q-radio
          v-for="ageOption in ageWorkOptions"
          :key="ageOption.value"
          v-model="formData.ageWork"
          :val="ageOption.value"
          :label="ageOption.label"
          :disable="formData.disableAgeWork"
          color="accent"
        />
      </div>
    </q-card-section>

    <!-- Position Section -->
    <q-card-section class="card-section">
      <div class="text-subtitle1 text-bold q-mb-sm">ตำแหน่ง</div>
      <div class="row q-gutter-sm q-mb-md">
        <div class="col-6">
          <q-select
            v-model="selectedPosition"
            label="ประเภทสายงาน"
            :options="positionTypeOptions"
            outlined
            dense
            color="accent"
          />
        </div>
      </div>

      <div class="q-mb-md">
        <q-select
          v-model="selectedSpecificPosition"
          label="ค้นหาตำแหน่ง"
          :options="searchablePositions"
          outlined
          dense
          use-input
          hide-selected
          fill-input
          input-debounce="300"
          @filter="filterPositions"
          option-value="id"
          option-label="name"
          color="accent"
        >
          <template #prepend>
            <q-icon name="search" />
          </template>
        </q-select>
      </div>
    </q-card-section>

    <!-- Level Section -->
    <q-card-section class="card-section">
      <div class="text-subtitle1 text-bold q-mb-sm">ระดับ</div>
      <div class="row q-gutter-md">
        <q-radio
          v-for="levelOption in levelOptions"
          :key="levelOption.value"
          v-model="selectedLevel"
          :val="levelOption.value"
          :label="levelOption.label"
          color="accent"
        />
      </div>
    </q-card-section>

    <!-- Knowledge and Skills Section -->
    <q-card-section class="card-section">
      <div class="row items-center justify-between q-mb-md">
        <div class="text-subtitle1 text-bold">ความรู้และทักษะ</div>
        <q-btn color="accent" icon="add" label="เพิ่มทักษะใหม่" @click="onAddSkill" />
      </div>

      <!-- Search Filters -->
      <div class="row q-gutter-sm q-mb-md">
        <div class="col-6">
          <q-select
            v-model="filters.knowledgeType"
            label="ประเภทความรู้และทักษะ"
            :options="knowledgeTypeOptions"
            outlined
            dense
            color="accent"
          />
        </div>
        <div class="col-5">
          <q-select
            v-model="filters.department"
            label="ประเภทสายงาน"
            :options="departmentOptions"
            outlined
            dense
            color="accent"
          />
        </div>
      </div>

      <!-- Skills Search -->
      <div class="q-mb-md">
        <q-select
          v-model="selectedSkill"
          label="ค้นหาทักษะ"
          :options="searchableSkills"
          outlined
          dense
          use-input
          hide-selected
          fill-input
          input-debounce="300"
          @filter="filterSkills"
          option-value="id"
          option-label="name"
          color="accent"
        >
          <template #prepend>
            <q-icon name="search" />
          </template>
        </q-select>
      </div>

      <!-- Selected Skills List -->
      <!-- Display Skills List -->
      <div class="q-mt-md">
        <div class="text-subtitle2 q-mb-sm">ทักษะที่แนะนำ:</div>
        <q-inner-loading :showing="loading">
          <q-spinner-dots size="50px" color="accent" />
        </q-inner-loading>
        <div v-if="!loading" class="row q-gutter-sm">
          <div
            v-for="skill in formData.skills"
            :key="skill.id || 0"
            class="q-pa-sm"
            style="
              border: 1px solid #e0e0e0;
              border-radius: 8px;
              background-color: #f5f5f5;
              display: flex;
              align-items: center;
              gap: 8px;
            "
          >
            <span>{{ skill.name }}</span>
            <q-btn
              icon="close"
              size="xs"
              flat
              round
              color="negative"
              @click="removeSkill(skill.id || 0)"
            />
          </div>
        </div>
      </div>
    </q-card-section>

    <!-- Create Skill Dialog -->
    <CreateSkillDialog />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import type { Skill } from 'src/types/models';
import type { IdpFormData, AgeWorkOption } from 'src/types/idp-tab-plan';
import { QSelect } from 'quasar';
import { useSkillStore } from 'src/stores/skills';
import { useSkillsService } from 'src/services/skills/skills';
import CreateSkillDialog from 'src/components/skill/createSkillDialog.vue';

const skillStore = useSkillStore();

const formData = defineModel<IdpFormData>({
  required: true,
  default: (): IdpFormData => ({
    ageWork: '',
    skills: [],
    disableAgeWork: false,
  }),
});

const selectedPosition = ref(null);
const selectedSpecificPosition = ref(null);
const selectedLevel = ref('');
const selectedSkill = ref<Skill | null>(null);

const filters = ref({
  knowledgeType: null,
  department: null,
});

const ageWorkOptions = ref<AgeWorkOption[]>([
  { label: 'อายุงาน 1 - 2 ปี', value: '1-2YEAR' },
  { label: 'อายุงาน 3 - 5 ปี', value: '3-5YEAR' },
  { label: 'อายุงาน 6 - 8 ปี', value: '6-8YEAR' },
  { label: 'อายุงานตั้งแต่ 9 ปีขึ้นไป', value: '9UP' },
]);

const positionTypeOptions = ref([{ label: 'ประเภทสายงาน', value: 'general' }]);

const levelOptions = ref([
  { label: 'ปฏิบัติงาน', value: 'operational' },
  { label: 'ชำนาญการ', value: 'specialist' },
  { label: 'ชำนาญการพิเศษ', value: 'senior_specialist' },
  { label: 'เชี่ยวชาญ', value: 'expert' },
  { label: 'ทรงคุณวุฒิ', value: 'distinguished' },
]);

const knowledgeTypeOptions = ref([{ label: 'ประเภทความรู้และทักษะ', value: 'general' }]);

const departmentOptions = ref([{ label: 'ประเภทสายงาน', value: 'general' }]);

const searchablePositions = ref([]);
const searchableSkills = ref<Skill[]>([]);

const displaySkills = ref<Skill[]>([]);
const loading = ref(false);
const pagination = ref({
  page: 1,
  rowsPerPage: 25,
  sortBy: 'id',
  descending: false,
});

const onAddSkill = () => {
  skillStore.dialogTitile = 'สร้างทักษะเฉพาะตำแหน่งใหม่';
  skillStore.createDialog = true;
};

const removeSkill = (skillId: number) => {
  formData.value.skills = formData.value.skills.filter((skill) => skill.id !== skillId);
};

const filterPositions: QSelect['onFilter'] = (inputValue: string, doneFn) => {
  doneFn(() => {
    searchablePositions.value = [];
  });
};

const filterSkills: QSelect['onFilter'] = (inputValue: string, doneFn) => {
  if (inputValue === '') {
    doneFn(() => {
      searchableSkills.value = displaySkills.value;
    });
    return;
  }

  const filtered = displaySkills.value.filter((skill: Skill) =>
    skill.name.toLowerCase().includes(inputValue.toLowerCase()),
  );

  doneFn(() => {
    searchableSkills.value = filtered;
  });
};

// Fetch skills from API
const fetchSkills = async () => {
  loading.value = true;
  try {
    const res = await useSkillsService.getAll(pagination.value);
    if (res && res.data) {
      displaySkills.value = res.data;
      searchableSkills.value = res.data;
    }
  } catch (error) {
    console.error('Error fetching skills:', error);
  } finally {
    loading.value = false;
  }
};

// Initialize searchableSkills and fetch data
onMounted(() => {
  fetchSkills();
});
</script>

<style scoped lang="scss">
.card-section {
  border: 0.5px solid $surface-selected;
  border-radius: $generic-border-radius;
  min-height: 120px;
  margin-bottom: 1rem;
}
</style>
