<template>
  <div>
    <!-- Position Section -->
    <q-card-section class="card-section">
      <div class="text-subtitle1 text-bold q-mb-sm">ตำแหน่ง</div>

      <div class="q-mb-md">
        <q-select
          v-model="selectedPosition"
          label="ค้นหาตำแหน่ง"
          :options="searchablePositions"
          outlined
          dense
          use-input
          hide-selected
          fill-input
          input-debounce="300"
          @filter="filterPositions"
          option-value="id"
          option-label="name"
          color="accent"
        >
          <template #prepend>
            <q-icon name="search" />
          </template>
        </q-select>
      </div>

      <!-- Selected Position Tags -->
      <div class="q-mt-md">
        <div class="row q-gutter-sm">
          <div
            v-for="position in selectedPositions"
            :key="position.id"
            class="q-pa-sm"
            style="
              border: 1px solid #e0e0e0;
              border-radius: 8px;
              background-color: #fce4ec;
              display: flex;
              align-items: center;
              gap: 8px;
            "
          >
            <span>{{ position.name }}</span>
            <q-btn
              icon="delete"
              size="xs"
              flat
              round
              color="negative"
              @click="removePosition(position.id)"
            />
          </div>
        </div>
      </div>
    </q-card-section>

    <!-- Knowledge and Skills Section -->
    <q-card-section class="card-section">
      <div class="row items-center justify-between q-mb-md">
        <div class="text-subtitle1 text-bold">ความรู้และทักษะ</div>
        <q-btn color="accent" icon="add" label="เพิ่มทักษะใหม่" @click="onAddSkill" />
      </div>

      <!-- Search Filters -->
      <div class="row q-gutter-sm q-mb-md">
        <div class="col-12">
          <q-select
            v-model="filters.knowledgeType"
            label="ความรู้และทักษะทั่วไปของผู้บริหาร"
            :options="knowledgeTypeOptions"
            outlined
            dense
            color="accent"
          />
        </div>
      </div>

      <!-- Skills Search -->
      <div class="q-mb-md">
        <q-select
          v-model="selectedSkill"
          label="ค้นหาทักษะ"
          :options="searchableSkills"
          outlined
          dense
          use-input
          hide-selected
          fill-input
          input-debounce="300"
          @filter="filterSkills"
          option-value="id"
          option-label="name"
          color="accent"
        >
          <template #prepend>
            <q-icon name="search" />
          </template>
        </q-select>
      </div>

      <!-- Selected Skills List -->
      <div v-if="formData.skills.length > 0" class="q-mt-md">
        <div class="text-subtitle2 q-mb-sm">ทักษะที่เลือก:</div>
        <div class="row q-gutter-sm">
          <div
            v-for="skill in formData.skills"
            :key="skill.id || 0"
            class="q-pa-sm"
            style="
              border: 1px solid #e0e0e0;
              border-radius: 8px;
              background-color: #fff8e1;
              display: flex;
              align-items: center;
              gap: 8px;
            "
          >
            <span>{{ skill.name }}</span>
            <q-btn
              icon="close"
              size="xs"
              flat
              round
              color="negative"
              @click="removeSkill(skill.id || 0)"
            />
          </div>
        </div>
      </div>

      <!-- Display Skills List -->
      <div v-if="displaySkills.length > 0" class="q-mt-md">
        <div class="text-subtitle2 q-mb-sm">ทักษะที่แนะนำ:</div>
        <div class="row q-gutter-sm">
          <div
            v-for="skill in displaySkills"
            :key="skill.id || 0"
            class="col-12 skill-display-item q-pa-sm"
            style="
              border: 1px solid #e0e0e0;
              border-radius: 8px;
              display: flex;
              align-items: center;
              justify-content: space-between;
            "
          >
            <div>
              <div class="text-weight-medium">{{ skill.name }}</div>
              <div class="text-caption text-grey-6">{{ skill.description }}</div>
            </div>
            <q-btn
              icon="delete"
              size="sm"
              flat
              round
              color="negative"
              @click="removeDisplaySkill(skill.id || 0)"
            />
          </div>
        </div>
      </div>
    </q-card-section>

    <!-- Create Skill Dialog -->
    <CreateSkillDialog />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import type { Skill } from 'src/types/models';
import type { IdpFormData } from 'src/types/idp-tab-plan';
import { QSelect } from 'quasar';
import { useSkillStore } from 'src/stores/skills';
import CreateSkillDialog from 'src/components/skill/createSkillDialog.vue';

const skillStore = useSkillStore();

const formData = defineModel<IdpFormData>({
  required: true,
  default: (): IdpFormData => ({
    ageWork: '',
    skills: [],
  }),
});

const selectedPosition = ref(null);
const selectedSkill = ref<Skill | null>(null);
const selectedPositions = ref([
  { id: 1, name: 'รองอธิการบดี' },
  { id: 2, name: 'ผู้ช่วยเลขาธิการงาน' },
]);

const filters = ref({
  knowledgeType: null,
});

const knowledgeTypeOptions = ref([
  { label: 'ความรู้และทักษะทั่วไปของผู้บริหาร', value: 'management' },
]);

const searchablePositions = ref([]);
const searchableSkills = ref<Skill[]>([]);

// Mock skills
const displaySkills = ref<Skill[]>([
  {
    id: 60,
    name: 'การจัดการเชิงกลยุทธ์',
    description: 'ทักษะการบริหารระดับสูง',
    career_type: 'management',
    dep_id: 1,
    evaluatorId: '1', // Changed to string
    tracking: true,
    programId: 1,
    competencyIds: [1],
  },
  {
    id: 61,
    name: 'การเจรจาต่อรอง',
    description: 'ทักษะการเจรจาและการต่อรอง',
    career_type: 'management',
    dep_id: 1,
    evaluatorId: '1', // Changed to string
    tracking: true,
    programId: 1,
    competencyIds: [2],
  },
]);

const onAddSkill = () => {
  skillStore.dialogTitile = 'สร้างทักษะเฉพาะด้านผู้บริหารใหม่';
  skillStore.createDialog = true;
};

const removeSkill = (skillId: number) => {
  formData.value.skills = formData.value.skills.filter((skill) => skill.id !== skillId);
};

const removePosition = (positionId: number) => {
  selectedPositions.value = selectedPositions.value.filter((pos) => pos.id !== positionId);
};

const removeDisplaySkill = (skillId: number) => {
  displaySkills.value = displaySkills.value.filter((skill) => skill.id !== skillId);
};

const filterPositions: QSelect['onFilter'] = (inputValue: string, doneFn) => {
  doneFn(() => {
    searchablePositions.value = [];
  });
};

const filterSkills: QSelect['onFilter'] = (inputValue: string, doneFn) => {
  if (inputValue === '') {
    doneFn(() => {
      searchableSkills.value = displaySkills.value;
    });
    return;
  }

  const filtered = displaySkills.value.filter((skill: Skill) =>
    skill.name.toLowerCase().includes(inputValue.toLowerCase()),
  );

  doneFn(() => {
    searchableSkills.value = filtered;
  });
};

onMounted(() => {
  searchableSkills.value = displaySkills.value;
});
</script>

<style scoped lang="scss">
.card-section {
  border: 0.5px solid $surface-selected;
  border-radius: $generic-border-radius;
  min-height: 120px;
  margin-bottom: 1rem;
}
</style>
