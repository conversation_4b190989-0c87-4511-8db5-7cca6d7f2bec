<template>
  <div>
    <!-- Age Work Section -->
    <q-card-section class="card-section">
      <div class="text-subtitle1 text-bold q-mb-sm">อายุการปฏิบัติงาน</div>
      <div class="row q-gutter-md">
        <q-radio
          v-for="ageOption in ageWorkOptions"
          :key="ageOption.value"
          v-model="formData.ageWork"
          :val="ageOption.value"
          :label="ageOption.label"
          :disable="formData.disableAgeWork"
          color="accent"
        />
      </div>
    </q-card-section>

    <!-- Knowledge and Skills Section -->
    <q-card-section class="card-section">
      <div class="row items-center justify-between q-mb-md">
        <div class="text-subtitle1 text-bold">ความรู้และทักษะ</div>
        <q-btn color="accent" icon="add" label="เพิ่มทักษะใหม่" @click="onAddSkill" />
      </div>

      <!-- Search Filters -->
      <div class="row q-gutter-sm q-mb-md">
        <div class="col-auto flex items-center">
          <span class="text-subtitle2">ค้นหาโดย:</span>
        </div>
        <div class="col-4">
          <q-select
            v-model="filters.skillType"
            label="ความรู้และทักษะทั่วไปของบุคลากร"
            :options="skillTypeOptions"
            outlined
            dense
            color="accent"
          />
        </div>
        <div class="col-4">
          <q-select
            v-model="filters.status"
            label="ประเภทสถานะงาน"
            :options="statusOptions"
            outlined
            color="accent"
            dense
          />
        </div>
      </div>

      <!-- Skills Search -->
      <div class="q-mb-md">
        <q-select
          v-model="selectedSkill"
          label="ค้นหาทักษะ"
          :options="searchableSkills"
          outlined
          dense
          use-input
          hide-selected
          fill-input
          input-debounce="300"
          @filter="filterSkills"
          option-value="id"
          option-label="name"
          style="width: 100%"
          @update:model-value="addSelectedSkill"
          color="accent"
        >
          <template #prepend>
            <q-icon name="search" />
          </template>
        </q-select>
      </div>

      <!-- Selected Skills List -->
      <div v-if="formData.skills.length > 0" class="q-mt-md">
        <div class="text-subtitle2 q-mb-sm">ทักษะที่เลือก:</div>
        <div class="row q-gutter-sm">
          <div
            v-for="skill in formData.skills"
            :key="skill.id || 0"
            class="q-pa-sm"
            style="
              border: 1px solid #e0e0e0;
              border-radius: 8px;
              background-color: #f5f5f5;
              display: flex;
              align-items: center;
              gap: 8px;
            "
          >
            <span>{{ skill.name }}</span>
            <q-btn
              icon="close"
              size="xs"
              flat
              round
              color="negative"
              @click="removeSkill(skill.id || 0)"
            />
          </div>
        </div>
      </div>

      <!-- Skills List Display -->
      <div class="q-mt-md">
        <div class="text-subtitle2 q-mb-sm">รายการทักษะ:</div>
        <q-inner-loading :showing="loading">
          <q-spinner-dots size="50px" color="accent" />
        </q-inner-loading>
        <div v-if="!loading" class="row q-gutter-sm">
          <div
            v-for="skill in displaySkills"
            :key="skill.id || 0"
            class="col-12 skill-display-item q-pa-sm"
            style="
              border: 1px solid #e0e0e0;
              border-radius: 8px;
              display: flex;
              align-items: center;
              justify-content: space-between;
            "
          >
            <div>
              <div class="text-weight-medium">{{ skill.name }}</div>
              <div class="text-caption text-grey-6">{{ skill.description }}</div>
            </div>
            <q-btn
              icon="delete"
              size="sm"
              flat
              round
              color="negative"
              @click="removeDisplaySkill(skill.id || 0)"
            />
          </div>
        </div>
      </div>
    </q-card-section>

    <!-- Create Skill Dialog -->
    <CreateSkillDialog />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed, watch } from 'vue';
import type { Skill } from 'src/types/models';
import type { IdpFormData, AgeWorkOption, SkillTypeOption } from 'src/types/idp-tab-plan';
import type { DevelopmentPlan, TypePlan } from 'src/types/idp';
import { QSelect } from 'quasar';
import { useSkillStore } from 'src/stores/skills';
import { useSkillsService } from 'src/services/skills/skills';
import { useDevPlansService } from 'src/services/idp/devPlansService';
import { useRoute } from 'vue-router';
import CreateSkillDialog from 'src/components/skill/createSkillDialog.vue';

// Initialize services and stores
const skillStore = useSkillStore();
const devPlansService = useDevPlansService();
const route = useRoute();

const formData = defineModel<IdpFormData>({
  required: true,
  default: (): IdpFormData => ({
    ageWork: '',
    skills: [],
    disableAgeWork: false,
  }),
});

const filters = ref({
  skillType: null,
  status: null,
});

const skillTypeOptions = ref<SkillTypeOption[]>([
  { label: 'ทักษะทั่วไป', value: 'general' },
  { label: 'ทักษะเฉพาะทาง', value: 'specialized' },
]);

const statusOptions = ref<SkillTypeOption[]>([
  { label: 'Active', value: 'active' },
  { label: 'Inactive', value: 'inactive' },
]);

const ageWorkOptions = ref<AgeWorkOption[]>([
  { label: 'อายุงาน 1 - 2 ปี', value: '1-2YEAR' },
  { label: 'อายุงาน 3 - 5 ปี', value: '3-5YEAR' },
  { label: 'อายุงาน 6 - 8 ปี', value: '6-8YEAR' },
  { label: 'อายุงานตั้งแต่ 9 ปีขึ้นไป', value: '9UP' },
]);

const selectedSkill = ref<Skill | null>(null);

const displaySkills = ref<Skill[]>([]);
const developmentPlanData = ref<DevelopmentPlan | null>(null);
const loading = ref(false);
const pagination = ref({
  page: 1,
  rowsPerPage: 25,
  sortBy: 'id',
  descending: false,
});

const searchableSkills = ref<Skill[]>([]);

// Computed property to filter skills based on development plan data
const filteredSkillsFromDevPlan = computed(() => {
  if (!developmentPlanData.value?.typePlans) {
    return [];
  }

  const currentAgeWork = formData.value.ageWork;
  const relevantSkills: Skill[] = [];

  // Filter typePlans that match the current context (ทั่วไปบุคลากร for CommonITab)
  const relevantTypePlans = developmentPlanData.value.typePlans.filter(
    (typePlan: TypePlan) => typePlan.name === 'ทั่วไปบุคลากร',
  );

  relevantTypePlans.forEach((typePlan: TypePlan) => {
    // If ageWork is specified, filter by matching ageWork
    if (currentAgeWork && typePlan.ageWork) {
      const ageWorkMatches =
        (currentAgeWork === '1-2YEAR' &&
          typePlan.ageWork.startYear === 1 &&
          typePlan.ageWork.endYear === 2) ||
        (currentAgeWork === '3-5YEAR' &&
          typePlan.ageWork.startYear === 3 &&
          typePlan.ageWork.endYear === 5) ||
        (currentAgeWork === '6-8YEAR' &&
          typePlan.ageWork.startYear === 6 &&
          typePlan.ageWork.endYear === 8) ||
        (currentAgeWork === '9UP' && typePlan.ageWork.startYear >= 9);

      if (ageWorkMatches && typePlan.skills) {
        relevantSkills.push(...typePlan.skills);
      }
    } else if (!currentAgeWork && typePlan.skills) {
      // If no ageWork is specified, include all skills from this typePlan
      relevantSkills.push(...typePlan.skills);
    }
  });

  // Remove duplicates based on skill ID
  const uniqueSkills = relevantSkills.filter(
    (skill, index, self) => index === self.findIndex((s) => s.id === skill.id),
  );

  return uniqueSkills;
});

// Methods
const onAddSkill = () => {
  console.log('Opening create skill dialog');
  skillStore.cleanValue();
  skillStore.dialogTitile = 'สร้างความรู้และทักษะทั่วไปของบุคลากร';
  skillStore.createDialog = true;
};

const removeDisplaySkill = (skillId: number) => {
  displaySkills.value = displaySkills.value.filter((skill) => skill.id !== skillId);
};

const removeSkill = (skillId: number) => {
  formData.value.skills = formData.value.skills.filter((skill) => skill.id !== skillId);
};

const addSelectedSkill = (skill: Skill) => {
  if (skill && !formData.value.skills.some((s) => s.id === skill.id)) {
    formData.value.skills.push(skill);
    selectedSkill.value = null; // Reset selected skill after adding
  }
};

const filterSkills: QSelect['onFilter'] = (inputValue: string, doneFn) => {
  // Use the current displaySkills which are already filtered by development plan
  const availableSkills = displaySkills.value;

  if (inputValue === '') {
    doneFn(() => {
      searchableSkills.value = availableSkills;
    });
    return;
  }

  const filtered = availableSkills.filter((skill: Skill) =>
    skill.name.toLowerCase().includes(inputValue.toLowerCase()),
  );

  doneFn(() => {
    searchableSkills.value = filtered;
  });
};

// Fetch development plan data from API
const fetchDevelopmentPlan = async () => {
  const devPlanId = Number(route.params.id);
  if (!devPlanId || isNaN(devPlanId)) {
    console.warn('No valid development plan ID found in route');
    // Fallback to general skills API if no development plan ID
    await fetchSkills();
    return;
  }

  loading.value = true;
  try {
    const plan = await devPlansService.fetchOne(devPlanId);
    developmentPlanData.value = plan;

    // Initial update of displayed skills
    updateDisplayedSkills();
  } catch (error) {
    console.error('Error fetching development plan:', error);
    // Fallback to general skills API if development plan fetch fails
    await fetchSkills();
  } finally {
    loading.value = false;
  }
};

// Fetch skills from general API (fallback)
const fetchSkills = async () => {
  loading.value = true;
  try {
    const res = await useSkillsService.getAll(pagination.value);
    if (res && res.data) {
      displaySkills.value = res.data;
      searchableSkills.value = res.data;
    }
  } catch (error) {
    console.error('Error fetching skills:', error);
  } finally {
    loading.value = false;
  }
};

// Update displayed skills when ageWork changes
const updateDisplayedSkills = () => {
  if (developmentPlanData.value) {
    const filteredSkills = filteredSkillsFromDevPlan.value;
    displaySkills.value = filteredSkills;
    searchableSkills.value = filteredSkills;

    // Update formData.skills to show existing skills for the selected ageWork
    formData.value.skills = [...filteredSkills];
  }
};

// Watch for ageWork changes to update displayed skills
watch(
  () => formData.value.ageWork,
  () => {
    updateDisplayedSkills();
  },
  { immediate: false },
);

// Watch for development plan data changes
watch(
  () => developmentPlanData.value,
  () => {
    updateDisplayedSkills();
  },
  { immediate: false },
);

// Watch for displaySkills changes to update searchableSkills when no search is active
watch(
  () => displaySkills.value,
  (newDisplaySkills) => {
    if (!isSearchActive.value) {
      searchableSkills.value = newDisplaySkills;
    }
  },
  { immediate: true },
);

// Initialize and fetch data
onMounted(() => {
  fetchDevelopmentPlan();
});
</script>

<style scoped lang="scss">
.card-section {
  border: 0.5px solid $surface-selected;
  border-radius: $generic-border-radius;
  min-height: 120px;
  margin-bottom: 1rem;
}
</style>
