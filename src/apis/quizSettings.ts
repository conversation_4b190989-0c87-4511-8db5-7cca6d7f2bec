import { api as axios } from 'src/boot/axios';
import type { Assessment } from 'src/types/models';

export interface QuizSettings {
  id: number;
  name: string;
  startAt?: string;
  endAt?: string;
  timeout: number;
  submitLimit?: number;
  passRatio: number;
  isPrototype?: boolean;
  type: 'QUIZ' | 'EVALUATE';
}

export interface UpdateQuizSettingsPayload {
  startAt?: string | undefined;
  endAt?: string | undefined;
  timeout?: number | undefined;
  submitLimit?: number | undefined;
  passRatio?: number | undefined;
  isPrototype?: boolean | undefined;
}

/**
 * Get quiz settings for a specific assessment
 * @param assessmentId - The ID of the assessment
 * @returns Promise containing quiz settings
 */
export const getQuizSettingsApi = async (assessmentId: number): Promise<{ data: QuizSettings }> => {
  const response = await axios.get<QuizSettings>(`/assessments/${assessmentId}/settings`);
  return { data: response.data };
};

/**
 * Update quiz settings for a specific assessment
 * @param assessmentId - The ID of the assessment
 * @param settings - The settings to update
 * @returns Promise containing updated assessment
 */
export const updateQuizSettingsApi = async (
  assessmentId: number,
  settings: UpdateQuizSettingsPayload
): Promise<{ data: Assessment }> => {
  const response = await axios.patch<Assessment>(`/assessments/${assessmentId}/settings`, settings);
  return { data: response.data };
};
