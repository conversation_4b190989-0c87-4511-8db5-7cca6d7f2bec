import type { QTableProps } from 'quasar';
import type { AxiosResponse } from 'axios';
import { api } from 'src/boot/axios';
import type { DataResponse, FacultyUser } from 'src/types/data';

import { formatParams } from 'src/utils/formatter';
import type { Faculty } from 'src/types';

// API Functions
const FALCULTY_PATH = 'faculties';

/**
 * Get Faculties
 */
export async function getFacultiesApi(
  pagination: QTableProps['pagination'],
): Promise<AxiosResponse<DataResponse<Faculty>>> {
  const params = formatParams(pagination);
  return api.get<DataResponse<Faculty>>(FALCULTY_PATH, {
    params,
  });
}

/**
 * Get FacultiesRoles
 */

export async function getFalcultieRolesApi(
  pagination: QTableProps['pagination'],
  search?: string,
  facultyId?: number,
  roleId?: number,
): Promise<AxiosResponse<DataResponse<FacultyUser>>> {
  const params = formatParams(pagination, search);
  return api.get<DataResponse<FacultyUser>>(`${FALCULTY_PATH}/${facultyId}/${roleId}/roles`, {
    params,
  });
}

export async function getUserByFacultyApi(
  pagination: QTableProps['pagination'],
  search?: string,
  facultyId?: number,
): Promise<AxiosResponse<DataResponse<FacultyUser>>> {
  const params = formatParams(pagination, search);
  return api.get<DataResponse<FacultyUser>>(`${FALCULTY_PATH}/${facultyId}/users`, {
    params,
  });
}
