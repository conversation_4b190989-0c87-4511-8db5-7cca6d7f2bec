import { api as axios } from 'src/boot/axios';
import type { Assessment } from 'src/types/models';

export interface EvaluateSettings {
  id: number;
  name: string;
  startAt?: string;
  endAt?: string;
  responseEdit?: boolean;
  submitLimit?: number;
  isPrototype?: boolean;
  type: 'QUIZ' | 'EVALUATE';
}

export interface UpdateEvaluateSettingsPayload {
  startAt?: string | undefined;
  endAt?: string | undefined;
  responseEdit?: boolean | undefined;
  submitLimit?: number | undefined;
  isPrototype?: boolean | undefined;
}

/**
 * Get evaluate settings for a specific assessment
 * @param assessmentId - The ID of the assessment
 * @returns Promise containing evaluate settings
 */
export const getEvaluateSettingsApi = async (assessmentId: number): Promise<{ data: EvaluateSettings }> => {
  const response = await axios.get<EvaluateSettings>(`/assessments/${assessmentId}/evaluate-settings`);
  return { data: response.data };
};

/**
 * Update evaluate settings for a specific assessment
 * @param assessmentId - The ID of the assessment
 * @param settings - The settings to update
 * @returns Promise containing updated assessment
 */
export const updateEvaluateSettingsApi = async (
  assessmentId: number,
  settings: UpdateEvaluateSettingsPayload
): Promise<{ data: Assessment }> => {
  const response = await axios.patch<Assessment>(`/assessments/${assessmentId}/evaluate-settings`, settings);
  return { data: response.data };
};
