import { api as axios } from 'src/boot/axios';
import type { AssessmentType } from 'src/types/data';
import type { ItemBlock, ItemBlockType } from 'src/types/models';
import type { UpdateBlockPayload, UpdateSequenceResponse } from 'src/types/api';
import { triggerLoading, handleApiError } from 'src/utils/apiHelpers';
import { showError } from 'src/utils/notifications';

/**
 * Block API operations
 */

export interface CreateBlockPayload {
  assessmentId: number;
  type: ItemBlockType;
  sequence: number;
  section: number;
  isRequired: boolean;
}

export interface DuplicateBlockData {
  assessmentId: number;
  sequence?: number;
  section?: number;
}

/**
 * Create a new block
 */
export async function createBlock(
  block: CreateBlockPayload,
  assessmentType: AssessmentType,
): Promise<ItemBlock | undefined> {
  console.log('🚀 createBlock called with:', {
    type: assessmentType,
    block: block,
  });

  try {
    if (assessmentType === 'evaluate') {
      return await createBlockViaCorrectEndpoint(block);
    } else {
      // For quiz type, try the original endpoint first
      try {
        console.log('📍 Attempting to create block with quiz endpoint:', `/item-blocks/block`);
        const res = await axios.post<ItemBlock>(`/item-blocks/block`, block);
        triggerLoading();
        return res.data;
      } catch (error: unknown) {
        console.error('❌ Block creation failed:', error);

        if (error && typeof error === 'object' && 'response' in error) {
          const axiosError = error as { response?: { data?: unknown; status?: number } };
          console.error('Error response:', axiosError.response?.data);
          console.error('Error status:', axiosError.response?.status);

          if (axiosError.response?.status === 404) {
            return await createBlockFallback(block);
          } else {
            showError('ไม่สามารถสร้าง Block ได้');
          }
        } else {
          console.error('Full error:', error);
          showError('ไม่สามารถสร้าง Block ได้');
        }
        return;
      }
    }
  } catch (error) {
    handleApiError(error, 'สร้าง Block');
  }
}

/**
 * Update a block
 */
export async function updateBlock(block: UpdateBlockPayload): Promise<ItemBlock | undefined> {
  try {
    const updateEndpoint = `/item-blocks/${block.id}`;

    const sanitizedBlock: Partial<ItemBlock> = {
      id: block.id,
      ...(block.type && { type: block.type as ItemBlockType }),
      ...(block.isRequired !== undefined && { isRequired: block.isRequired }),
      ...(block.assessmentId !== undefined && { assessmentId: block.assessmentId }),
    };

    if (block.options?.length) {
      sanitizedBlock.options = block.options.map((opt, idx) => ({
        id: opt.id ?? 0,
        itemBlockId: opt.itemBlockId ?? block.id,
        optionText: opt.optionText ?? '',
        value: opt.value ?? 0,
        sequence: idx,
      }));
    }

    console.log('before send: ', block.type);

    const res = await axios.patch<ItemBlock>(updateEndpoint, sanitizedBlock);

    console.log('✅ Updated block from server:', res.data);

    triggerLoading();
    return res.data;
  } catch (error: unknown) {
    console.error('❌ Block update failed:', error);

    if (error && typeof error === 'object' && 'response' in error) {
      const axiosError = error as { response?: { data?: unknown; status?: number } };
      console.error('Update error response:', axiosError.response?.data);
      console.error('Update error status:', axiosError.response?.status);
    }

    showError('ไม่สามารถอัปเดต Block ได้');
    return;
  }
}

/**
 * Update block sequences
 */
export async function updateBlockSequences(
  blocks: ItemBlock[],
): Promise<UpdateSequenceResponse | undefined> {
  try {
    console.log('🔄 updateBlockSequences called with:', {
      blocksCount: blocks.length,
      blockIds: blocks.map((b) => b.id),
      sequences: blocks.map((b) => b.sequence),
    });

    // Validate blocks before preparing payload
    const validatedBlocks = blocks.filter((block) => {
      const isValid = block.id && !isNaN(Number(block.id)) && Number(block.id) > 0;
      if (!isValid) {
        console.error('❌ Invalid block detected in updateBlockSequences:', {
          blockId: block.id,
          blockType: block.type,
          sequence: block.sequence,
          idType: typeof block.id,
          isNaN: isNaN(Number(block.id)),
        });
      }
      return isValid;
    });

    if (validatedBlocks.length === 0) {
      console.error('❌ No valid blocks to update sequences');
      showError('ไม่มีรายการที่ถูกต้องสำหรับการอัปเดตลำดับ');
      return;
    }

    // Prepare the payload for the API
    const payload = {
      itemBlocks: validatedBlocks.map((block) => ({
        id: Number(block.id), // Ensure ID is a number
        sequence: Number(block.sequence), // Ensure sequence is a number
      })),
    };

    console.log('📤 Sending payload to backend:', {
      payload,
      originalBlocksCount: blocks.length,
      validatedBlocksCount: validatedBlocks.length,
      itemBlockIds: payload.itemBlocks.map((b) => b.id),
      itemBlockSequences: payload.itemBlocks.map((b) => b.sequence),
    });

    const res = await axios.patch<UpdateSequenceResponse>('/item-blocks/update/sequences', payload);

    console.log('✅ Block sequences updated successfully:', res.data);
    return res.data;
  } catch (error: unknown) {
    console.error('❌ Block sequence update failed:', error);

    if (error && typeof error === 'object' && 'response' in error) {
      const axiosError = error as { response?: { data?: unknown; status?: number } };
      console.error('Sequence update error response:', axiosError.response?.data);
      console.error('Sequence update error status:', axiosError.response?.status);
    }

    showError('ไม่สามารถอัปเดตลำดับได้');
    return;
  }
}

/**
 * Delete a block
 */
export async function deleteBlock(
  block: ItemBlock,
  assessmentType: AssessmentType,
): Promise<ItemBlock | undefined> {
  try {
    console.log('🗑️ deleteBlock called with:', {
      type: assessmentType,
      blockId: block.id,
      blockType: block.type,
      assessmentId: block.assessmentId,
      hasHeaderBody: !!block.headerBody,
      hasQuestions: !!block.questions?.length,
      hasOptions: !!block.options?.length,
      questionsCount: block.questions?.length || 0,
      optionsCount: block.options?.length || 0,
    });

    // Enhanced validation before deletion
    if (!block.id) {
      console.error('❌ Cannot delete block: Missing block ID');
      showError('ไม่สามารถลบได้ - ไม่พบ ID ของรายการ');
      return;
    }

    if (!block.assessmentId) {
      console.error('❌ Cannot delete block: Missing assessmentId');
      showError('ไม่สามารถลบได้ - ไม่พบ Assessment ID');
      return;
    }

    const deleteEndpoint = `/item-blocks/${block.id}`;

    console.log('🌐 Sending DELETE request to backend...', {
      endpoint: deleteEndpoint,
      blockId: block.id,
      blockType: block.type,
      timestamp: new Date().toISOString(),
    });

    const res = await axios.delete<ItemBlock>(deleteEndpoint);

    console.log('✅ Block deleted successfully from backend:', {
      deletedBlock: res.data,
      responseStatus: res.status,
      responseHeaders: res.headers,
      timestamp: new Date().toISOString(),
    });

    // Validate that the response contains the expected data
    if (!res.data?.id) {
      console.warn('⚠️ Backend response missing expected data:', res.data);
    } else if (res.data.id !== block.id) {
      console.warn('⚠️ Backend response ID mismatch:', {
        expectedId: block.id,
        receivedId: res.data.id,
      });
    }

    return res.data;
  } catch (error: unknown) {
    console.error('❌ Block deletion failed:', error);

    // Enhanced error logging
    if (error && typeof error === 'object' && 'response' in error) {
      const axiosError = error as {
        response?: { data?: unknown; status?: number; statusText?: string };
      };
      console.error('Delete error details:', {
        status: axiosError.response?.status,
        statusText: axiosError.response?.statusText,
        data: axiosError.response?.data,
        blockId: block.id,
        blockType: block.type,
        endpoint: `/item-blocks/${block.id}`,
      });

      // Provide more specific error messages based on status code
      if (axiosError.response?.status === 404) {
        showError('ไม่พบรายการที่ต้องการลบ - อาจถูกลบไปแล้ว');
      } else if (axiosError.response?.status === 403) {
        showError('ไม่มีสิทธิ์ในการลบรายการนี้');
      } else if (axiosError.response?.status === 500) {
        showError('เกิดข้อผิดพลาดในระบบ - กรุณาลองใหม่อีกครั้ง');
      } else {
        showError('ไม่สามารถลบ Block ได้');
      }
    } else {
      console.error('Delete error (non-axios):', error);
      showError('ไม่สามารถลบ Block ได้');
    }

    return;
  }
}

/**
 * Duplicate a block
 */
export async function duplicateBlock(
  sourceBlockId: number,
  duplicateData: DuplicateBlockData,
): Promise<ItemBlock | undefined> {
  try {
    const endpoint = `/item-blocks/${sourceBlockId}/duplicate`;
    const res = await axios.post<ItemBlock>(endpoint, duplicateData);

    triggerLoading();
    return res.data;
  } catch (error: unknown) {
    if (error && typeof error === 'object' && 'response' in error) {
      const axiosError = error as { response?: { data?: unknown; status?: number } };
      console.error('Duplication error response:', axiosError.response?.data);
      console.error('Duplication error status:', axiosError.response?.status);
    }

    showError('ไม่สามารถคัดลอก Block ได้');
    return;
  }
}

// Internal helper functions

async function createBlockViaCorrectEndpoint(
  block: CreateBlockPayload,
): Promise<ItemBlock | undefined> {
  try {
    const payload = {
      assessmentId: block.assessmentId,
      type: block.type || 'RADIO',
      sequence: block.sequence,
      section: block.section,
      isRequired: block.isRequired,
    };

    const res = await axios.post<ItemBlock>('/item-blocks/block', payload);
    triggerLoading();
    return res.data;
  } catch (error: unknown) {
    if (error && typeof error === 'object' && 'response' in error) {
      const axiosError = error as { response?: { data?: unknown; status?: number } };
      console.error('Error response from correct endpoint:', axiosError.response?.data);
      console.error('Error status from correct endpoint:', axiosError.response?.status);

      if (block.type === 'HEADER' && axiosError.response?.status === 500) {
        console.error('🚫 Header creation failed due to known backend bug - not trying fallbacks');
        throw error;
      }
    }

    if (block.type !== 'HEADER') {
      return await createBlockFallback(block);
    } else {
      throw error;
    }
  }
}

async function createBlockFallback(block: CreateBlockPayload): Promise<ItemBlock | undefined> {
  // Try simple evaluate endpoint
  try {
    const payload = {
      assessmentId: block.assessmentId,
      type: block.type || 'RADIO',
      sequence: block.sequence,
      section: block.section,
      isRequired: block.isRequired,
    };

    const res = await axios.post<ItemBlock>('/evaluate/item-blocks', payload);
    triggerLoading();
    return res.data;
  } catch (error) {
    console.error('Simple evaluate endpoint failed:', error);
  }

  // Try other alternative endpoints
  const alternatives = [
    `/evaluate/item-blocks`,
    `/item-blocks`,
    `/evaluate/item-blocks/create`,
    `/evaluate/blocks`,
    `/evaluate/blocks/create`,
  ];

  for (const endpoint of alternatives) {
    try {
      const res = await axios.post<ItemBlock>(endpoint, block);
      triggerLoading();
      return res.data;
    } catch {
      continue;
    }
  }

  showError(`ไม่พบ endpoint ที่ใช้งานได้สำหรับการสร้าง Block - กรุณาตรวจสอบ backend API`);
  return undefined;
}
