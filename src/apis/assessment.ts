import { api as axios } from 'src/boot/axios';
import type { AxiosResponse, AxiosError } from 'axios';
import type { QTableProps } from 'quasar';
import type { AssessmentQueryParams, AssessmentType, DataResponse } from 'src/types/data';
import type { Assessment, ItemBlock, HeaderBody, Question, Option } from 'src/types/models';
import type { AssessmentFetchResponse } from 'src/types/api';
import { formatParams } from 'src/utils/formatter';
import { getAssessmentErrorMessage } from 'src/utils/assessmentErrors';
import { showError } from 'src/utils/notifications';
import { apiCallWithData, handleApiSuccess } from 'src/utils/apiHelpers';

// Assessment API functions
export const fetchAssessmentsApi = async (
  path: string,
  type: AssessmentType,
  pag: QTableProps['pagination'],
  search?: string,
): Promise<AxiosResponse<DataResponse<Assessment>>> => {
  const format = formatParams(pag, search);
  const params = { ...format, type } as AssessmentQueryParams;
  return axios.get<DataResponse<Assessment>>(path, { params });
};

export const fetchAssessmentsForViewApi = async (
  path: string,
  type: AssessmentType,
  pag: QTableProps['pagination'],
  search?: string,
): Promise<AxiosResponse<DataResponse<Assessment>>> => {
  const format = formatParams(pag, search);
  const params = { ...format, type } as AssessmentQueryParams;
  return axios.get<DataResponse<Assessment>>(`${path}/standardUser/view-assessment`, { params });
};

export const fetchAssessmentApi = async (
  path: string,
  id: number,
  query?: { page?: number; limit?: number },
): Promise<AxiosResponse<{ assessment: Assessment; pagedItemBlocks: ItemBlock[] }>> => {
  return axios.get<{ assessment: Assessment; pagedItemBlocks: ItemBlock[] }>(`${path}/${id}`, {
    params: query,
  });
};

export const getAssessmentByUUIDApi = async (
  path: string,
  id: string,
): Promise<AxiosResponse<Assessment>> => {
  return axios.get<Assessment>(`${path}/url/${id}`);
};

export const createAssessmentApi = async (
  path: string,
  assessment: Partial<Assessment>,
): Promise<AxiosResponse<Assessment>> => {
  return axios.post<Assessment>(path, assessment);
};

export const updateAssessmentApi = async (
  path: string,
  id: number,
  assessment: Partial<Assessment>,
): Promise<AxiosResponse<Assessment>> => {
  return axios.patch<Assessment>(`${path}/${id}`, assessment);
};

export const duplicateAssessmentApi = async (
  path: string,
  sourceId: number,
  targetId: number,
): Promise<AxiosResponse<Assessment>> => {
  return axios.patch<Assessment>(`${path}/${sourceId}/copy-to/${targetId}`);
};

export const deleteAssessmentApi = async (
  path: string,
  id: number,
): Promise<AxiosResponse<Assessment>> => {
  return axios.delete<Assessment>(`${path}/${id}`);
};

// Item Block API functions
export const createItemBlockApi = async (
  block: Omit<ItemBlock, 'id'>,
): Promise<AxiosResponse<ItemBlock>> => {
  return axios.post<ItemBlock>('/item-blocks/block', block);
};

export const createItemBlockEvaluateApi = async (
  block: Omit<ItemBlock, 'id'>,
): Promise<AxiosResponse<ItemBlock>> => {
  const payload = {
    assessmentId: block.assessmentId,
    type: block.type || 'RADIO',
    sequence: block.sequence,
    section: block.section,
    isRequired: block.isRequired,
  };
  return axios.post<ItemBlock>('/evaluate/item-blocks', payload);
};

export const updateItemBlockApi = async (
  id: number,
  block: {
    id: number;
    type: string;
    isRequired?: boolean;
    assessmentId?: number;
    options?: Option[];
  },
): Promise<AxiosResponse<ItemBlock>> => {
  return axios.patch<ItemBlock>(`/item-blocks/${id}`, block);
};

export const updateItemBlockSequencesApi = async (
  blocks: { id: number; sequence: number }[],
): Promise<
  AxiosResponse<{ success: boolean; message: string; data: { id: number; sequence: number }[] }>
> => {
  return axios.patch('/item-blocks/sequences', { sequences: blocks });
};

export const deleteItemBlockApi = async (id: number): Promise<AxiosResponse<ItemBlock>> => {
  return axios.delete<ItemBlock>(`/item-blocks/${id}`);
};

// Header Body API functions
export const fetchHeaderBodyApi = async (
  headerBodyPath: string,
  id: number,
): Promise<AxiosResponse<HeaderBody>> => {
  return axios.get<HeaderBody>(`${headerBodyPath}/${id}`);
};

export const createHeaderBodyApi = async (
  headerBodyPath: string,
  headerBody: Partial<HeaderBody>,
): Promise<AxiosResponse<HeaderBody>> => {
  return axios.post<HeaderBody>(headerBodyPath, headerBody);
};

export const deleteHeaderBodyApi = async (
  headerBodyPath: string,
  id: number,
): Promise<AxiosResponse<HeaderBody>> => {
  return axios.delete<HeaderBody>(`${headerBodyPath}/${id}`);
};

// Question API functions
export const updateQuestionApi = async (
  id: number,
  question: Partial<Question>,
): Promise<AxiosResponse<Question>> => {
  return axios.patch<Question>(`/questions/${id}`, question);
};

export const updateQuestionLegacyApi = async (
  itemBlockId: number,
  itemBlockType: string,
  question: Partial<Question>,
): Promise<AxiosResponse<Question>> => {
  return axios.patch<Question>(`/item-blocks/${itemBlockId}`, {
    id: itemBlockId,
    type: itemBlockType,
    questionText: question.questionText,
  });
};

// Option API functions
export const updateOptionApi = async (
  itemBlockId: number,
  itemBlockType: string,
  option: Partial<Option>,
): Promise<AxiosResponse<Option>> => {
  return axios.patch<Option>(`/item-blocks/${itemBlockId}`, {
    id: itemBlockId,
    type: itemBlockType,
    optionText: option.optionText,
    score: option.value,
  });
};

// Quiz specific API functions
export const getQuizHeaderApi = async (linkUrl: string): Promise<AxiosResponse<Assessment>> => {
  return axios.get<Assessment>(`/assessments/header/url/${linkUrl}`);
};

export const getQuizByLinkUrlApi = async (linkUrl: string): Promise<AxiosResponse<Assessment>> => {
  return axios.get<Assessment>('/assessments/by-link', {
    params: { linkUrl },
  });
};

// ===========================
// BUSINESS LOGIC LAYER
// High-level functions with error handling and notifications
// ===========================

const ASSESSMENT_PATH = '/assessments';

/**
 * Assessment CRUD operations with error handling
 */

export async function fetchAssessments(
  type: AssessmentType,
  pagination: QTableProps['pagination'],
  search?: string
) {
  return apiCallWithData(
    () => fetchAssessmentsApi(ASSESSMENT_PATH, type, pagination, search),
    'โหลด'
  );
}

export async function fetchAssessmentsForView(
  type: AssessmentType,
  pagination: QTableProps['pagination'],
  search?: string
) {
  return apiCallWithData(
    () => fetchAssessmentsForViewApi(ASSESSMENT_PATH, type, pagination, search),
    'โหลด'
  );
}

export async function fetchAssessment(
  id: number,
  query?: { page?: number; limit?: number }
): Promise<AssessmentFetchResponse> {
  return apiCallWithData(
    () => fetchAssessmentApi(ASSESSMENT_PATH, id, query),
    'โหลด'
  );
}

export async function getAssessmentByUUID(id: string): Promise<{ data: Assessment }> {
  try {
    const response = await getAssessmentByUUIDApi(ASSESSMENT_PATH, id);
    return response;
  } catch (error) {
    const axiosError = error as AxiosError;
    const errorMessage = getAssessmentErrorMessage(axiosError, 'โหลด');
    showError(errorMessage);
    throw error;
  }
}

export async function createAssessment(assessment: Partial<Assessment>) {
  const result = await apiCallWithData(
    () => createAssessmentApi(ASSESSMENT_PATH, assessment),
    'สร้าง'
  );
  handleApiSuccess('สร้างแบบประเมินสำเร็จ');
  return result;
}

export async function updateAssessment(id: number, assessment: Partial<Assessment>) {
  const result = await apiCallWithData(
    () => updateAssessmentApi(ASSESSMENT_PATH, id, assessment),
    'อัปเดต'
  );
  handleApiSuccess('อัปเดตแบบประเมินสำเร็จ');
  return result;
}

export async function duplicateAssessment(sourceId: number, targetId: number) {
  const result = await apiCallWithData(
    () => duplicateAssessmentApi(ASSESSMENT_PATH, sourceId, targetId),
    'คัดลอก'
  );
  handleApiSuccess('คัดลอกแบบประเมินสำเร็จ');
  return result;
}

export async function deleteAssessment(id: number) {
  const result = await apiCallWithData(
    () => deleteAssessmentApi(ASSESSMENT_PATH, id),
    'ลบ'
  );
  handleApiSuccess('ลบแบบประเมินสำเร็จ');
  return result;
}
