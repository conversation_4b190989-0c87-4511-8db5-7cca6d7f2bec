import { api as axios } from 'src/boot/axios';
import type { HeaderBody, Question, Option, Assessment } from 'src/types/models';
import { triggerLoading } from 'src/utils/apiHelpers';
import { showError } from 'src/utils/notifications';

/**
 * Header Body API operations
 */

const HEADER_BODY_PATH = '/header-bodies';

export async function fetchHeaderBody(id: number): Promise<HeaderBody | undefined> {
  try {
    const res = await axios.get<HeaderBody>(`${HEADER_BODY_PATH}/${id}`);
    return res.data;
  } catch {
    showError('ไม่สามารถโหลดข้อมูล Header Body ได้');
    return;
  }
}

export async function createHeaderBody(headerBody: Partial<HeaderBody>): Promise<HeaderBody | undefined> {
  try {
    const res = await axios.post<HeaderBody>(HEADER_BODY_PATH, headerBody);
    triggerLoading();
    return res.data;
  } catch {
    showError('ไม่สามารถสร้าง Header Body ได้');
    return;
  }
}

export async function updateHeaderBody(
  id: number,
  headerBody: Partial<HeaderBody>
): Promise<HeaderBody | undefined> {
  try {
    const res = await axios.patch<HeaderBody>(`${HEADER_BODY_PATH}/${id}`, headerBody);
    triggerLoading();
    return res.data;
  } catch {
    showError('ไม่สามารถอัปเดต Header Body ได้');
    return;
  }
}

export async function deleteHeaderBody(id: number): Promise<HeaderBody | undefined> {
  try {
    const res = await axios.delete<HeaderBody>(`${HEADER_BODY_PATH}/${id}`);
    return res.data;
  } catch {
    showError('ไม่สามารถลบ Header Body ได้');
    return;
  }
}

/**
 * Question API operations
 */

export async function updateQuestion(id: number, question: Partial<Question>): Promise<Question | undefined> {
  try {
    const res = await axios.patch<Question>(`/questions/${id}`, question);
    triggerLoading();
    return res.data;
  } catch {
    showError('ไม่สามารถอัปเดตคำถามได้');
    return;
  }
}

export async function updateQuestionLegacy(
  itemBlockId: number,
  itemBlockType: string,
  question: Partial<Question>
): Promise<Question | undefined> {
  try {
    const res = await axios.patch<Question>(`/item-blocks/${itemBlockId}`, {
      id: itemBlockId,
      type: itemBlockType,
      questionText: question.questionText,
    });
    triggerLoading();
    return res.data;
  } catch {
    showError('ไม่สามารถอัปเดตคำถามได้');
    return;
  }
}

/**
 * Option API operations
 */

export async function updateOption(
  itemBlockId: number,
  itemBlockType: string,
  option: Partial<Option>
): Promise<Option | undefined> {
  try {
    const res = await axios.patch<Option>(`/item-blocks/${itemBlockId}`, {
      id: itemBlockId,
      type: itemBlockType,
      optionText: option.optionText,
      score: option.value,
    });
    triggerLoading();
    return res.data;
  } catch {
    showError('ไม่สามารถอัปเดตตัวเลือกได้');
    return;
  }
}

/**
 * Quiz specific API operations
 */

export async function getQuizHeader(linkUrl: string): Promise<Assessment> {
  try {
    const res = await axios.get<Assessment>(`/assessments/header/url/${linkUrl}`);
    return res.data;
  } catch (error) {
    console.error(`Error fetching quiz header for assessmentId ${linkUrl}:`, error);
    throw error;
  }
}

export async function getQuizByLinkUrl(linkUrl: string): Promise<Assessment> {
  try {
    const res = await axios.get<Assessment>(`/assessments/by-link`, {
      params: { linkUrl },
    });
    return res.data;
  } catch (error) {
    console.error(`Error fetching quiz by linkUrl ${linkUrl}:`, error);
    throw error;
  }
}
