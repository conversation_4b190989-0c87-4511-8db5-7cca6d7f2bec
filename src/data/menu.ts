import type { QTabProps } from 'quasar';
import type { MenuLink } from 'src/types/app';

export const allDrawerMenu: MenuLink[] = [
  {
    title: 'หน้าหลัก',
    link: '/home',
    icon: 'home',
  },
  {
    title: 'ติดตามผล',
    link: '/monitor',
    icon: 'preview',
  },
  {
    title: 'จัดการ',
    icon: 'settings',
    children: [
      {
        title: 'ผู้ใช้งาน',
        link: '/ums/management',
        icon: 'group',
      },
      {
        title: 'แบบทดสอบ',
        link: '/quiz/management',
        icon: 'quiz',
      },
      {
        title: 'แบบประเมิน',
        link: '/evaluate/management',
        icon: 'library_books',
      },
      {
        title: 'สมรรถนะ',
        link: '/competency/management',
        icon: 'mdi-brain',
      },
      {
        title: 'ทักษะ',
        link: '/skill/management',
        icon: 'code',
      },
      {
        title: 'In House Training',
        link: '/in-house/management',
        icon: 'mdi-bag-personal',
      },
    ],
  },
  {
    title: 'ทำแบบทดสอบ/ประเมิน',
    icon: 'mdi-clipboard-list',
    children: [
      {
        title: 'ทำแบบทดสอบ',
        link: '/personal/my-quiz/',
        icon: 'app:test-quiz',
      },
      {
        title: 'ทำแบบประเมิน',
        link: '/personal/my-evaluate',
        icon: 'mdi-application-edit',
      },
    ],
  },
  {
    title: 'แผนพัฒนา',
    icon: 'mdi-book-open-variant',
    children: [
      {
        title: 'อายุการปฏิบัติงาน',
        link: '/idp/age-work-criteria',
        icon: 'support_agent',
      },
      {
        title: 'จัดการแผนพัฒนา',
        link: '/idp/dev-management',
        icon: 'mdi-book-edit',
      },
      {
        title: 'จัดการแผนพัฒนารายบุคคล',
        link: '/idp/idp-management',
        icon: 'mdi-account-tie',
      },
      {
        title: 'แผนพัฒนาของฉัน',
        link: '/personal/my-plan',
        icon: 'mdi-star',
      },
    ],
  },
];

export const managerMenu: MenuLink[] = [
  {
    title: 'หน้าหลัก',
    link: '/home',
    icon: 'home',
  },
  {
    title: 'จัดการแบบทดสอบ',
    link: '/quiz/management',
    icon: 'quiz',
  },
  {
    title: 'จัดการแบบประเมิน',
    link: '/evaluate/management',
    icon: 'library_books',
  },
];

export const standardUserMenu: MenuLink[] = [
  {
    title: 'หน้าหลัก',
    link: '/home',
    icon: 'home',
  },
  {
    title: 'ทำแบบทดสอบ',
    link: '/personal/my-quiz/',
    icon: 'app:test-quiz',
  },
  {
    title: 'ทำแบบประเมิน',
    link: '/personal/my-evaluate',
    icon: 'mdi-application-edit',
  },
];

export const LogOutMenu: MenuLink[] = [
  {
    title: 'ออกจากระบบ',
    link: 'login',
    icon: 'logout',
  },
];

export const defaultAsmTabsMenu: QTabProps[] = [
  {
    label: 'คำถาม',
    name: 'questions',
    icon: 'help',
  },
  {
    label: 'การตอบ',
    name: 'replies',
    icon: 'reply',
  },
  {
    label: 'ตั้งค่า',
    name: 'settings',
    icon: 'settings',
  },
];

export const defaultUmsTabsMenu: QTabProps[] = [
  {
    label: 'บทบาท',
    name: 'roles',
  },
  {
    label: 'ผู้ใช้งาน',
    name: 'users',
  },
];
